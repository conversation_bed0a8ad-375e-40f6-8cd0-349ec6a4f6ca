using System;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    /// <summary>
    /// 退票审核表 - 存储退票处理和审核过程中的信息
    /// </summary>
    [SugarTable("crm_invoice_refund_review")]
    public class Db_crm_invoice_refund_review
    {
        /// <summary>
        /// 审核ID
        /// </summary>
        [SugarColumn(IsPrimaryKey=true)]
        public string Id { get; set; }
        
        /// <summary>
        /// 关联的退票申请ID
        /// </summary>
        public string RefundApplicationId { get; set; }
        
        /// <summary>
        /// 原发票ID（被退的发票）
        /// </summary>
        public string InvoiceId { get; set; }
        
        /// <summary>
        /// 红字发票ID（退票成功后创建的发票）
        /// </summary>
        public string RedInvoiceId { get; set; }
        
        /// <summary>
        /// 合同ID
        /// </summary>
        public string ContractId { get; set; }
        
        /// <summary>
        /// 退票号码 (OCR识别)
        /// </summary>
        public string RefundNumber { get; set; }
        
        /// <summary>
        /// 退票日期 (OCR识别)
        /// </summary>
        public DateTime? RefundDate { get; set; }
        
        /// <summary>
        /// 退票金额 (OCR识别)
        /// </summary>
        public decimal RefundAmount { get; set; }

        /// <summary>
        /// 发票类型
        /// </summary>  
        public int? InvoiceType { get; set; }
        
        /// <summary>
        /// 审核状态
        /// </summary>
        public int AuditStatus { get; set; }
        
        /// <summary>
        /// 处理人ID
        /// </summary>
        public string ProcessorId { get; set; }
        
        /// <summary>
        /// 处理时间
        /// </summary>
        public DateTime? ProcessTime { get; set; }
        
        /// <summary>
        /// 审核人ID
        /// </summary>
        public string AuditorId { get; set; }
        
        /// <summary>
        /// 审核时间
        /// </summary>
        public DateTime? AuditTime { get; set; }
        
        /// <summary>
        /// 审核反馈
        /// </summary>
        public string AuditFeedback { get; set; }
        
        /// <summary>
        /// 经办人备注
        /// </summary>
        public string ProcessorRemark { get; set; }
        
        /// <summary>
        /// 修改的字段(JSON格式)
        /// </summary>
        public string ModifiedFields { get; set; }
        
        /// <summary>
        /// 删除标记
        /// </summary>
        public bool? Deleted { get; set; }
        
        /// <summary>
        /// 创建人
        /// </summary>
        public string CreateUser { get; set; }
        
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateDate { get; set; }
        
        /// <summary>
        /// 修改人
        /// </summary>
        public string UpdateUser { get; set; }
        
        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateDate { get; set; }
    }
} 