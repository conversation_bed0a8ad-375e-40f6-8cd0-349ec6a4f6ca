using System;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    /// <summary>
    /// 退票申请表 - 存储客户经理申请退票的信息
    /// </summary>
    [SugarTable("crm_invoice_refund_application")]
    public class Db_crm_invoice_refund_application
    {
        /// <summary>
        /// 申请ID
        /// </summary>
        [SugarColumn(IsPrimaryKey=true)]
        public string Id { get; set; }
        
        /// <summary>
        /// 原发票ID（被退的发票）
        /// </summary>
        public string InvoiceId { get; set; }
        
        /// <summary>
        /// 红字发票ID（退票成功后创建的发票）
        /// </summary>
        public string RedInvoiceId { get; set; }
        
        /// <summary>
        /// 合同ID
        /// </summary>
        public string ContractId { get; set; }
        
        /// <summary>
        /// 退票金额
        /// </summary>
        public decimal RefundAmount { get; set; }
        
        /// <summary>
        /// 退票原因
        /// </summary>
        public string RefundReason { get; set; }
        
        /// <summary>
        /// 申请状态
        /// </summary>
        public int AuditStatus { get; set; }
        
        /// <summary>
        /// 申请人ID
        /// </summary>
        public string ApplicantId { get; set; }
        
        /// <summary>
        /// 申请时间
        /// </summary>
        public DateTime? ApplyTime { get; set; }
        
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
        
        /// <summary>
        /// 删除标记
        /// </summary>
        public bool? Deleted { get; set; }
        
        /// <summary>
        /// 创建人
        /// </summary>
        public string CreateUser { get; set; }
        
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateDate { get; set; }
        
        /// <summary>
        /// 修改人
        /// </summary>
        public string UpdateUser { get; set; }
        
        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateDate { get; set; }

        /// <summary>
        /// 催票标记 (0=未催票, 1=已催票)
        /// </summary>
        public int? IsReminded { get; set; }
        
        /// <summary>
        /// 最近催票时间
        /// </summary>
        public DateTime? RemindedDate { get; set; }
        
        /// <summary>
        /// 催票用户ID
        /// </summary>
        public string RemindedUser { get; set; }

        /// <summary>
        /// 退票后台备注
        /// </summary>
        public string RefundBackgroundRemark { get; set; }
    }
} 