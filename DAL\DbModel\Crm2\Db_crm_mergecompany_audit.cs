﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///
    ///</summary>
    [SugarTable("crm_mergecompany_audit")]
    public partial class Db_crm_mergecompany_audit
    {
           public Db_crm_mergecompany_audit(){


           }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string Id {get;set;}

           /// <summary>
           /// Desc:申请人id
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string ApplicantId {get;set;}
        /// <summary>
        /// Desc:^客户id
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string CustomerId { get; set; }
        

           /// <summary>
           /// Desc:反馈意见
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? ApplicantDate {get;set;}

        /// <summary>
        /// Desc:反馈意见
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string Feedback {get;set; }
        /// <summary>
        /// Desc:关系说明
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string RelationshipDescription { get; set; }
        

           /// <summary>
           /// Desc:状态（0：提交待审核: 1：通过: 2：拒绝）
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public int State {get;set;}

           /// <summary>
           /// Desc:审核人id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ReviewerId {get;set;}

           /// <summary>
           /// Desc:审核时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? ReviewerDate {get;set;}

           /// <summary>
           /// Desc:
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public int Deleted {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CreateUser {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? CreateDate {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UpdateUser {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? UpdateDate {get;set;}

    }
}
