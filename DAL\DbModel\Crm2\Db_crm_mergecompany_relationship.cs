﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///
    ///</summary>
    [SugarTable("crm_mergecompany_relationship")]
    public partial class Db_crm_mergecompany_relationship
    {
           public Db_crm_mergecompany_relationship(){


           }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string Id {get;set;}

           /// <summary>
           /// Desc:客户表id
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string MainCustomerId {get;set;}

           /// <summary>
           /// Desc:子客户表id
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string SubCustomerId {get;set;}
        /// <summary>
        /// Desc:子客户（原）主公司id
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string SubCustomerMainCompanyId { get; set; }

           /// <summary>
           /// Desc:是否有效
           /// Default:1
           /// Nullable:False
           /// </summary>           
           public int IsValid {get;set;}

           /// <summary>
           /// Desc:无效的时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? InvalidDate {get;set;}
        /// <summary>
        /// Desc:审核状态（0：提交待审核: 1：通过: 2：拒绝）
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int State { get; set; }
           /// <summary>
           /// Desc:合并公司审核表id
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string MergeCompanyAuditId {get;set;}

           /// <summary>
           /// Desc:合并公司审核表公司列表id
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string MergeCompanyAuditCompanyListId {get;set;}
        /// <summary>
        /// Desc:关联的拆分表Id
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string SplitCustomerAuditId { get; set; }
        

           /// <summary>
           /// Desc:
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public int Deleted {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CreateUser {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? CreateDate {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UpdateUser {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? UpdateDate {get;set;}

    }
}
