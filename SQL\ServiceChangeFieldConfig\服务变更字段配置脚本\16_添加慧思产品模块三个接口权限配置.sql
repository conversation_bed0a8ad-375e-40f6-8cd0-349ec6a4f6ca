-- 为慧思产品模块下添加三个新接口权限配置
-- 执行日期: 2025-01-29

-- ================================
-- 变量定义
-- ================================
SET @now = NOW();
SET @userId = 'sys'; -- 创建用户

-- ================================
-- 1. 为合同管理-服务管理-慧思产品模块添加GetAvailableServiceChangeReasons接口权限
-- ================================
-- 使用提供的慧思产品模块ID
SET @huisiProductId = '1046ec76-65fc-11f0-8d7d-c025a58cf040';

-- 检查慧思产品模块是否存在并插入GetAvailableServiceChangeReasons权限配置
INSERT INTO sys_form (`Id`, `Name`, `Describe`, `Type`, `ParentId`, `Title`, `OrderNum`, `ControllerName`, `MethodName`, `LogTemplate`, `Deleted`, `CreateUser`, `CreateDate`)
SELECT 
    UUID(), 
    'GetAvailableServiceChangeReasons', 
    '合同管理-服务管理-慧思产品-GetAvailableServiceChangeReasons', 
    5, 
    @huisiProductId, 
    'GetAvailableServiceChangeReasons', 
    2, 
    'ServiceChangeFieldPermission', 
    'GetAvailableServiceChangeReasons', 
    NULL, 
    0, 
    @userId, 
    @now
WHERE EXISTS (SELECT 1 FROM sys_form WHERE Id = @huisiProductId AND Deleted = 0)
  AND NOT EXISTS (
    SELECT 1 FROM sys_form 
    WHERE MethodName = 'GetAvailableServiceChangeReasons' 
      AND ControllerName = 'ServiceChangeFieldPermission' 
      AND ParentId = @huisiProductId 
      AND Deleted = 0
  );

-- ================================
-- 2. 为合同管理-服务管理-慧思产品模块添加GetApplyFieldPermissions接口权限
-- ================================
-- 插入GetApplyFieldPermissions权限配置
INSERT INTO sys_form (`Id`, `Name`, `Describe`, `Type`, `ParentId`, `Title`, `OrderNum`, `ControllerName`, `MethodName`, `LogTemplate`, `Deleted`, `CreateUser`, `CreateDate`)
SELECT 
    UUID(), 
    'GetApplyFieldPermissions', 
    '合同管理-服务管理-慧思产品-GetApplyFieldPermissions', 
    5, 
    @huisiProductId, 
    'GetApplyFieldPermissions', 
    3, 
    'ServiceChangeFieldPermission', 
    'GetApplyFieldPermissions', 
    NULL, 
    0, 
    @userId, 
    @now
WHERE EXISTS (SELECT 1 FROM sys_form WHERE Id = @huisiProductId AND Deleted = 0)
  AND NOT EXISTS (
    SELECT 1 FROM sys_form 
    WHERE MethodName = 'GetApplyFieldPermissions' 
      AND ControllerName = 'ServiceChangeFieldPermission' 
      AND ParentId = @huisiProductId 
      AND Deleted = 0
  );

-- ================================
-- 3. 为合同管理-服务管理-慧思产品模块添加GetAuditFieldPermissions接口权限
-- ================================
-- 插入GetAuditFieldPermissions权限配置
INSERT INTO sys_form (`Id`, `Name`, `Describe`, `Type`, `ParentId`, `Title`, `OrderNum`, `ControllerName`, `MethodName`, `LogTemplate`, `Deleted`, `CreateUser`, `CreateDate`)
SELECT 
    UUID(), 
    'GetAuditFieldPermissions', 
    '合同管理-服务管理-慧思产品-GetAuditFieldPermissions', 
    5, 
    @huisiProductId, 
    'GetAuditFieldPermissions', 
    4, 
    'ServiceChangeFieldPermission', 
    'GetAuditFieldPermissions', 
    NULL, 
    0, 
    @userId, 
    @now
WHERE EXISTS (SELECT 1 FROM sys_form WHERE Id = @huisiProductId AND Deleted = 0)
  AND NOT EXISTS (
    SELECT 1 FROM sys_form 
    WHERE MethodName = 'GetAuditFieldPermissions' 
      AND ControllerName = 'ServiceChangeFieldPermission' 
      AND ParentId = @huisiProductId 
      AND Deleted = 0
  );

-- 显示慧思产品模块配置结果
SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM sys_form WHERE Id = @huisiProductId AND Deleted = 0) 
        THEN CONCAT('慧思产品模块权限配置成功，模块ID: ', @huisiProductId)
        ELSE '警告：未找到慧思产品模块，请检查模块ID是否正确'
    END AS message;

-- ================================
-- 验证配置结果
-- ================================
SELECT '慧思产品模块三个新接口权限配置完成！' AS message;

-- 显示添加的权限配置
SELECT 
    f.Id,
    f.Name,
    f.Describe,
    f.Title,
    f.ControllerName,
    f.MethodName,
    f.ParentId,
    p.Title as ParentTitle,
    f.OrderNum,
    f.CreateUser,
    f.CreateDate
FROM sys_form f
LEFT JOIN sys_form p ON f.ParentId = p.Id
WHERE f.MethodName IN ('GetAvailableServiceChangeReasons', 'GetApplyFieldPermissions', 'GetAuditFieldPermissions')
  AND f.ControllerName = 'ServiceChangeFieldPermission'
  AND f.ParentId = @huisiProductId
  AND f.Deleted = 0
ORDER BY f.OrderNum ASC;

-- 显示慧思产品模块下的所有权限（包括新增的三个接口）
SELECT 
    f.Id,
    f.Name,
    f.Describe,
    f.Title,
    f.ControllerName,
    f.MethodName,
    f.OrderNum,
    f.CreateUser,
    f.CreateDate
FROM sys_form f
WHERE f.ParentId = @huisiProductId
  AND f.Deleted = 0
ORDER BY f.OrderNum ASC;
