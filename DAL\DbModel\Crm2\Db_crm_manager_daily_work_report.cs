﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///工作管理日报表
    ///</summary>
    [SugarTable("crm_manager_daily_work_report")]
    public class Db_crm_manager_daily_work_report
    {
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string Id {get;set;}

           /// <summary>
           /// Desc:所有者Id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UserId {get;set;}

           /// <summary>
           /// Desc:当日晨夕会与其他沟通摘要
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CommunicationSummary {get;set;}

           /// <summary>
           /// Desc:当日对员工赋能以及复盘摘要
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ResumptionSummary {get;set;}

           /// <summary>
           /// Desc:当日对员工的工作考核摘要
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string AppraisalSummary {get;set;}

           /// <summary>
           /// Desc:团队遇到的问题及挑战
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string Problems {get;set;}

           /// <summary>
           /// Desc:团队需要的支持与协助
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string Support {get;set;}

           /// <summary>
           /// Desc:个人本日销售业绩
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? User_SalesAchivement {get;set;}

           /// <summary>
           /// Desc:团队本日销售业绩
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? Org_SalesAchivement {get;set;}

           /// <summary>
           /// Desc:团队月销售业绩
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? Org_SalesAchivement_Month {get;set;}

           /// <summary>
           /// Desc:团队月销售目标/合同目标
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? Org_SalesAchivementTarget_Month {get;set;}

           /// <summary>
           /// Desc:团队合同金额
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? Org_ContractAmount_Month {get;set;}

           /// <summary>
           /// Desc:本月未到账用户
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? Org_ToBeRecivedCustomer_Month {get;set;}

           /// <summary>
           /// Desc:本月未到账金额
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? Org_ToBeRecivedAmount_Month {get;set;}

           /// <summary>
           /// Desc:状态（浏览状态、提交状态）
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? State {get;set;}

           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:True
           /// </summary>           
           public bool? Deleted {get;set;}

           /// <summary>
           /// Desc:创建人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CreateUser {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? CreateDate {get;set;}

           /// <summary>
           /// Desc:修改人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UpdateUser {get;set;}

           /// <summary>
           /// Desc:更新时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? UpdateDate {get;set;}

    }
}
