﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///
    ///</summary>
    [SugarTable("crm_mergecompany_audit_companylist")]
    public partial class Db_crm_mergecompany_audit_companylist
    {
           public Db_crm_mergecompany_audit_companylist(){


           }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string Id {get;set;}

           /// <summary>
           /// Desc:拆分公司审核表id
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string MergeCompanyAuditId {get;set;}

           /// <summary>
           /// Desc:客户表id
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string CustomerId {get;set;}

           /// <summary>
           /// Desc:设置哪个是主客户（已知主客户就会获取到它的主公司，然后将这个公司设置为主公司）主客户：1；子客户：0；
           /// Default:1
           /// Nullable:False
           /// </summary>           
           public int IsMainCustomer {get;set;}

           /// <summary>
           /// Desc:
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public int Deleted {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CreateUser {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? CreateDate {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UpdateUser {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? UpdateDate {get;set;}

    }
}
