{
  "Port": 6001,
  //启动环境 0：开发环境   1：生产环境  2：联调测试环境
  "Env": 0,
  //连接字符串
  "ConnectionStrings": {
    //"CRM2": "Server=*************;Database=crm2;Uid=crm2_kaifa;pwd=*******************",
    //"CRM2": "4B+zDDqdDuZGm9FuMXcX+ydUnq98BpOJGGvZJBtS2A0chFeivULty+BcFOSoi81beSP9aBnQJUJzGZpXnCYjvmKMumYSGrXMhkdDfFekuig=", //134
    //"CRM2": "4B+zDDqdDuZGm9FuMXcX+ydUnq98BpOJGGvZJBtS2A33ZIssd3kTQaWnYcuCVZxwn9j2YKSrm8zjIgXvV04c/BeW0PFwPWObJABM3zgoq7To5EmzxBpJwg==", //134生产环境数据库
    //"CRM2": "uXQKIF3548bNubx23P0sqVn7Y+LnNOizLq17vxvaEOOPzCh3EvICJD9SvTQZlP81rMjIOpdUwbOMv6KkW5uzv1lWVm+//xT9/O6ZrJYceqHgv3sRRfzE3w==",
    //"CRM2": "4B+zDDqdDuZGm9FuMXcX+/9syPlnp+V+WFQVS3CnYWzAooq21BZHeb/DrPOmuEz2vMaY88+RS0X/+HW6oSJgl+zX53B6K3OZRn8JpPM8XAc=",//228
    //"CRM2": "MgQQdGPJeYAsPe1FmJXpNWjyGxISyMJ18I4+06RtlRnW62+7Kf+g8UohFZ18CpOUurbDq2TzXekflXDUOUtYjmwvYFSL/O4T7dHeibiIgtXQXuS//KNRzA==", //正式环境,
    //"CRM2": "MgQQdGPJeYAsPe1FmJXpNWjyGxISyMJ18I4+06RtlRnW62+7Kf+g8Vvse4tP6+a2tkLIsPtbBGs9TiVqSn4X6EapsPmljzxb1adI/Lh3Bvk=",
    //"CRM2": "MgQQdGPJeYAsPe1FmJXpNWjyGxISyMJ18I4+06RtlRnW62+7Kf+g8Vvse4tP6+a2Nbc3SxHk2s2cqON8CmJzQ7mjCi/2XGxOEBa0oEF1nSFE65ZFTomIYQ==",
    //"CRM2": "4B+zDDqdDuZGm9FuMXcX+5nODL2tygGb0gmJDeOFbIYdx+HP28qY375jUlSDavsWwRKSaNCsexg=", //新172开发数据库 //228生产环境数据库
    "CRM2": "4B+zDDqdDuZGm9FuMXcX+749cavDYXqzeFmbi2holI7Db/E7kCRrtFO4TFULxU2fWXGgsz+Mb2nC4F9oiotHv3Qk6zypu9EqeFNiXD1HNQ8zHOFX/wKZRg==", //105
    //"GTIS": "4B+zDDqdDuZGm9FuMXcX+ydUnq98BpOJNU38qVKWZxOsjky0NGsBO1G/RakO6Eeb+H5MbcCM4ra+TlyL42JeVg==",//134生产环境数据库
    "GTIS": "ABsy8Rv+178EK1QfHuQil4ccWLgmgckbAYP4yE4Wif3ZI/0d6gGcCMAzJBjW8p4i+BCVVvpaS76KlUZoWHWdqT4U6YnQtjjn9MBR11OJLNOqBxxOFA1NNg==", //228生产环境数据库
    "LOG": "mJvkZm8N1iWIQ9lOD76clI+PbQXfDmjrZJMM8q4NAtvQKM7ue4H5TkXaCJyIV0edvh5dhs7wCb6EhH4Lcy4u7iW4/HxAuMIBDhxVs7HeTKGt4fIggdKHbF2hfUUoPAak",
    "Redis": "localhost:6379,defaultDatabase=2"
    //"Redis": "*************:6379,defaultDatabase=3"
  },
  "Jwt": {
    //jwt加密秘钥
    "SecretKey": "asdho#^09gan6u4609ian#@)(*&^%ijag/*-+.asfhu!@#$%^",
    //jwt有效时长(小时)
    "ExpressionHours": 1
  },
  //阿里云手机短信配置
  "AliPhoneMessage": {
    "AccessKeyId": "LTAI4GLCvnNGX9H4XiMvLSsE",
    "AccessSecret": "******************************",
    "SignName": "环球慧思"
  },
  //邮箱发送配置
  "Email": {
    //发送主机
    "Host": "smtp.qiye.aliyun.com",
    //端口
    "Port": 25,
    //发件箱
    "UserName": "<EMAIL>",
    //发件箱密码--第三方客户端密码
    "Pwd": "eW7959DRncWy1n0BKvajGVaxw9Re8QQg",
    //测试时的接收邮箱
    //"RecieveEmail": "<EMAIL>"
    "RecieveEmail": ""

  },
  //微信登录配置
  "WeChatLogin": {
    "AppId": "wx8f6f5399c4857097",
    "AppSecret": "ccd21d0c9aaa41133aeeff4444a32a6f"
  },
  "WeChatConfig": {
    "AppID": "wx6b4859d77d0ccb5f",
    "AppSecret": "824675ce5964a28d4c9ba9d6a93de769",
    "grant_type": "client_credential",
    "EncodingAESKey": "1RKdLogWO4NSIDK6lcf5AJF4HMoUp5ocTXMkjIQ0DXs",
    "access_token_url": "https://api.weixin.qq.com/cgi-bin/token",
    "token": "globalwitslive",
    "at_time": "2020/6/5 16:49:02",
    "send_url": "https://api.weixin.qq.com/cgi-bin/message/custom/send",
    "access_token": "",
    "TempleteID1": "2XXTbIWvavTHdry43ZFkfRZLbqvrUf-8QRXE516RAL0",
    "TempleteID2": "MlMUpTFNGdEs_aMe_-W5CDwHVCPM1PVlBUF8p7Jxalc",
    "TempleteID_DataUpdate": "MxObG_9XfLd6zPCIu5G5WJfKYFfd-mlDskUcveQ6HFY",
    "TempleteID3": "cBmMZyPR9ubgim8RmGs12t2c88rbE1hpbS9_PMnUgTg",
    "TempleteID_Reserve": "xBVfMRabUTFpofzIgotM2BcgfFnEAawTswgeIeU9P7E",
    "message_url": "http://202.108.54.28:8036/SMS_WeChat_Service.asmx/WXSend_CRM2",
    "message_id": "op7vDM9mUaSM1wnUP112og=="
  },
  "RSAKey": {
    //登录用公钥
    "PublicKey": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA7glYctrgLDjPgGUFdO1KKavU8u9xIz7eVRqfMlXshfekZHmngYprluBkaRyIIyEH5gGxRQdl8iCk0bRe0A9PNZKxVxI3L+fuHvDUhiskOFlq71h7o5FVzB6EY4plU3Fk6dQQkwsCgnCUmlo15tGheYUV/7qfXgzoC+x7rvdE/oSWH62hIwSOhI0itsBWoyP+p9L6Cj17tC/ZYSq+Z6hpHfFDHFBvF4yjRALlWs1lMTixYkkwuL2GddUsE2C+WO0aLvIz907e8gqE3z10nHnX5X4S/Ixk6EvLYfAHUobfUUOPFXcTs0eyCgG/H3fAhhuav97uN40BuSC+iFtPeC+hvwIDAQAB",
    //登录使用私钥
    "PrivateKey": "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDuCVhy2uAsOM+AZQV07Uopq9Ty73EjPt5VGp8yVeyF96RkeaeBimuW4GRpHIgjIQfmAbFFB2XyIKTRtF7QD081krFXEjcv5+4e8NSGKyQ4WWrvWHujkVXMHoRjimVTcWTp1BCTCwKCcJSaWjXm0aF5hRX/up9eDOgL7Huu90T+hJYfraEjBI6EjSK2wFajI/6n0voKPXu0L9lhKr5nqGkd8UMcUG8XjKNEAuVazWUxOLFiSTC4vYZ11SwTYL5Y7Rou8jP3Tt7yCoTfPXScedflfhL8jGToS8th8AdSht9RQ48VdxOzR7IKAb8fd8CGG5q/3u43jQG5IL6IW094L6G/AgMBAAECggEAF69UXrFKpvvMIdFrdpP50iWn3bhXgJNL2hn5BzAnhbj7YYf9U9VrGSsgspjFLwGlYIyAkAbJbQs0ey1AVeGAFyuOe8RzO1APkIKUEvmN9BFpo4EhZEgcyl+q+qCVrnTU/rM8JvVsfYAoOSeKv3nSYF4Or1BpdRlBqZYRRHqUIeLsS881GeR2duXwn/SiBSLW8T81hvvh7mnPN7itWbvzp4K1nwD2tbXdtG/GiBk2Lqqz6vZ/vbeIaEktL+vH6wigKieGP9le1M/TSg7lyhXFBOgP0rDacAjZxjl6KZd8D1oUmPbRnNo/DSUpaI6CTUkQ8bog3dZOpOtVMAxkCQjpGQKBgQD+EaPTNlOBNkUfzKsNi5R8rJxC3PvvG2WFyBy947HKfuyXa8A1JzhVdkUYxeDnGoMPU7cCpG5r8J/Cjfs1sUCYbFQ8jb5PI4cgox1Q6AFh5RztwDYR8nOK/Kw/zQ1DpaE78HcHRDr2lOtgpJPjnpaCg8wA4OvXfQVxLGQYmmi4PQKBgQDv2IKapmXmCKlPlsScmjdK3CemP8UG34sEqzw0ojggJ1ijTL9EtU1g1PIrlyvsDfWjuAfO1NuQJM7QIxisYwen2VH8nnW40uxw/4sfdz7nqlzMZ7DE2f/ozvnhi+1ajFcR0V1hhK+x8Q4aawh+9/RXmtbg7y2wXqeG21+tDgblqwKBgDinilaQ6LIJfgSHSSVuGKBshTPLv0okW9EnPHGDkqPLBcsGNggkRMWZ5/KMPYiLOupyCn9GV3EuaRGjQ39HlVXn4ijWmrde/sTiWqkt8xD3XCoQEH6UB/vYODjJ6slI59lL198A8f0GXC5ixK+fL4TPCvCvNygIMux3r6Jk3DEpAoGBAKv68wOTh4Of+E/Six7D0hwCqwehuFTKXDO32U7gdyBPNla6BHhvDJH/f7rUBjGYfOetYSPlK587VXSXheyugeHQieJ7eWxTCimog4jHh7q0RSBoxa8Rple4eiNJo6OG+DQVjUEM1LLiZ7t3vjrU70cWBzh5KKjL5pJjoUJEyZtLAoGBAMVPNj8NjyVagSaCwtZAkJltsXzyMBzdq8cWjolVqRMqvsOQwF7u2eBqoSycqVos8or3VIsoL36zzHrWltEEW5++YBtmzhYnKNaSKHO5qSRrFp7pbNZZ5zH7PeKj8ppPKMt3fCu6xoqlufDlH5qmdFTsMzp1Qaeq8T8tdQKO8Tb6"
  },
  "GlobalSearchAPI": {
    //"AddPrimaryUserUrl": "https://mgr.globalsou.com/api/?globalwitsID={0}&globalwitsName={1}&member=ent&acctType=online&dt_service_from={2}&dt_service_to={3}&actionType=acct_add&st_bill_type={4}&st_bill_month={5}&st_memo={6}",
    //"AddSubUserUrl": "https://mgr.globalsou.com/api/?globalsouID_Parent={0}&globalwitsID={1}&globalwitsName={2}&member=ent_normal&actionType=acct_add&st_memo={3}",
    //"CheckUserStatus": "https://mgr.globalsou.com/api/?cstids={0}&actionType=acct_status",
    //"UpdateUserUrl": "https://mgr.globalsou.com/api/?souID={0}&globalwitsID={1}&globalwitsName={2}&member=ent&acctType=online&dt_service_from={3}&dt_service_to={4}&st_bill_type={5}&st_bill_month={6}&st_memo={7}&actionType=acct_upd",

    "AddPrimaryUserUrl": "https://mgr.globalsou.com/api/?globalwitsID={0}&globalwitsName={1}&member=ent&acctType=online&dt_service_from={2}&dt_service_to={3}&actionType=acct_add&st_bill_type={4}&st_bill_month={5}&st_memo=xxx_testwu",
    "AddSubUserUrl": "https://mgr.globalsou.com/api/?globalsouID_Parent={0}&globalwitsID={1}&globalwitsName={2}&member=ent_normal&actionType=acct_add",
    "CheckUserStatus": "https://mgr.globalsou.com/api/?cstids={0}&actionType=acct_status",
    "UpdateUserUrl": "https://mgr.globalsou.com/api/?souID={0}&globalwitsID={1}&globalwitsName={2}&member=ent&acctType=online&dt_service_from={3}&dt_service_to={4}&st_bill_type={5}&st_bill_month={6}&st_memo=xxx_testwu_upd&actionType=acct_upd",
  },
  "TianyanchaAPI": {
    "Tianyancha_Token": "61ff94eb-da42-46b6-8c3a-ce1af29d54d6",
    "Tianyancha_BasicInfoUrl": "http://open.api.tianyancha.com/services/open/ic/baseinfo/normal?keyword=",
    "Tianyancha_QueryUrl": "http://open.api.tianyancha.com/services/open/search/2.0?pageSize=20&pageNum=1&word=",
    "Tianyancha_MaxNum": "10000"
  },
  //请求gtis需要的地址和token
  "GtisApi": {
    "Url": "http://192.168.0.105:6003",//测试
    //"Url": "http://192.168.0.226:6003", //开发
    //"UrlBackUp": "http://192.168.0.50:6003",//正式环境备份库，查询使用情况用
    "UrlBackUp": "http://192.168.0.226:6003",
    "UrlDemo": "http://192.168.0.105:6004",
    //"UrlBackUp": "http://192.168.0.226:6003",
    //"UrlDemo": "http://192.168.0.105:6004",
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCIsImN0eSI6IkpXVCJ9.eyJqdGkiOiJ0c1p6RC9FSjJneE8yQjhINVJGZVBTVUlMcGV1NE1ZV3JSaHpYdnZMZldZL0o0M2g2R1FMUG12QzRWTk5UMS9tV1MwVjV1dWtKL3pGMVZOWklXSTdXeUE1N2RIakVYN3JuYWlSYUFCT0RXRVFVcC9IN1ZkM1V4bitqQ0x4RDloUGNmSHhBQmRKV0lFYzZ0NFRkbzdFaCtOaUpGQjlHa3RxamhUYzE0YS9RbzcvMEFWd2krWVA3WDZSU25xNk85aXhMNGZJem5UeWxrRXczZ2xhVXFlUWp5YkZsQmlmR2NvcWZvbGhIRTZEZ2srODVxNnA3RWNLNXUwUFl1QnJ5Y0NRRlhDWG51ek9EczVzOTJvYWpPMld4UHFwdnVwd1JZbVJpSU5KaUJ0QllFaWtDTzhreHNKSUtSQ3hGUkRVaG53RHlBZGpEdGxoTTBvd0d5YkRpcWxrSUM0eFhPcktYcEZjMU9abStHNytydWs9IiwibmJmIjoxNjk1ODcyNzgwLCJleHAiOjE2OTU4NzYzODB9.DUzvLfgkWkvtXVM6Qe7WGcUcUtv1I-JVcvM6VU515AM"
  },
  "SilenceMinutes": 60,
  "BindWeChatMenu": false,
  "AutoVoidContractDays": 60,
  //"DownLoadFilePath": "/hqhs_data/00.API/01.CRM/03.FileUpload"
  "DownLoadFilePath": "/FileUpload",
  "OldDownLoadFilePath": "/hqhs_data/00.API/01.CRM/02.CompanyFiles",
  "GTISTypeDescriptionDate2025": "2025-01-01",
  "GTISTypeDescriptionContent2025": "GTIS 6.0",
  "GTISTypeDescriptionDate2026": "2026-01-01",
  "GTISTypeDescriptionContent2026": "GTIS 7.0",
  "GTISTypeDescriptionDate2027": "2027-01-01",
  "GTISTypeDescriptionContent2027": "GTIS 8.0",
  "GTISTypeDescriptionDate2028": "2028-01-01",
  "GTISTypeDescriptionContent2028": "GTIS 9.0",
  "QCloud": {
    "SecretId": "AKIDoWBdjcKrCKH2bzBB1unYBe8WltskU8oK",
    "SecretKey": "Vs5A6tstlSyKZvLHBxcAoPp4Tp3AA3ud",
    "Region": "ap-beijing",
    "Host": "witsfiles.globalwits.cn",
    "Enable": true
  },
  "DingTalk": {
    "AppKey": "dinghlwojv7p62jtlmh9",
    "AppSecret": "2T3CCcqWA5sRiFbPCqkK72kU5oLc2Zps63pBsj65XrE7ZWsMatCu_NUHv1E9_mMX",
    "RobotCode": "dinghlwojv7p62jtlmh9",
    "AgentId": "3535939366",
    "CorpId": "ding6ac5463ef32c5776bc961a6cb783455b"
  },
  "LogConfig": {
    "EnableLogging": true,
    "LogLevel": "Information",
    "LogDirectory": "Logs",
    "ClickHouseHost": "niW3W7A/tjmxrHPu8oOVlRasDDiqrVqQ8tUfL8ySG54=",
    "ClickHousePort": 443,
    "ClickHouseUser": "rk0O9yn//1c=",
    "ClickHousePassword": "HQqK7LYC6yc="
  },
  "UmiOcr": {
    "ServiceUrl": "http://*************:8088"
  },
  "SaleWits": {
    "ApiUrl_Online": "http://saleswits.globalwits.cy/api/v1/tenant/credit",
    "ApiUrl_Test": "http://ai.globalwits.cn:8011/api/v1/tenant/credit",
    "TestUuid": "7d2ee649-8a73-4d10-8e5f-0a6b8e4428d4",
    "OnlineToken": "eyJhbGciOiJSUzI1NiIsImtpZCI6IjY1RkRBREJGMEU0OERENzkxNzdBODZERjExRTYwMUI1RjU1Qzc1MUUiLCJ4NXQiOiJaZjJ0dnc1STNYa1hlb2JmRWVZQnRmVmNkUjQiLCJ0eXAiOiJhdCtqd3QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.fQVAe-JGZQRr90_8S4GSb8grOIrmpulyn9DSj5f4gkNEtCigkfz9JiqDVMVvnPXJDqNflzkrBYC0C4rSruVCZGCXQAM9p_Broh77lIqheUrJ1vfVh0l01JkagXHUYtlvNPFxIFPst01L1fYitZ_71LKlQ6EVeyr8If39O-P_r0fhDDMMuasjQkXJDe9NQF_70igbjE3njs2MbLFLPXCjQWCG_pJWRR1S5YBDp7ZVJy11YoiNRyZNUUt4fH8lCJCOMQlBzoVmpaPdeNMroiVnb1tKFuev4rL6lLjq9OdovPx_wIIKOCIkxVHeh3_aBK0EGNqnCYfLuhFZOxmP8HXTxw",
    "Environment": "Test"
  }
}
