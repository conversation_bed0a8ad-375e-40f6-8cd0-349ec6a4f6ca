using CRM2_API.BLL;
using CRM2_API.BLL.Common;
using CRM2_API.Common.Cache;
using CRM2_API.DAL.DbCommon;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.System;
using LgyUtil;
using LumiSoft.Net.Media;
using SqlSugar;

namespace CRM2_API.DAL.DbModelOpe.Crm2
{
    /// <summary>
    /// crm_mergecompany_relationship表操作
    /// </summary>
    public class DbOpe_crm_mergecompany_relationship : DbOperateCrm2Ex<Db_crm_mergecompany_relationship, DbOpe_crm_mergecompany_relationship>
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="showCustomerId"></param>
        /// <returns></returns>
        public List<string> GetMergedCustomerIds(string showCustomerId)
        {
            var r = Db.Queryable<Db_v_customer_merge>().Where(m => m.AfterCustomerId == showCustomerId).Select(m => m.BeforeCustomerId).ToList();
            r.Add(showCustomerId);
            return r;
        }
        /// <summary>
        /// 获取审核详情
        /// </summary>
        /// <param name="auditId"></param>
        public GetMergeCustomerAudit_OUT GetAuditInfo(string auditId)
        {
            GetMergeCustomerAudit_OUT getMergeCustomerAudit = new GetMergeCustomerAudit_OUT();
            DateTime dt = DateTime.Now;
            var audit = DbOpe_crm_mergecompany_audit.Instance.QueryByPrimaryKey(auditId);
            if (audit == null)
            {
                throw new ApiException("未找到对应的审核");
            }
            else
            {
                getMergeCustomerAudit = audit.MappingTo<GetMergeCustomerAudit_OUT>();
                getMergeCustomerAudit.AuditId = auditId;
                getMergeCustomerAudit.State = (EnumCustomerMergeAuditState)audit.State;
                getMergeCustomerAudit.IsChangeValid = true;
                var relations = Queryable.Where(c => c.MergeCompanyAuditId == audit.Id).ToList();
                getMergeCustomerAudit.MergeCustomerList = new List<AuditCustomerInfo_OUT>();
                //这里得验证一下 合并的这几个客户还在不在那个客户下，如果都不在了要提示错误，部分还在就只显示还在的
                var mainCustomerId = audit.CustomerId;
                var mainCustomer = DbOpe_crm_customer.Instance.GetCustomerInfoIgnoreAuth(mainCustomerId, false);
                //var test = DbOpe_crm_customer_copy.Instance.GetCustomerCopy(EnumCustomerCopyAuditType.Merge, audit.CustomerId, auditId);
                if (mainCustomer == null)
                {
                    getMergeCustomerAudit.IsChangeValid = false;
                    //合并的主客户找不到了 找快照
                    mainCustomer = DbOpe_crm_customer_copy.Instance.GetCustomerCopy(EnumCustomerCopyAuditType.Merge, audit.CustomerId, auditId);
                    if (mainCustomer == null)
                    {
                        throw new ApiException("客户关系已变更，无法查看");
                    }
                }
                #region 关系说明文件
                getMergeCustomerAudit.RelationshiptFiles = Db.Queryable<Db_crm_mergecompany_attachfile>()
                  .Where(r => r.Deleted == false)
                  .Where(r => r.AuditId == auditId)
                  .Select(r => new BM_FileInfo { Id = r.Id, FileName = r.FileName, FilePath = r.FilePath, CreateDate = r.CreateDate.ToString() })
                  .ToList();
                #endregion
                getMergeCustomerAudit.MainCustomerInfo = mainCustomer;
                DateTime tempDate = DateTime.Now;
                if (DateTime.TryParse(getMergeCustomerAudit.MainCustomerInfo.ProtectionDeadline, out tempDate))
                {
                    getMergeCustomerAudit.MainCustomerInfo.ProtectionDeadline = tempDate.ToString("yyyy-MM-dd");
                }
                foreach (var rel in relations)
                {
                    if (rel.SubCustomerId == mainCustomerId)
                    {
                        //避免显示拆分更换主公司后 自己合并自己的记录
                        continue;
                    }
                    if (rel.State != (int)EnumCustomerMergeAuditState.OK)
                    {
                        AuditCustomerInfo_OUT auditMergeCustomer = new AuditCustomerInfo_OUT();
                        //如果待审核或者拒绝了或者拆分了，那就直接获取当前的子客户信息就行了
                        var mergeCustomer = DbOpe_crm_customer.Instance.GetCustomerInfoIgnoreAuth(rel.SubCustomerId, false);
                        //var test1 = DbOpe_crm_customer_copy.Instance.GetCustomerCopy(EnumCustomerCopyAuditType.Merge, rel.SubCustomerId, auditId);
                        if (mergeCustomer == null)
                        {

                            //客户找不到了 找快照
                            mergeCustomer = DbOpe_crm_customer_copy.Instance.GetCustomerCopy(EnumCustomerCopyAuditType.Merge, rel.SubCustomerId, auditId);
                            if (mergeCustomer == null)
                            {
                                throw new ApiException("未找到对应的客户，无法查看");
                            }
                            auditMergeCustomer = mergeCustomer.MappingTo<AuditCustomerInfo_OUT>();
                            auditMergeCustomer.IsChangeValid = false;
                        }
                        else
                        {
                            auditMergeCustomer = mergeCustomer.MappingTo<AuditCustomerInfo_OUT>();
                            auditMergeCustomer.IsChangeValid = rel.State == (int)EnumCustomerMergeAuditState.InProcess;
                        }

                        auditMergeCustomer.AuditId = rel.Id;
                        auditMergeCustomer.AuditState = (EnumCustomerMergeAuditState)rel.State;
                        getMergeCustomerAudit.MergeCustomerList.Add(auditMergeCustomer);
                    }
                    else
                    {
                        if (getMergeCustomerAudit.IsChangeValid)
                        {
                            //如果是审核通过了的，对于每个合并的子客户，看一下现在还在不在这个合并的主公司下面（主公司在不在现在的子公司里）
                            if (mainCustomer.SubCompanys.Find(c => c.CompanyId == rel.SubCustomerMainCompanyId) != null)
                            {
                                var tempRelationHistoryCompanyIds = new List<string>();
                                //还在里面，获取子客户当前的结构
                                var mergeCustomer = GetHistoryMergeSubCustomerOut(rel, mainCustomer, ref tempRelationHistoryCompanyIds);
                                //var test2 = DbOpe_crm_customer_copy.Instance.GetCustomerCopy(EnumCustomerCopyAuditType.Merge, rel.SubCustomerId, auditId);
                                AuditCustomerInfo_OUT auditMergeCustomer = new AuditCustomerInfo_OUT();
                                if (mergeCustomer == null)
                                {
                                    //客户找不到了 找快照
                                    mergeCustomer = DbOpe_crm_customer_copy.Instance.GetCustomerCopy(EnumCustomerCopyAuditType.Merge, rel.SubCustomerId, auditId);
                                    if (mergeCustomer == null)
                                    {
                                        throw new ApiException("未找到对应的客户，无法查看");
                                    }
                                    //在主客户下面剔除子客户下的公司
                                    mainCustomer.SubCompanys.RemoveAll(c => mergeCustomer.SubCompanys.Select(sc => sc.CompanyId).Contains(c.CompanyId));
                                    mergeCustomer.MappingTo(auditMergeCustomer);
                                    auditMergeCustomer.IsChangeValid = false;
                                }
                                else
                                {
                                    mergeCustomer.MappingTo(auditMergeCustomer);
                                    auditMergeCustomer.IsChangeValid = true;
                                    //在主客户下面剔除子客户下的公司
                                    mainCustomer.SubCompanys.RemoveAll(c => tempRelationHistoryCompanyIds.Contains(c.CompanyId));
                                }
                                auditMergeCustomer.AuditId = rel.Id;
                                auditMergeCustomer.AuditState = (EnumCustomerMergeAuditState)rel.State;
                                getMergeCustomerAudit.MergeCustomerList.Add(auditMergeCustomer);

                            }
                        }
                        else
                        {
                            var mergeCustomer = DbOpe_crm_customer.Instance.GetCustomerInfoIgnoreAuth(rel.SubCustomerId, false);
                            AuditCustomerInfo_OUT auditMergeCustomer = new AuditCustomerInfo_OUT();
                            if (mergeCustomer == null)
                            {
                                //客户找不到了 找快照
                                mergeCustomer = DbOpe_crm_customer_copy.Instance.GetCustomerCopy(EnumCustomerCopyAuditType.Merge, rel.SubCustomerId, auditId);
                                if (mergeCustomer == null)
                                {
                                    throw new ApiException("未找到对应的客户，无法查看");
                                }
                                mergeCustomer.MappingTo(auditMergeCustomer);
                                auditMergeCustomer.IsChangeValid = false;
                            }
                            else
                            {
                                mergeCustomer.MappingTo(auditMergeCustomer);
                                auditMergeCustomer.IsChangeValid = false;
                            }
                            //在主客户下面剔除子客户下的公司
                            mainCustomer.SubCompanys.RemoveAll(c => mergeCustomer.SubCompanys.Select(sc => sc.CompanyId).Contains(c.CompanyId));
                            auditMergeCustomer.AuditId = rel.Id;
                            auditMergeCustomer.AuditState = (EnumCustomerMergeAuditState)rel.State;
                            getMergeCustomerAudit.MergeCustomerList.Add(auditMergeCustomer);
                        }

                    }

                }
                return getMergeCustomerAudit;
            }
        }
        /// <summary>
        /// 获取被合并的子客户现在的信息
        /// </summary>
        /// <param name="relation"></param>
        /// <param name="mergeMainCustomer"></param>
        /// <param name="historyCompanyIds"></param>
        /// <returns></returns>
        public CustomerInfo_OUT GetHistoryMergeSubCustomerOut(Db_crm_mergecompany_relationship relation, CustomerInfo_OUT mergeMainCustomer, ref List<string> historyCompanyIds)
        {
            CustomerInfo_OUT customerInfo_OUT = new CustomerInfo_OUT();
            customerInfo_OUT.Pool = mergeMainCustomer.Pool;
            customerInfo_OUT.ProtectionDeadline = mergeMainCustomer.ProtectionDeadline;
            customerInfo_OUT.UserName = mergeMainCustomer.UserName;
            customerInfo_OUT.CustomerId = relation.SubCustomerId;
            var subCustomerData = DbOpe_crm_customer.Instance.GetCustomerDataExist(relation.SubCustomerId);
            subCustomerData.MappingTo(customerInfo_OUT);
            customerInfo_OUT.CompanyId = relation.SubCustomerMainCompanyId;
            customerInfo_OUT.SubCompanys = new List<Company_OUT>();
            customerInfo_OUT.CustomerLevelName = ((EnumCustomerLevel)subCustomerData.CustomerLevel).GetEnumDescription();
            customerInfo_OUT.TrackingStageName = ((EnumTrackingStage)subCustomerData.TrackingStage).GetEnumDescription();
            customerInfo_OUT.CustomerSourceName = ((EnumCustomerSource)subCustomerData.CustomerSource).GetEnumDescription();
            customerInfo_OUT.PeerDataName = ((EnumPeerData)subCustomerData.PeerData).GetEnumDescription();
            customerInfo_OUT.CustomerNatureName = ((EnumCustomerNature)subCustomerData.CustomerNature).GetEnumDescription();
            customerInfo_OUT.CustomerSizeName = ((EnumCustomerSize)subCustomerData.CustomerSize).GetEnumDescription();
            var seviceState = Db.Queryable<Db_v_customerproductserviceinfostatusold>().Where(p => p.CustomerId == subCustomerData.Id).First();
            if (seviceState != null)
            {
                customerInfo_OUT.ServiceState = (EnumCustomerProductServiceState)seviceState.ProductServiceInfoStatus;
            }
            else
            {
                customerInfo_OUT.ServiceState = EnumCustomerProductServiceState.NONE;
            }
            customerInfo_OUT.ServiceStateName = customerInfo_OUT.ServiceState.GetEnumDescription();
            historyCompanyIds = DbOpe_crm_mergecompany_relationship_subcompany.Instance.GetRelationHistoryCustomerCompanys(relation.Id, relation.SubCustomerId).Select(r => r.CustomerSubCompanyId).ToList();
            var companyWithMainList = DbOpe_crm_customer_subcompany.Instance.GetCompanyInfoById(historyCompanyIds);
            companyWithMainList.ForEach(c =>
            {
                //这里要把产品和行业和地址的ID翻译成中文
                var company_out = c.MappingTo<Company_OUT>();
                company_out.CompanyId = c.Id;
                company_out.MainProducts = new Dictionary<string, string>();
                company_out.CustomerIndustry = new Dictionary<string, string>();
                company_out.CountryName = (company_out.Country == -1 || company_out.Country == 0) ? "" : LocalCache.LC_Address.CountryAndAreaCache.Find(c => c.Id == company_out.Country).Name;
                company_out.ProvinceName = (company_out.Province == -1 || company_out.Province == 0) ? "" : LocalCache.LC_Address.ProvinceCache.Find(c => c.Id == company_out.Province).Name;
                company_out.CityName = (company_out.City == -1 || company_out.City == 0) ? "" : LocalCache.LC_Address.CityCache.Find(c => c.Id == company_out.City).Name;
                c.bussinessList.ForEach(l =>
                {
                    if (!company_out.MainProducts.ContainsKey(l.MainProducts))
                    {
                        company_out.MainProducts.Add(l.MainProducts, l.MainProductName);
                    }
                    if (!company_out.CustomerIndustry.ContainsKey(l.CustomerIndustry))
                    {
                        company_out.CustomerIndustry.Add(l.CustomerIndustry, l.CustomerIndustryName);
                    }
                });
                company_out.IndustryName = company_out.CustomerIndustry.Values.JoinToString();
                company_out.ProductName = company_out.MainProducts.Values.JoinToString();
                if (company_out.CompanyId == relation.SubCustomerMainCompanyId)
                {
                    company_out.IsMain = EnumCustomerCompanyMain.Main;
                    company_out.MappingTo(customerInfo_OUT);
                    customerInfo_OUT.CustomerId = subCustomerData.Id;
                    customerInfo_OUT.CompanyId = relation.SubCustomerMainCompanyId;
                }
                else
                {
                    customerInfo_OUT.SubCompanys.Add(company_out);
                }
            });
            return customerInfo_OUT;
        }

        /// <summary>
        /// 审核合并客户 10.16 有通过的就要刷新跟踪阶段
        /// </summary>
        /// <param name="mergeCustomerAudit_IN"></param>
        /// <param name="user"></param>
        /// <param name="isHaveFlow"></param>
        public void MergePrivatePoolAudit(MergeCustomerAudit_IN mergeCustomerAudit_IN, string user, bool isHaveFlow = true)
        {
            DateTime dt = DateTime.Now;
            DbOpe_crm_mergecompany_audit.Instance.TransDeal(() =>
            {
                var audit = DbOpe_crm_mergecompany_audit.Instance.QueryByPrimaryKey(mergeCustomerAudit_IN.AuditId);
                if (audit == null || audit.State != (int)EnumCustomerMergeAuditState.InProcess || audit.Deleted == (int)EnumCustomerDel.Del)
                {
                    throw new ApiException("未找到对应的审核或审核已完成");
                }
                else
                {
                    audit.ReviewerId = user;
                    audit.ReviewerDate = dt;
                    audit.Feedback = mergeCustomerAudit_IN.Feedback;
                    //audit.State = (int)mergeCustomerAudit_IN.State;
                    var relationList = Queryable.Where(a => a.MergeCompanyAuditId == audit.Id)
                        .Where(a => a.Deleted == (int)EnumCustomerDel.NotDel)
                        .ToList();
                    foreach (Db_crm_mergecompany_relationship r in relationList)
                    {
                        var resultObj = mergeCustomerAudit_IN.MergeCustomerAuditResults.Find(c => c.Id == r.Id);
                        if (resultObj == null)
                        {
                            continue;
                        }
                        var targetState = resultObj.Result;
                        if (targetState == (int)EnumCustomerMergeAuditState.Refuse)
                        {
                            //拒绝的就直接更新审核状态就可以了
                            r.IsValid = (int)EnumCustomerMergeRelationValid.INVALID;
                            r.InvalidDate = dt;
                            r.State = (int)targetState;
                            DbOpe_crm_mergecompany_relationship.Instance.Update(r);
                        }
                        else
                        {
                            //通过的话不仅要更新审核状态，还要处理合并的客户
                            //先更新审核状态
                            r.IsValid = (int)EnumCustomerMergeRelationValid.Valid;
                            r.State = (int)targetState;
                            DbOpe_crm_mergecompany_relationship.Instance.Update(r);
                            //再处理合并的客户
                            ExcuteMerge(new List<Db_crm_mergecompany_relationship>() { r }, resultObj.SameCompany == 1);
                        }
                    }
                    if (relationList.Find(d => d.State == (int)EnumCustomerMergeAuditState.InProcess) != null)
                    {
                        //只要存在一个没审核结果的子公司，这条审核就标记为待审核
                        audit.State = (int)EnumCustomerMergeAuditState.InProcess;
                    }
                    else if (relationList.Find(d => d.State == (int)EnumCustomerMergeAuditState.OK) != null)
                    {
                        //子公司只要有一个通过审核，就算其他全拒绝，那这条审核就标记为通过
                        audit.State = (int)EnumCustomerMergeAuditState.OK;
                    }
                    else
                    {
                        audit.State = (int)EnumCustomerMergeAuditState.Refuse;
                    }
                    if (relationList.Find(d => d.State == (int)EnumCustomerMergeAuditState.OK) != null)
                    {
                        //有通过的就要刷新跟踪阶段
                        DbOpe_crm_customer.Instance.RefreshTrackingStage(audit.CustomerId);
                    }
                    DbOpe_crm_mergecompany_audit.Instance.Update(audit);
                    if (isHaveFlow)
                    {
                        string dataState = Dictionary.CustomerMergeAuditState.First(e => e.Value == audit.State.ToInt().ToString()).Name;
                        var customerInfo = DbOpe_crm_customer.Instance.GetCustomerInfoIgnoreAuth(audit.CustomerId);
                        BLL_WorkFlow.Instance.AddWorkFlow("合并客户审批流程", audit.Id, audit, customerInfo, audit.Feedback, dataState, "审核");
                    }
                }

            });

        }

        public void ExcuteMerge(List<Db_crm_mergecompany_relationship> relationList,bool SameCompany = false)
        {
            DateTime dt = DateTime.Now;
            var subCustomerIds = relationList.Select(r => r.SubCustomerId).ToList();
            foreach (var relation in relationList)
            {
                var mainCustomerId = relation.MainCustomerId;
                var subCustomerId = relation.SubCustomerId;
                var customerData = DbOpe_crm_customer.Instance.GetCustomerInfoById(subCustomerId);
                if (customerData != null)
                {
                    customerData.IsMerge = (int)EnumCustomerMerge.Merge;
                    PrivateCustomerSimpleInfo poolData = new PrivateCustomerSimpleInfo();
                    if (DbOpe_crm_customer_privatepool.Instance.CheckInPool(subCustomerId, ref poolData))
                    {
                        //如果这个客户当前在私有池，需要把被合并的客户私有池记录状态更新一下(被合并,相当于释放了)
                        poolData.State = (int)EnumCustomerPrivateRelease.Merge;
                        poolData.ReleaseType = (int)EnumCustomerPrivateReleaseType.Auto;
                        poolData.ReleaseTime = dt;
                        DbOpe_crm_customer_privatepool.Instance.Update(poolData);
                    }
                    DbOpe_crm_customer.Instance.Update(customerData);
                    //11.30 被合并后 删除被合并客户的归属日志
                    DbOpe_crm_customer_org_log.Instance.MergeCustomerLog(subCustomerId);
                    var companyList = customerData.subCompanyList.ToList();
                    companyList.Add(customerData.mainCompany);
                    foreach (var company in companyList)
                    {
                        //记录一下将要被合并的客户的原有主子关系
                        DbOpe_crm_mergecompany_relationship_subcompany.Instance.Insert(new Db_crm_mergecompany_relationship_subcompany()
                        {
                            Id = Guid.NewGuid().ToString(),
                            OldCustomerId = subCustomerId,
                            NewCustomerId = mainCustomerId,
                            MergeCompanyRelationshipId = relation.Id,
                            CustomerSubCompanyId = company.Id,
                            IsMain = company.IsMain
                        });
                    }
                    //更新将要被合并的客户的从属关系
                    var customerSubCompanyList = companyList.MappingTo<List<Db_crm_customer_subcompany>>();
                    customerSubCompanyList.ForEach(c =>
                    {
                        c.CustomerId = mainCustomerId;
                        c.IsMain = (int)EnumCustomerCompanyMain.Sub;
                        if (c.IsMain == (int)EnumCustomerCompanyMain.Main && c.CreditType == (int)EnumCompanyType.Other && SameCompany)
                        {
                            //20240706 如果审核判断是国内同名客户/国内客户曾用名，要把(被合并客户主公司的)客户类型更新回《国内客户》
                            c.CreditType = (int)EnumCompanyType.CN;
                        }
                    });
                    DbOpe_crm_customer_subcompany.Instance.Update(customerSubCompanyList);
                }
                else
                {
                    throw new ApiException("未找到对应客户");
                }
            }

        }

        /// <summary>
        /// 验证是不是在合并审核流程中
        /// </summary>
        /// <param name="customerIds"></param>
        /// <param name="throwException"></param>
        public List<string> CheckAuditInProcess(List<string> customerIds, bool throwException = true)
        {
            List<string> unableCustomerIds = new List<string>();
            var inProcessDatas = Queryable.LeftJoin<Db_crm_mergecompany_audit>((auditCompany, audit) => auditCompany.MergeCompanyAuditId == audit.Id)
                .Where((auditCompany, audit) => audit.State == (int)EnumCustomerMergeAuditState.InProcess)
                .Where((auditCompany, audit) => auditCompany.Deleted == (int)EnumCustomerDel.NotDel)
                .Where((auditCompany, audit) => audit.Deleted == (int)EnumCustomerDel.NotDel)
                .ToList();
            if (inProcessDatas.Count > 0)
            {
                var inProcessCustomerIds = inProcessDatas.Select(x => x.MainCustomerId).ToList();
                inProcessCustomerIds = inProcessCustomerIds.Concat(inProcessDatas.Select(x => x.SubCustomerId).ToList()).Distinct().ToList();
                unableCustomerIds = customerIds.FindAll(c => inProcessCustomerIds.Contains(c));
                if (unableCustomerIds.Count > 0 && throwException)
                {
                    var unableNames = DbOpe_crm_customer_subcompany.Instance.GetCustomerMainCompany(unableCustomerIds);
                    var companyNames = unableNames?.Select(c => c?.CompanyName).Where(name => !string.IsNullOrEmpty(name)).ToList() ?? new List<string>();
                    throw new ApiException("公司(" + string.Join(",", companyNames) + ")已经在合并审核流程中");
                }
            }
            return unableCustomerIds;
        }
        /// <summary>
        /// 验证是不是在合并审核流程中
        /// </summary>
        /// <param name="customerIds"></param>
        /// <param name="mergeCompanyAuditId"></param>
        /// <param name="throwException"></param>
        public List<string> CheckAuditInProcessNoSelf(List<string> customerIds,string mergeCompanyAuditId, bool throwException = true)
        {
            List<string> unableCustomerIds = new List<string>();
            var inProcessDatas = Queryable.LeftJoin<Db_crm_mergecompany_audit>((auditCompany, audit) => auditCompany.MergeCompanyAuditId == audit.Id)
                .Where((auditCompany, audit) => audit.State == (int)EnumCustomerMergeAuditState.InProcess)
                .Where((auditCompany, audit) => auditCompany.Deleted == (int)EnumCustomerDel.NotDel)
                .Where((auditCompany, audit) => audit.Deleted == (int)EnumCustomerDel.NotDel)
                .Where((auditCompany, audit) => audit.Id != mergeCompanyAuditId)
                .ToList();
            if (inProcessDatas.Count > 0)
            {
                var inProcessCustomerIds = inProcessDatas.Select(x => x.MainCustomerId).ToList();
                inProcessCustomerIds = inProcessCustomerIds.Concat(inProcessDatas.Select(x => x.SubCustomerId).ToList()).Distinct().ToList();
                unableCustomerIds = customerIds.FindAll(c => inProcessCustomerIds.Contains(c));
                if (unableCustomerIds.Count > 0 && throwException)
                {
                    var unableNames = DbOpe_crm_customer_subcompany.Instance.GetCustomerMainCompany(unableCustomerIds);
                    var companyNames = unableNames?.Select(c => c?.CompanyName).Where(name => !string.IsNullOrEmpty(name)).ToList() ?? new List<string>();
                    throw new ApiException("公司(" + string.Join(",", companyNames) + ")已经在合并审核流程中");
                }
            }
            return unableCustomerIds;
        }
        /// <summary>
        /// 取消合并审核流程
        /// </summary>
        /// <param name="customerIds"></param>
        public void CancelAuditInProcess(List<string> customerIds)
        {
            List<string> unableCustomerIds = new List<string>();
            var inProcessDatas = Queryable
                .LeftJoin<Db_crm_mergecompany_audit>((auditCompany, audit) => auditCompany.MergeCompanyAuditId == audit.Id)
                .Where((auditCompany, audit) => audit.State == (int)EnumCustomerMergeAuditState.InProcess)
                .Where((auditCompany, audit) => auditCompany.Deleted == (int)EnumCustomerDel.NotDel)
                .Where((auditCompany, audit) => audit.Deleted == (int)EnumCustomerDel.NotDel)
                .Where((auditCompany, audit) => SqlFunc.ContainsArray(customerIds,auditCompany.MainCustomerId)|| SqlFunc.ContainsArray(customerIds, auditCompany.SubCustomerId))
                .ToList();
            foreach(var p in inProcessDatas) {
                p.Deleted = 1;
                Update(p);
                var audit = Db.Queryable<Db_crm_mergecompany_audit>().Where(a => a.Id == p.MergeCompanyAuditId && a.Deleted == 0).First();
                if(audit != null) { 
                    audit.Deleted = 1;
                    DbOpe_crm_mergecompany_audit.Instance.Update(audit);
                }
            }
        }
        /// <summary>
        /// 验证是不是在合并审核流程中
        /// </summary>
        /// <param name="crmCustomerCompanys"></param>
        public void CheckAuditInProcess(List<CheckCompanyUniqueInfo> crmCustomerCompanys)
        {
            var inProcessDatas = Queryable.LeftJoin<Db_crm_mergecompany_audit>((auditCompany, audit) => auditCompany.MergeCompanyAuditId == audit.Id)
                .Where((auditCompany, audit) => audit.State == (int)EnumCustomerMergeAuditState.InProcess)
                .Where((auditCompany, audit) => auditCompany.Deleted == (int)EnumCustomerDel.NotDel)
                .Where((auditCompany, audit) => audit.Deleted == (int)EnumCustomerDel.NotDel)
                .ToList();
            if (inProcessDatas.Count > 0)
            {
                var inProcessCustomerIds = inProcessDatas.Select(x => x.MainCustomerId).ToList();
                inProcessCustomerIds = inProcessCustomerIds.Concat(inProcessDatas.Select(x => x.SubCustomerId).ToList()).Distinct().ToList();
                var inProcessCompanys = DbOpe_crm_customer_subcompany.Instance.GetCustomerCompanys(inProcessCustomerIds);
                var unableNames = new List<string>();
                var unableCodes = new List<string>();
                foreach (CheckCompanyUniqueInfo ui in crmCustomerCompanys)
                {
                    if (inProcessCompanys.Find(c => c.CompanyName == ui.CompanyName) != null)
                    {
                        unableNames.Add(ui.CompanyName);
                    }
                    else if (inProcessCompanys.Find(c => c.CreditCode == ui.CreditCode && !string.IsNullOrEmpty(c.CreditCode)) != null)
                    {
                        unableCodes.Add(ui.CreditCode);
                    }
                }
                if (unableNames.Count > 0)
                {
                    throw new ApiException("公司(" + string.Join(",", unableNames) + ")已经在合并审核流程中");
                }
                if (unableCodes.Count > 0)
                {
                    throw new ApiException("代码(" + string.Join(",", unableCodes) + ")已经在合并审核流程中");
                }

            }

        }
        /// <summary>
        /// 获取审核相关的还未拆分的关系数据
        /// </summary>
        /// <param name="auditId"></param>
        /// <returns></returns>
        public List<Db_crm_mergecompany_relationship> GetRelationData(string auditId)
        {
            return Queryable
                .Where(r => r.MergeCompanyAuditId == auditId)
                .Where(r => r.Deleted == (int)EnumCustomerDel.NotDel)
                .Where(r => r.IsValid == (int)EnumCustomerMergeRelationValid.Valid)
                .ToList();
        }
    }
}
