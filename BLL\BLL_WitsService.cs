using System;
using System.Collections.Generic;
using System.Linq;
using CRM2_API.BLL.Common;
using CRM2_API.Common;
using CRM2_API.Common.Log;
using CRM2_API.DAL.DbCommon;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BLLModel.ServiceOpening;
using CRM2_API.Model.Enum;
using CRM2_API.Model.System;

namespace CRM2_API.BLL
{
    /// <summary>
    /// 慧思服务业务逻辑
    /// </summary>
    public class BLL_WitsService : BaseBLL<BLL_WitsService>
    {
        /// <summary>
        /// 根据慧思服务ID获取已开通的服务信息
        /// </summary>
        /// <param name="serviceId">慧思服务申请ID</param>
        /// <returns>慧思服务已开通信息</returns>
        public WitsServiceInfoResponse GetWitsServiceInfoByServiceId(string serviceId)
        {
            try
            {
                if (string.IsNullOrEmpty(serviceId))
                {
                    var errorMsg = "慧思服务ID不能为空";
                    LogUtil.AddErrorLog(errorMsg);
                    throw new CRM2_API.Model.System.ApiException(errorMsg);
                }

                // 获取慧思服务主表信息
                var witsService = DbOpe_crm_contract_serviceinfo_wits.Instance.GetDataById(serviceId);
                if (witsService == null)
                {
                    var errorMsg = $"未找到ID为{serviceId}的慧思服务信息";
                    LogUtil.AddErrorLog(errorMsg);
                    throw new CRM2_API.Model.System.ApiException(errorMsg);
                }

                                // 构建返回结果
                var result = new WitsServiceInfoResponse
                {
                    Success = true,
                    Message = "获取慧思服务信息成功",
                    ServiceInfo = new WitsServiceInfo
                    {
                        ServiceId = witsService.Id,
                        ContractId = witsService.ContractId,
                        ContractNum = witsService.ContractNum,
                        ServiceCycleStart = witsService.ServiceCycleStart,
                        ServiceCycleEnd = witsService.ServiceCycleEnd,
                        HasGtisApp = witsService.HasGtisApp ?? false,
                        HasGlobalSearchApp = witsService.HasGlobalSearchApp ?? false,
                        HasSalesWitsApp = witsService.HasSalesWitsApp ?? false,
                        HasCollegeApp = witsService.HasCollegeApp ?? false,
                        UserList = new List<WitsUserInfo>(),
                        IsUrgent = null, // 暂时设为null，等待数据库字段添加
                        RenewContractNum = null, // 暂时设为null，等待数据库字段添加
                        Remark = witsService.Remark,
                        PerlongServiceDays = witsService.PerlongServiceDays,
                        PrivateServiceDays = witsService.PrivateServiceDays,
                        CouponList = witsService.CounponDetailIds
                    }
                };

                // 获取用户账号信息
                var userAccounts = DbOpe_crm_contract_serviceinfo_wits_user.Instance.GetUsersByServelId(witsService.Id);
                if (userAccounts != null && userAccounts.Any())
                {
                    foreach (var account in userAccounts)
                    {
                                                 result.ServiceInfo.UserList.Add(new WitsUserInfo
                        {
                            UserId = account.UserId,
                            AccountNumber = account.AccountNumber,
                            AccountType = account.AccountType,
                            AccountTypeName = GetAccountTypeName(account.AccountType),
                            OpeningStatus = account.OpeningStatus,
                            OpeningStatusName = GetOpeningStatusName(account.OpeningStatus),
                            SharePeopleNum = account.PhoneUserNum, // 使用者数量
                            GtisPermission = account.GtisPermission,
                            GlobalSearchPermission = account.GlobalSearchPermission,
                            SalesWitsPermission = account.SalesWitsPermission,
                            CollegePermission = account.CollegePermission,
                            SalesWitsPhoneId = account.SalesWitsBindPhoneId // SalesWits使用者ID
                        });
                    }
                }

                // 获取GTIS服务信息
                if (witsService.HasGtisApp == true && !string.IsNullOrEmpty(witsService.CurrentGtisServiceId))
                {
                    var gtisService = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetDataById(witsService.CurrentGtisServiceId);
                    if (gtisService != null)
                    {
                        result.ServiceInfo.GtisService = new GtisServiceInfo
                        {
                            ServiceId = gtisService.Id,
                            ServiceCycleStart = gtisService.ServiceCycleStart,
                            ServiceCycleEnd = gtisService.ServiceCycleEnd,
                            ServiceMonth = gtisService.ServiceMonth,
                            PrimaryAccountsNum = gtisService.PrimaryAccountsNum,
                            SubAccountsNum = gtisService.SubAccountsNum,
                            AuthorizationNum = gtisService.AuthorizationNum,
                            ShareUsageNum = gtisService.ShareUsageNum,
                            WordRptPermissions = gtisService.WordRptPermissions,
                            WordRptMaxTimes = gtisService.WordRptMaxTimes,
                            ForbidSearchExport = gtisService.ForbidSearchExport,
                            // SharePeopleNum = gtisService.SharePeopleNum,
                            GtisApplCountry = DbContext.Crm2Db.Queryable<Db_crm_contract_serviceinfo_gtis_country>()
                            .Where(d => d.ContractServiceInfoGtisId == gtisService.Id && d.Deleted == false && d.Sid != null)
                            .Select(d => new Model.ControllersViewModel.VM_Contract.GtisApplCountry() { Sid = d.Sid.Value })
                            .ToList()
                        };
                    }
                }

                // 获取环球搜服务信息
                if (witsService.HasGlobalSearchApp == true && !string.IsNullOrEmpty(witsService.CurrentGlobalSearchServiceId))
                {
                    var globalSearchService = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetDataById(witsService.CurrentGlobalSearchServiceId);
                    if (globalSearchService != null)
                    {
                        result.ServiceInfo.GlobalSearchService = new GlobalSearchServiceInfo
                        {
                            ServiceId = globalSearchService.Id,
                            ServiceCycleStart = globalSearchService.ServiceCycleStart,
                            ServiceCycleEnd = globalSearchService.ServiceCycleEnd,
                            ServiceMonth = globalSearchService.ServiceMonth,
                            AccountCount = globalSearchService.PrimaryAccountsNum + globalSearchService.SubAccountsNum,
                            AccountLevel = globalSearchService.SettlementLevel,
                            PaymentPeriod = globalSearchService.SettlementMonth
                        };
                    }
                }

                // 获取SalesWits服务信息
                if (witsService.HasSalesWitsApp == true && !string.IsNullOrEmpty(witsService.CurrentSalesWitsServiceId))
                {
                    var salesWitsService = DbOpe_crm_contract_serviceinfo_saleswits.Instance.GetDataById(witsService.CurrentSalesWitsServiceId);
                    if (salesWitsService != null)
                    {
                        result.ServiceInfo.SalesWitsService = new SalesWitsServiceInfo
                        {
                            ServiceId = salesWitsService.Id,
                            ServiceCycleStart = salesWitsService.ServiceCycleStart,
                            ServiceCycleEnd = salesWitsService.ServiceCycleEnd,
                            ServiceMonth = salesWitsService.ServiceMonth,
                            AccountCount = salesWitsService.AccountsNum,
                            GiftResourceMonths = salesWitsService.CurrentGiftMonths,
                            TokenCount = salesWitsService.CurrentGiftTokens,
                            EmailCount = salesWitsService.CurrentGiftEmails,
                            AddCredit = salesWitsService.RechargeAmount,
                        };
                    }
                }

                // 获取慧思学院服务信息
                if (witsService.HasCollegeApp == true && !string.IsNullOrEmpty(witsService.CurrentCollegeServiceId))
                {
                    var collegeService = DbOpe_crm_contract_serviceinfo_college.Instance.GetDataById(witsService.CurrentCollegeServiceId);
                    if (collegeService != null)
                    {
                        result.ServiceInfo.CollegeService = new CollegeServiceInfo
                        {
                            ServiceId = collegeService.Id,
                            ServiceCycleStart = collegeService.ServiceCycleStart,
                            ServiceCycleEnd = collegeService.ServiceCycleEnd,
                            ServiceMonth = collegeService.ServiceMonth,
                            AccountCount = collegeService.PrimaryAccountsNum
                        };
                    }
                }

                return result;
            }
            catch (CRM2_API.Model.System.ApiException)
            {
                // 重新抛出业务异常，保持原有的错误信息和上下文
                throw;
            }
            catch (Exception ex)
            {
                var errorMsg = $"获取慧思服务信息异常，服务ID: {serviceId}, 错误: {ex.Message}";
                LogUtil.AddErrorLog($"{errorMsg}，详细异常: {ex}");
                throw new CRM2_API.Model.System.ApiException(errorMsg);
            }
        }

        /// <summary>
        /// 获取账号类型名称
        /// </summary>
        /// <param name="accountType">账号类型</param>
        /// <returns>账号类型名称</returns>
        private string GetAccountTypeName(int? accountType)
        {
            if (!accountType.HasValue)
                return string.Empty;

            // 使用枚举的GetEnumDescription方法获取描述
            if (Enum.IsDefined(typeof(EnumGtisAccountType), accountType.Value))
            {
                return ((EnumGtisAccountType)accountType.Value).GetEnumDescription();
            }
            return accountType.Value.ToString();
        }

        /// <summary>
        /// 获取开通状态名称
        /// </summary>
        /// <param name="openingStatus">开通状态</param>
        /// <returns>开通状态名称</returns>
        private string GetOpeningStatusName(int? openingStatus)
        {
            if (!openingStatus.HasValue)
                return string.Empty;

            // 使用枚举的GetEnumDescription方法获取描述
            if (Enum.IsDefined(typeof(EnumGtisUserOpeningStatus), openingStatus.Value))
            {
                return ((EnumGtisUserOpeningStatus)openingStatus.Value).GetEnumDescription();
            }
            return openingStatus.Value.ToString();
        }
    }
} 