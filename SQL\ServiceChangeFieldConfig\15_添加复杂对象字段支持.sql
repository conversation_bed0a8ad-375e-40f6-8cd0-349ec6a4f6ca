-- 服务变更字段配置 - 添加复杂对象字段支持
-- 执行日期: 2025-02-01
-- 目的：为GtisApplCountry和UserList等复杂对象字段添加IsArray支持

-- ================================
-- 1. 添加新字段到配置表
-- ================================

-- 检查并添加IsArray字段
SET @column_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
                     WHERE TABLE_SCHEMA = DATABASE() 
                     AND TABLE_NAME = 'crm_service_change_reason_field_config' 
                     AND COLUMN_NAME = 'IsArray');

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE crm_service_change_reason_field_config ADD COLUMN IsArray TINYINT(1) DEFAULT 0 COMMENT ''是否为数组/列表类型字段：0-否，1-是''', 
    'SELECT ''IsArray字段已存在'' AS message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加FieldType字段
SET @column_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
                     WHERE TABLE_SCHEMA = DATABASE() 
                     AND TABLE_NAME = 'crm_service_change_reason_field_config' 
                     AND COLUMN_NAME = 'FieldType');

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE crm_service_change_reason_field_config ADD COLUMN FieldType VARCHAR(50) DEFAULT ''simple'' COMMENT ''字段类型：simple-简单类型，list-列表类型，object-对象类型''', 
    'SELECT ''FieldType字段已存在'' AS message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加ComparisonMethod字段
SET @column_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
                     WHERE TABLE_SCHEMA = DATABASE() 
                     AND TABLE_NAME = 'crm_service_change_reason_field_config' 
                     AND COLUMN_NAME = 'ComparisonMethod');

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE crm_service_change_reason_field_config ADD COLUMN ComparisonMethod VARCHAR(50) DEFAULT ''direct'' COMMENT ''比较方式：direct-直接比较，list_diff-列表差异，json_compare-JSON比较''', 
    'SELECT ''ComparisonMethod字段已存在'' AS message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ================================
-- 2. 更新现有的复杂对象字段配置
-- ================================

-- 更新GtisApplCountry配置（详细差异比较）
UPDATE crm_service_change_reason_field_config 
SET IsArray = 1, 
    FieldType = 'list', 
    ComparisonMethod = 'list_diff',
    UpdateDate = CURRENT_TIMESTAMP,
    UpdateUser = 'system'
WHERE FieldKey IN ('GtisApplyCountry', 'GtisApplCountry');

-- 更新UserList配置（简单JSON比较）
UPDATE crm_service_change_reason_field_config 
SET IsArray = 1, 
    FieldType = 'list', 
    ComparisonMethod = 'json_compare',
    UpdateDate = CURRENT_TIMESTAMP,
    UpdateUser = 'system'
WHERE FieldKey IN ('GtisUserList', 'UserList') 
   OR FieldName LIKE '%账户信息%' 
   OR FieldName LIKE '%用户列表%';

-- ================================
-- 3. 验证更新结果
-- ================================

SELECT '复杂对象字段配置更新完成！' AS message;

-- 显示更新后的复杂对象字段配置
SELECT 
    FieldKey,
    FieldName,
    ServiceType,
    ChangeReasonEnum,
    IsArray,
    FieldType,
    ComparisonMethod,
    IsActive
FROM crm_service_change_reason_field_config 
WHERE IsArray = 1
ORDER BY ServiceType, ChangeReasonEnum, DisplayOrder;

-- 显示字段统计
SELECT 
    FieldType,
    ComparisonMethod,
    COUNT(*) as count
FROM crm_service_change_reason_field_config 
WHERE IsArray = 1
GROUP BY FieldType, ComparisonMethod;