﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///合同服务信息_Gtis系列产品申请_用户表
    ///</summary>
    [SugarTable("crm_contract_productserviceinfo_wits_appl_user")]
    public class Db_crm_contract_productserviceinfo_wits_appl_user
    {
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>           
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// Desc:合同服务信息环球慧思申请表id
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string WitsApplId { get; set; }

        /// <summary>
        /// Desc:账号Id
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string SysUserId { get; set; }

        /// <summary>
        /// Desc:账号
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string AccountNumber { get; set; }

        /// <summary>
        /// Desc:密码
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string PassWord { get; set; }

        /// <summary>
        /// Desc:账号类型
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? AccountType { get; set; }

        /// <summary>
        /// Desc:开通状态
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? OpeningStatus { get; set; }

        /// <summary>
        /// Desc:启用日期
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// Desc:停用日期
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// Desc:登录IP
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string LoginIP { get; set; }

        /// <summary>
        /// Desc:最近登录时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? LastLoginTime { get; set; }

        /// <summary>
        /// Desc:每个账户共享人数
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? SharePeopleNum { get; set; }

        /// <summary>
        /// Desc:子账号授权国家次数
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? AuthorizationNum { get; set; }

        /// <summary>
        /// Desc:Gtis权限
        /// Default:
        /// Nullable:True
        /// </summary>           
        public bool? GtisPermission { get; set; }

        /// <summary>
        /// Desc:Vip零售国家权限
        /// Default:
        /// Nullable:True
        /// </summary>           
        public bool? VipPermission { get; set; }

        /// <summary>
        /// Desc:环球搜权限
        /// Default:
        /// Nullable:True
        /// </summary>           
        public bool? GlobalSearchPermission { get; set; }

        /// <summary>
        /// Desc:CollegePermission
        /// Default:
        /// Nullable:True
        /// </summary>           
        public bool? CollegePermission { get; set; }

        /// <summary>
        /// Desc:SalesWits权限
        /// Default:
        /// Nullable:True
        /// </summary>           
        public bool? SalesWitsPermission { get; set; }

        /// <summary>
        /// Desc:SalesWits使用者Id
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string SalesWitsPhoneId { get; set; }

        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:True
        /// </summary>           
        public bool? Deleted { get; set; }

        /// <summary>
        /// Desc:创建人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CreateUser { get; set; }

        /// <summary>
        /// Desc:创建时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? CreateDate { get; set; }

        /// <summary>
        /// Desc:修改人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string UpdateUser { get; set; }

        /// <summary>
        /// Desc:更新时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? UpdateDate { get; set; }

    }
}
