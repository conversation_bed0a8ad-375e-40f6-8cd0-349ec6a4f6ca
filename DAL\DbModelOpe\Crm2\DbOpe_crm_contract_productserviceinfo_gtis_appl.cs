using CRM2_API.BLL;
using CRM2_API.BLL.Common;
using CRM2_API.BLL.GtisOpe;
using CRM2_API.Common.AppSetting;
using CRM2_API.Common.Cache;
using CRM2_API.Common.JWT;
using CRM2_API.DAL.DbCommon;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Gtis;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.System;
using DocumentFormat.OpenXml.Drawing;
using JiebaNet.Segmenter.Common;
using Lucene.Net.Support;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SqlSugar;
using System.Linq;
using static CRM2_API.Model.BLLModel.Enum.CouponEnumOption;
using CRM2_API.Model.BLLModel.Enum;
using static CRM2_API.Model.BLLModel.Enum.PrivateServiceEnumOption;
using static CRM2_API.Model.ControllersViewModel.VM_Contract;

namespace CRM2_API.DAL.DbModelOpe.Crm2
{
    /// <summary>
    /// crm_contract_productserviceinfo_gtis_appl表操作
    /// </summary>
    public class DbOpe_crm_contract_productserviceinfo_gtis_appl : DbOperateCrm2Ex<Db_crm_contract_productserviceinfo_gtis_appl, DbOpe_crm_contract_productserviceinfo_gtis_appl>
    {
        //private List<string> REGISTERROLES = new List<string> { "dcc7660c-155f-4aba-882c-df892e94ee77", "ec6cf8a2-9ecd-4ede-a22e-63e9c8e454e8", "e4530a5d-540b-42db-9286-fe2217040cef", "7bf7f88b-8d06-46c2-94ba-258249e84fd6" };
        //private List<string> REVIEWROLES = new List<string> { "fd538d10-f926-47f0-80c0-cd79cded40eb", "ec6cf8a2-9ecd-4ede-a22e-63e9c8e454e8", "e4530a5d-540b-42db-9286-fe2217040cef", "7bf7f88b-8d06-46c2-94ba-258249e84fd6" };
        //private List<string> VOIDROLES = new List<string> { "fd538d10-f926-47f0-80c0-cd79cded40eb", "ec6cf8a2-9ecd-4ede-a22e-63e9c8e454e8", "e4530a5d-540b-42db-9286-fe2217040cef", "7bf7f88b-8d06-46c2-94ba-258249e84fd6" };
        private List<string> REGISTERBTNS = new List<string> { "b86b5f2a-ea4b-11ed-bc7b-30d042e24322" };
        private List<string> REVIEWRBTNS = new List<string> { "478d0fe2-4c18-e9ba-7218-3b9c80c002d3" };
        private List<string> VOIDBTNS = new List<string> { "fdad02dc-55c9-d7c1-7a62-c8b5e2f9695c" };
        private List<string> SETBTNS = new List<string> { "0B15CBD0-1AE4-B309-490F-0841A1789F46", "9918F8C0-D9DE-A206-5503-99F5E9028DB9", "8818F8C0-D9DE-A206-5503-99F5E9028DB9" };


        public List<BM_GTISUser> GTISUserTest()
        {
            var user = DbOpe_sysuser.Instance.GTISUserList();
            var userList = user.MappingTo<List<BM_GTISUser>>();
            return userList;
        }
        public List<int> GetUserStates(string userId)
        {
            List<int> r = new List<int>();
            //var roles = DbOpe_sys_userinrole.Instance.GetRoleByUserId(userId);
            //List<GetFormByUserId_Out> list = DbOpe_sys_form.Instance.GetFormByUserId(userId);
            var list = RedisCache.UserButtonRight.GetUserButtonRight(UserId)?.ToList();
            //foreach (var role in roles)
            //{
            if (list.Find(l => REGISTERBTNS.Contains(l.Id)) != null)
            {
                r.Add((int)EnumContractServiceOpenState.ToBeOpened);
                r.Add((int)EnumContractServiceOpenState.TobeChanged);
                r.Add((int)EnumContractServiceOpenState.ToBeReview);
                r.Add((int)EnumContractServiceOpenState.Refuse);
                r.Add((int)EnumContractServiceOpenState.Returned);
                r.Add((int)EnumContractServiceOpenState.Open);
            }
            if (list.Find(l => REVIEWRBTNS.Contains(l.Id)) != null)
            {
                r.Add((int)EnumContractServiceOpenState.ToBeReview);
                r.Add((int)EnumContractServiceOpenState.Returned);
                r.Add((int)EnumContractServiceOpenState.Open);
                r.Add((int)EnumContractServiceOpenState.OverDue);
                r.Add((int)EnumContractServiceOpenState.NearlyEnd);
                r.Add((int)EnumContractServiceOpenState.Void);
            }
            if (list.Find(l => VOIDBTNS.Contains(l.Id)) != null)
            {
                r.Add((int)EnumContractServiceOpenState.Open);
                r.Add((int)EnumContractServiceOpenState.ToBeReview);
                r.Add((int)EnumContractServiceOpenState.Returned);
                r.Add((int)EnumContractServiceOpenState.Void);
                //r.Add((int)EnumContractServiceOpenState.Refuse);
            }
            if (list.Find(l => SETBTNS.Contains(l.Id)) != null)
            {
                r.Add((int)EnumContractServiceOpenState.Open);
                r.Add((int)EnumContractServiceOpenState.OverDue);
                r.Add((int)EnumContractServiceOpenState.NearlyEnd);
            }
            //}
            return r.Distinct().ToList();
        }
        /// <summary>
        /// 对于所有申请：不可以同时重复申请
        /// 1、对于新申请开通，已经通过不可以再申请
        /// 2、对于续约申请，当前公司和被续约公司，有其他新开通/变更/续约申请（续约或被续约）时，都不能提续约申请
        /// 3、对于变更申请，以当前甲方公司为单位，有其他续约申请（续约或被续约）时，不能提变更申请；被续约了的合同，是不能再提变更申请的
        /// </summary>
        /// <param name="contractProductInfoId"></param>
        /// <param name="update"></param>
        /// <param name="exceptAppl"></param>
        /// <param name="renewFirstParty"></param>
        public void CheckAppl(string contractProductInfoId, bool update = false, string exceptAppl = "", string renewFirstParty = "")
        {
            //当前产品的合同
            Db_crm_contract targatContract = DbOpe_crm_contract.Instance.GetContractByContractProductInfoId(contractProductInfoId);
            //当前合同产品的历史开通申请（不算变更的）
            Db_crm_contract_productserviceinfo_gtis_appl appl = GetContractProductServiceInfoGtisApplByContractProductInfoId(contractProductInfoId, exceptAppl);
            if (targatContract.ContractType != (int)EnumContractType.ReNew && !update)
            {
                if (appl != null)
                {
                    //当前合同产品有过申请
                    if (appl.State == EnumProcessStatus.Submit.ToInt())
                    {
                        throw new ApiException("申请已经提交不可以重复申请");
                    }
                    if (appl.State == EnumProcessStatus.Pass.ToInt())
                    {
                        throw new ApiException("申请已经通过不可以重复申请");
                    }
                }
            }
            else if (targatContract.ContractType == (int)EnumContractType.ReNew && !update)
            {
                //续约
                if (appl != null)
                {
                    if (appl.State == EnumProcessStatus.Submit.ToInt())
                    {
                        throw new ApiException("申请已经提交不可以重复申请");
                    }
                }
                if (string.IsNullOrEmpty(renewFirstParty))
                {
                    throw new ApiException("续约公司不能为空");
                }
                var existCompanyAppl = Queryable
                    .LeftJoin<Db_crm_contract>((service, contract) => service.ContractId == contract.Id && contract.Deleted == false)
                    .Where((service, contract) => service.State == (int)EnumProcessStatus.Submit)
                    .Where((service, contract) => service.Deleted == false)
                    .Where((service, contract) => contract.FirstParty == targatContract.FirstParty || service.RenewFirstParty == targatContract.FirstParty)
                    .First();
                if (existCompanyAppl != null)
                {
                    throw new ApiException("当前甲方公司存在未审核的申请，不可以申请续约");
                }
                var existRenewCompanyAppl = Queryable
                .LeftJoin<Db_crm_contract>((service, contract) => service.ContractId == contract.Id && contract.Deleted == false)
                .Where((service, contract) => service.State == (int)EnumProcessStatus.Submit)
                .Where((service, contract) => service.Deleted == false)
                .Where((service, contract) => contract.FirstParty == renewFirstParty || service.RenewFirstParty == renewFirstParty)
                .First();
                if (existRenewCompanyAppl != null)
                {
                    throw new ApiException("当前被续约公司存在未审核的申请，不可以申请续约");
                }
            }
            else
            {
                //变更
                if (appl != null)
                {
                    if (appl.State == EnumProcessStatus.Submit.ToInt())
                    {
                        throw new ApiException("服务变更不可以重复申请");
                    }
                }
                var existCompanyRenewAppl = Queryable
                  .LeftJoin<Db_crm_contract>((service, contract) => service.ContractId == contract.Id && contract.Deleted == false)
                  .Where((service, contract) => service.State == (int)EnumProcessStatus.Submit)
                  .Where((service, contract) => contract.ContractType == (int)EnumContractType.ReNew)
                  .Where((service, contract) => contract.FirstParty == targatContract.FirstParty || service.RenewFirstParty == targatContract.FirstParty)
                  .Where((service, contract) => service.Deleted == false)
                  .First();
                if (existCompanyRenewAppl != null)
                {
                    throw new ApiException("当前甲方公司存在未审核的续约/被续约申请，不可以进行变更");
                }
                var renewCodeCheck = Db.Queryable<Db_crm_contract_serviceinfo_gtis>()
                    .Where(g => g.OldContractNum == targatContract.ContractNum)
                    .Where(g => g.IsApplHistory == false)
                    .Where(g => g.State != (int)EnumContractServiceState.VOID && g.State != (int)EnumContractServiceState.REFUSE && g.State != (int)EnumContractServiceState.INVALID)
                    .First();
                if (renewCodeCheck != null)
                {
                    throw new ApiException("当前合同产品已经续约，不可以进行变更");
                }
            }
        }
        public Db_crm_contract_productserviceinfo_gtis_appl AddContractProductServiceInfoGtisAppl(AddContractProductServiceInfoGtisAppl_In addContractProductServiceInfoGtisApplIn)
        {

            //旧的申请要置为无效，只保留最新的这个申请
            var oldAppls = Queryable.Where(a => a.ContractProductInfoId == addContractProductServiceInfoGtisApplIn.ContractProductInfoId && a.Deleted == false && a.IsInvalid == (int)EnumIsInvalid.Effective);
            oldAppls.ForEach(a =>
            {
                a.IsInvalid = (int)EnumIsInvalid.Invalid;
                Update(a);
            });
            Db_crm_contract_productinfo productinfo = DbOpe_crm_contract_productinfo.Instance.GetDataById(addContractProductServiceInfoGtisApplIn.ContractProductInfoId);
            Db_crm_contract_productserviceinfo_gtis_appl gtis_appl = addContractProductServiceInfoGtisApplIn.MappingTo<Db_crm_contract_productserviceinfo_gtis_appl>();
            gtis_appl.ProductId = productinfo.ProductId;
            gtis_appl.ContractId = productinfo.ContractId;
            gtis_appl.ApplicantId = TokenModel.Instance.id;
            gtis_appl.ApplicantDate = DateTime.Now;
            gtis_appl.State = EnumProcessStatus.Submit.ToInt();
            gtis_appl.ProcessingType = EnumProcessingType.Add.ToInt();
            gtis_appl.IsInvalid = EnumIsInvalid.Effective.ToInt();
            gtis_appl.Remark4List = addContractProductServiceInfoGtisApplIn.Remark;
            //保存优惠券
            if (addContractProductServiceInfoGtisApplIn.CounponDetailIdList != null && addContractProductServiceInfoGtisApplIn.CounponDetailIdList.Count > 0)
                gtis_appl.CouponIds = String.Join(',', addContractProductServiceInfoGtisApplIn.CounponDetailIdList);
            #region  这里处理默认值问题
            //这里的超级子账号数是套餐自带的
            var productInfo = DbOpe_crm_contract_productinfo.Instance.GetContractProductInfoById(addContractProductServiceInfoGtisApplIn.ContractProductInfoId);
            //这里的超级子账号数是另外买的
            List<ProductInfo_Out> SuperSubAccountList = DbOpe_crm_product_supersubaccount.Instance.GetProductSuperSubAccountByContractId(productinfo.ContractId);
            int review_SuperSubAccount = 0;
            if (SuperSubAccountList.Count > 0)
            {
                review_SuperSubAccount = SuperSubAccountList.First().SuperSubAccountNum.Value;
            }
            //子账号国家授权数默认 = 超级子账号数  或者  1;
            if (review_SuperSubAccount == 0)
            {
                gtis_appl.AuthorizationNum = (productInfo.SuperSubAccountNum == null || productInfo.SuperSubAccountNum.Value == 0) ? 1 : productInfo.SuperSubAccountNum.Value;
            }
            else
            {
                gtis_appl.AuthorizationNum = (productInfo.SuperSubAccountNum == null || productInfo.SuperSubAccountNum.Value == 0) ? review_SuperSubAccount : (review_SuperSubAccount + productInfo.SuperSubAccountNum.Value);
            }
            #endregion

            #region 校验RenewContractNum是否应该传入并校对
            var contract = DbOpe_crm_contract.Instance.GetContractById(gtis_appl.ContractId);
            if (contract.ContractType == EnumContractType.New.ToInt())
            {
                gtis_appl.RenewContractNum = null;
                gtis_appl.RenewFirstParty = null;
            }
            else if (contract.ContractType == EnumContractType.AddItem.ToInt())
            {
                gtis_appl.RenewContractNum = null;
                gtis_appl.RenewFirstParty = null;
            }
            else if (contract.ContractType == EnumContractType.ReNew.ToInt())
            {
                gtis_appl.RenewContractNum = contract.RenewalContractNum;
                gtis_appl.RenewFirstParty = DbOpe_crm_contract.Instance.GetContractByContractNum(contract.RenewalContractNum).FirstParty;
            }
            #endregion

            #region 销售经理赠送服务天数
            if (addContractProductServiceInfoGtisApplIn.PerlongServiceDays.GetValueOrDefault(0) > 0)
            {
                //获取销售经理当月可用个人服务时间
                var duration = BLL_PrivateService.Instance.GetDuration();
                if (addContractProductServiceInfoGtisApplIn.PrivateServiceDays.GetValueOrDefault(0) > duration.Duration)
                    throw new ApiException("个人服务天数超出当月可使用上限");
                //计算超额服务天数
                var overServiceDays = addContractProductServiceInfoGtisApplIn.PerlongServiceDays.GetValueOrDefault(0) - addContractProductServiceInfoGtisApplIn.PrivateServiceDays.GetValueOrDefault(0);
                //计算的超额服务天数大于当前人员可使用的超额服务天数上限，弹出异常
                if (overServiceDays > duration.ReleaseChargeDay)
                    throw new ApiException("超额服务天数超出当月可使用上限");
                //判断续约合同的服务是否已经中断超过6个月
                if (contract.ContractType == EnumContractType.ReNew.ToInt())
                {
                    var renewService = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetGtisServiceInfoByContractNum(gtis_appl.RenewContractNum);
                    if (renewService.ServiceCycleEnd.Value.AddMonths(6) < DateTime.Now.Date)
                        throw new ApiException("服务中断时间已超过6个月，无法使用个人服务天数");
                }
                //若计算的自费天数与传入的不符，弹出异常
                if (overServiceDays != addContractProductServiceInfoGtisApplIn.OverServiceDays)
                    throw new ApiException("超额服务天数与实际情况不符，请重新填写");
            }
            #endregion

            Guid ProductServiceInfoGtisApplId = InsertDataReturnId(gtis_appl);
            gtis_appl.Id = ProductServiceInfoGtisApplId.ToString();
            //如果PerlongServiceDays大于0，需要登记个人使用时间
            if (addContractProductServiceInfoGtisApplIn.PerlongServiceDays.GetValueOrDefault(0) > 0)
            {
                //登记使用个人使用时间,将对应天数改为使用中
                BLL_PrivateService.Instance.UsingPrivateServiceForContractService(gtis_appl.ContractId, EnumPrivateServiceType.Using, gtis_appl.Id, addContractProductServiceInfoGtisApplIn.PrivateServiceDays.GetValueOrDefault(0), addContractProductServiceInfoGtisApplIn.OverServiceDays.GetValueOrDefault(0));
            }


            List<Db_crm_contract_productserviceinfo_gtis_appl_country> CountryList = new List<Db_crm_contract_productserviceinfo_gtis_appl_country>();
            foreach (GtisApplCountry g in addContractProductServiceInfoGtisApplIn.GtisApplCountry)
            {
                Db_crm_contract_productserviceinfo_gtis_appl_country Gtis_Appl_Country = new Db_crm_contract_productserviceinfo_gtis_appl_country();
                Gtis_Appl_Country.Sid = g.Sid;
                Gtis_Appl_Country.ProductServiceInfoGtisApplId = gtis_appl.Id;
                CountryList.Add(Gtis_Appl_Country);
            }
            DbOpe_crm_contract_productserviceinfo_gtis_appl_country.Instance.InsertListData(CountryList);

            /*
            List<Db_crm_contract_productserviceinfo_gtis_appl_residentcountry> ResidentCountryList = new List<Db_crm_contract_productserviceinfo_gtis_appl_residentcountry>();
            if (addContractProductServiceInfoGtisApplIn.GtisApplResidentCountry != null && addContractProductServiceInfoGtisApplIn.GtisApplResidentCountry.Count > 0)
            {
                foreach (GtisApplResidentCountry g in addContractProductServiceInfoGtisApplIn.GtisApplResidentCountry)
                {
                    Db_crm_contract_productserviceinfo_gtis_appl_residentcountry Gtis_Appl_ResidentCountry = new Db_crm_contract_productserviceinfo_gtis_appl_residentcountry();
                    Gtis_Appl_ResidentCountry.ResidentCountry = g.ResidentCountry;
                    Gtis_Appl_ResidentCountry.ProductServiceInfoGtisApplId = gtis_appl.Id;
                    ResidentCountryList.Add(Gtis_Appl_ResidentCountry);
                }
                DbOpe_crm_contract_productserviceinfo_gtis_appl_residentcountry.Instance.InsertListData(ResidentCountryList);
            }

            List<Db_crm_contract_productserviceinfo_gtis_appl_residentcity> ResidentCityList = new List<Db_crm_contract_productserviceinfo_gtis_appl_residentcity>();
            if (addContractProductServiceInfoGtisApplIn.GtisApplResidentCity != null && addContractProductServiceInfoGtisApplIn.GtisApplResidentCity.Count > 0)
            {
                foreach (GtisApplResidentCity g in addContractProductServiceInfoGtisApplIn.GtisApplResidentCity)
                {
                    Db_crm_contract_productserviceinfo_gtis_appl_residentcity Gtis_Appl_ResidentCity = new Db_crm_contract_productserviceinfo_gtis_appl_residentcity();
                    Gtis_Appl_ResidentCity.ResidentCity = g.ResidentCity;
                    Gtis_Appl_ResidentCity.ProductServiceInfoGtisApplId = gtis_appl.Id;
                    ResidentCityList.Add(Gtis_Appl_ResidentCity);
                }
                DbOpe_crm_contract_productserviceinfo_gtis_appl_residentcity.Instance.InsertListData(ResidentCityList);
            }*/


            //校验优惠券是否作废或过期

            //使用优惠券
            if (addContractProductServiceInfoGtisApplIn.CounponDetailIdList != null && addContractProductServiceInfoGtisApplIn.CounponDetailIdList.Count > 0)
            {
                if (DbOpe_crm_customer_coupon.Instance.CheckCouponListExistExpire(addContractProductServiceInfoGtisApplIn.CounponDetailIdList))
                    throw new ApiException("优惠券失效，请重新申请");
                if (DbOpe_crm_customer_coupon.Instance.ChangeCouponType(addContractProductServiceInfoGtisApplIn.CounponDetailIdList, EnumCouponType.Using).state != 1)
                    throw new ApiException("优惠券无法使用，请重新核对");
            }
            return gtis_appl;
        }
        public Db_crm_contract_productserviceinfo_gtis_appl UpdateContractProductServiceInfoGtisAppl(UpdateContractProductServiceInfoGtisAppl_In updateContractProductServiceInfoGtisAppl_In)
        {
            if (updateContractProductServiceInfoGtisAppl_In.ChangeReasonList == null || updateContractProductServiceInfoGtisAppl_In.ChangeReasonList.Count == 0)
                throw new ApiException("请选择变更原因");
            //旧的申请要置为无效，只保留最新的这个申请
            var oldAppls = Queryable.Where(a => a.ContractProductInfoId == updateContractProductServiceInfoGtisAppl_In.ContractProductInfoId && a.Deleted == false && a.IsInvalid == (int)EnumIsInvalid.Effective).ToList();
            oldAppls.ForEach(a =>
            {
                a.IsInvalid = (int)EnumIsInvalid.Invalid;
                Update(a);
            });

            Db_crm_contract_productinfo productinfo = DbOpe_crm_contract_productinfo.Instance.GetDataById(updateContractProductServiceInfoGtisAppl_In.ContractProductInfoId);
            Db_crm_product product = DbOpe_crm_product.Instance.QueryByPrimaryKey(productinfo.ProductId);
            var gtis_serve = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetGtisServiceByContractProductInfoId(updateContractProductServiceInfoGtisAppl_In.ContractProductInfoId);

            Db_crm_contract_productserviceinfo_gtis_appl gtis_appl = updateContractProductServiceInfoGtisAppl_In.MappingTo<Db_crm_contract_productserviceinfo_gtis_appl>();
            gtis_appl.ProductId = productinfo.ProductId;
            gtis_appl.ContractId = productinfo.ContractId;
            gtis_appl.ApplicantId = TokenModel.Instance.id;
            gtis_appl.ApplicantDate = DateTime.Now;
            gtis_appl.State = EnumProcessStatus.Submit.ToInt();
            gtis_appl.ProcessingType = EnumProcessingType.Change.ToInt();
            gtis_appl.IsInvalid = EnumIsInvalid.Effective.ToInt();
            gtis_appl.SubAccountsNum = updateContractProductServiceInfoGtisAppl_In.UpdateContractProductServiceInfoGtisApplUser_In.FindAll(u => u.AccountType != (int)EnumGtisAccountType.Main).Count;
            gtis_appl.SharePeopleNum = updateContractProductServiceInfoGtisAppl_In.UpdateContractProductServiceInfoGtisApplUser_In.Max(u => u.SharePeopleNum);
            gtis_appl.AuthorizationNum = updateContractProductServiceInfoGtisAppl_In.UpdateContractProductServiceInfoGtisApplUser_In.Find(u => u.AccountType == (int)EnumGtisAccountType.Main).AuthorizationNum;
            gtis_appl.AuthorizationNum = gtis_appl.AuthorizationNum == null ? 1 : gtis_appl.AuthorizationNum.Value;
            gtis_appl.Remark4List = updateContractProductServiceInfoGtisAppl_In.Remark;
            gtis_appl.ServiceMonth = gtis_appl.ServiceAddMonth == null ? gtis_serve.ServiceMonth : gtis_serve.ServiceMonth + gtis_appl.ServiceAddMonth;
            gtis_appl.ChangeReasonEnums = String.Join(',', updateContractProductServiceInfoGtisAppl_In.ChangeReasonList.Select(e => e.ToInt()).ToList());
            #region 销售经理赠送服务天数
            if (updateContractProductServiceInfoGtisAppl_In.PerlongServiceDays.GetValueOrDefault(0) > 0)
            {
                //获取销售经理当月可用个人服务时间
                var duration = BLL_PrivateService.Instance.GetDuration();
                if (updateContractProductServiceInfoGtisAppl_In.PrivateServiceDays.GetValueOrDefault(0) > duration.Duration)
                    throw new ApiException("个人服务天数超出当月可使用上限");
                //计算超额服务天数
                var overServiceDays = updateContractProductServiceInfoGtisAppl_In.PerlongServiceDays.GetValueOrDefault(0) - updateContractProductServiceInfoGtisAppl_In.PrivateServiceDays.GetValueOrDefault(0);
                //计算的超额服务天数大于当前人员可使用的超额服务天数上限，弹出异常
                if (overServiceDays > duration.ReleaseChargeDay)
                    throw new ApiException("超额服务天数超出当月可使用上限");
                //判断被变更的服务是否停服已超过6个月
                var oldAppl = oldAppls.Where(e => e.State == (int)EnumProcessStatus.Pass).First();
                var oldService = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetGtisServiceByApplyId(oldAppl.Id);
                if (oldService.ServiceCycleEnd.Value.AddMonths(6) < DateTime.Now.Date)
                    throw new ApiException("服务中断时间已超过6个月，无法使用个人服务天数");
                //若计算的自费天数与传入的不符，弹出异常
                if (overServiceDays != updateContractProductServiceInfoGtisAppl_In.OverServiceDays.GetValueOrDefault(0))
                    throw new ApiException("超额服务天数与实际情况不符，请重新填写");
            }
            #endregion
            //保存优惠券
            if (updateContractProductServiceInfoGtisAppl_In.CounponDetailIdList != null && updateContractProductServiceInfoGtisAppl_In.CounponDetailIdList.Count > 0)
                gtis_appl.CouponIds = String.Join(',', updateContractProductServiceInfoGtisAppl_In.CounponDetailIdList);
            //gtis_appl.ChangeProject = string.Join(',', updateContractProductServiceInfoGtisAppl_In.ChangeProjectType);

            //定义更新后的国家列表和用户列表
            var upd_Country = updateContractProductServiceInfoGtisAppl_In.GtisApplCountry;
            var upd_UserList = updateContractProductServiceInfoGtisAppl_In.UpdateContractProductServiceInfoGtisApplUser_In;
            var upd_changeReasonList = updateContractProductServiceInfoGtisAppl_In.ChangeReasonList;
            //如果变更原因不包含[变更服务内容]和[尾款申请剩余服务],则前端传入的除服务时间外的其他信息可能不准确,于是国家列表和用户列表需要使用原服务的信息
            if (!upd_changeReasonList.Contains(EnumGtisServiceChangeProject.ChangeServiceContent) && !upd_changeReasonList.Contains(EnumGtisServiceChangeProject.ApplyResidualService))
            {
                //环球搜账号数量
                gtis_appl.GlobalSearchAccountCount = gtis_serve.GlobalSearchAccountCount;
                //定制报告次数
                gtis_appl.WordRptMaxTimes = gtis_serve.WordRptMaxTimes;
                //是否开通定制报告
                gtis_appl.WordRptPermissions = gtis_serve.WordRptPermissions == true;
                //禁止下载导出权限
                gtis_appl.ForbidSearchExport = gtis_serve.ForbidSearchExport == true;
                //账号列表，账号状态需要筛选，与变更申请时显示的账号状态一致
                upd_UserList = DbOpe_crm_contract_serviceinfo_gtis_user.Instance.GetDataList(gtis_serve.Id).Where(e => e.OpeningStatus != (int)EnumGtisUserOpeningStatus.NotFound && e.OpeningStatus != (int)EnumGtisUserOpeningStatus.INPROCESS && e.OpeningStatus != (int)EnumGtisUserOpeningStatus.BackManageStop).ToList().MappingTo<List<UpdateContractProductServiceInfoGtisApplUser_In>>();
                //国家列表
                upd_Country = DbOpe_crm_contract_serviceinfo_gtis_country.Instance.GetGtisCountryListByServeId(gtis_serve.Id).Select(e => e.MappingTo<GtisApplCountry>()).ToList();
                //服务中的国家列表只保存零售国家，需要加上套餐国家
                upd_Country.AddRange(BLL_G4DbNames.Instance.GetG4DbNames(gtis_serve.ProductId).Select(e => e.MappingTo<GtisApplCountry>()).ToList());
            }

            //创建服务申请数据,记录返回的申请Id
            Guid ProductServiceInfoGtisApplId = InsertDataReturnId(gtis_appl);
            gtis_appl.Id = ProductServiceInfoGtisApplId.ToString();
            //如果PerlongServiceDays大于0，需要登记个人使用时间
            if (updateContractProductServiceInfoGtisAppl_In.PerlongServiceDays.GetValueOrDefault(0) > 0)
            {
                //登记使用个人使用时间,将对应天数改为使用中
                BLL_PrivateService.Instance.UsingPrivateServiceForContractService(gtis_appl.ContractId, EnumPrivateServiceType.Using, gtis_appl.Id, updateContractProductServiceInfoGtisAppl_In.PrivateServiceDays.GetValueOrDefault(0), updateContractProductServiceInfoGtisAppl_In.OverServiceDays.GetValueOrDefault(0));
            }

            List<Db_crm_contract_productserviceinfo_gtis_appl_country> CountryList = new List<Db_crm_contract_productserviceinfo_gtis_appl_country>();
            foreach (GtisApplCountry g in upd_Country)
            {
                Db_crm_contract_productserviceinfo_gtis_appl_country Gtis_Appl_Country = new Db_crm_contract_productserviceinfo_gtis_appl_country();
                Gtis_Appl_Country.Sid = g.Sid;
                Gtis_Appl_Country.ProductServiceInfoGtisApplId = gtis_appl.Id;
                CountryList.Add(Gtis_Appl_Country);
            }
            DbOpe_crm_contract_productserviceinfo_gtis_appl_country.Instance.InsertListData(CountryList);

            List<Db_crm_contract_productserviceinfo_gtis_appl_user> UserList = new List<Db_crm_contract_productserviceinfo_gtis_appl_user>();
            List<Db_crm_contract_serviceinfo_gtis_user> EffectUserList = DbOpe_crm_contract_serviceinfo_gtis_user.Instance.GetUserData(productinfo.ProductId, productinfo.ContractId);
            foreach (var u in upd_UserList)
            {
                var effectUser = EffectUserList.Find(u => u.UserId == u.UserId);
                Db_crm_contract_productserviceinfo_gtis_appl_user Gtis_Appl_User = new Db_crm_contract_productserviceinfo_gtis_appl_user();
                if (effectUser != null)
                {
                    //更新的账户
                    effectUser.MappingTo(Gtis_Appl_User);
                    u.MappingTo(Gtis_Appl_User);
                    Gtis_Appl_User.ContractProductServiceInfoGtisApplId = gtis_appl.Id;
                }
                else
                {
                    //新增的账户
                    Gtis_Appl_User.ContractProductServiceInfoGtisApplId = gtis_appl.Id;
                    Gtis_Appl_User.AuthorizationNum = gtis_appl.AuthorizationNum;
                }
                UserList.Add(Gtis_Appl_User);
            }
            DbOpe_crm_contract_productserviceinfo_gtis_appl_user.Instance.InsertListData(UserList);
            #region 注释 不再存储常驻国家和常驻城市
            /*if (updateContractProductServiceInfoGtisAppl_In.GtisApplResidentCountry != null)
            {
                List<Db_crm_contract_productserviceinfo_gtis_appl_residentcountry> ResidentCountryList = new List<Db_crm_contract_productserviceinfo_gtis_appl_residentcountry>();
                foreach (GtisApplResidentCountry g in updateContractProductServiceInfoGtisAppl_In.GtisApplResidentCountry)
                {
                    Db_crm_contract_productserviceinfo_gtis_appl_residentcountry Gtis_Appl_ResidentCountry = new Db_crm_contract_productserviceinfo_gtis_appl_residentcountry();
                    Gtis_Appl_ResidentCountry.ResidentCountry = g.ResidentCountry;
                    Gtis_Appl_ResidentCountry.ProductServiceInfoGtisApplId = gtis_appl.Id;
                    ResidentCountryList.Add(Gtis_Appl_ResidentCountry);
                }
                DbOpe_crm_contract_productserviceinfo_gtis_appl_residentcountry.Instance.InsertListData(ResidentCountryList);
            }
            if (updateContractProductServiceInfoGtisAppl_In.GtisApplResidentCity != null)
            {
                List<Db_crm_contract_productserviceinfo_gtis_appl_residentcity> ResidentCityList = new List<Db_crm_contract_productserviceinfo_gtis_appl_residentcity>();
                foreach (GtisApplResidentCity g in updateContractProductServiceInfoGtisAppl_In.GtisApplResidentCity)
                {
                    Db_crm_contract_productserviceinfo_gtis_appl_residentcity Gtis_Appl_ResidentCity = new Db_crm_contract_productserviceinfo_gtis_appl_residentcity();
                    Gtis_Appl_ResidentCity.ResidentCity = g.ResidentCity;
                    Gtis_Appl_ResidentCity.ProductServiceInfoGtisApplId = gtis_appl.Id;
                    ResidentCityList.Add(Gtis_Appl_ResidentCity);
                }
                DbOpe_crm_contract_productserviceinfo_gtis_appl_residentcity.Instance.InsertListData(ResidentCityList);
            }*/
            #endregion 
            //使用优惠券
            if (updateContractProductServiceInfoGtisAppl_In.CounponDetailIdList != null && updateContractProductServiceInfoGtisAppl_In.CounponDetailIdList.Count > 0)
            {
                if (DbOpe_crm_customer_coupon.Instance.ChangeCouponType(updateContractProductServiceInfoGtisAppl_In.CounponDetailIdList, EnumCouponType.Using).state != 1)
                    throw new ApiException("优惠券无法使用，请重新核对");
            }

            #region 记录变更项及变更内容
            int changeProjectNum = 0;
            var hasVip = DbOpe_crm_contract_productinfo.Instance.CheckContractHasVIP(gtis_appl.ContractId);
            var changeAbleCols = new List<GtisChangeItems>()
            {
                new GtisChangeItems(){PropKey = "GlobalSearchAccountCount", PropName = "环球搜账号数量",IsArray = false },
                new GtisChangeItems(){PropKey = "ServiceCycleStart", PropName = "服务开始时间",IsArray = false },
                new GtisChangeItems(){PropKey = "ServiceCycleEnd", PropName = "服务结束时间",IsArray = false },
                new GtisChangeItems(){PropKey = "ServiceMonth", PropName = "服务月份",IsArray = false },
                new GtisChangeItems(){PropKey = "ShareUsageNum", PropName = "共享使用总数",IsArray = false },
                new GtisChangeItems(){PropKey = "ForbidSearchExport", PropName = "禁用下载/导出权限",IsArray = false },
                new GtisChangeItems(){PropKey = "WordRptPermissions", PropName = "是否开通定制报告",IsArray = false },
                new GtisChangeItems(){PropKey = "WordRptMaxTimes", PropName = "定制报告每年总次数",IsArray = false },
                new GtisChangeItems(){PropKey = "GtisUserList", PropName = "账户信息",IsArray = true },
                new GtisChangeItems(){PropKey = "GtisApplCountry", PropName = "开通国家",IsArray = true }
            };

            foreach (var col in changeAbleCols)
            {
                var appl_propKey = col.PropKey;
                var recordChagneItem4Array = false;
                var changeSidContent = string.Empty;
                if (!col.IsArray)
                {
                    if (col.PropKey == "ServiceCycleStart" && (product.ProductType == (int)EnumProductType.Gtis || product.ProductType == (int)EnumProductType.Vip))
                        appl_propKey = "ServiceCycleStartAfterDiscount";
                    if (col.PropKey == "ServiceCycleEnd" && (product.ProductType == (int)EnumProductType.Gtis || product.ProductType == (int)EnumProductType.Vip))
                        appl_propKey = "ServiceCycleEndAfterDiscount";
                    if (col.PropKey == "ServiceMonth" && (product.ProductType == (int)EnumProductType.Gtis || product.ProductType == (int)EnumProductType.Vip))
                        appl_propKey = "ServiceMonthAfterDiscount";
                    var applyV = gtis_appl.GetType().GetProperty(appl_propKey).GetValue(gtis_appl, null);
                    var serveV = gtis_serve.GetType().GetProperty(col.PropKey).GetValue(gtis_serve, null);
                    var hasChangedFlag = false;
                    if (applyV == null && serveV != null)
                        hasChangedFlag = true;
                    else if (applyV != null && serveV == null)
                        hasChangedFlag = true;
                    else if (applyV != null && serveV != null && applyV.ToString() != serveV.ToString())
                        hasChangedFlag = true;
                    if (hasChangedFlag)
                    {
                        var newItem = new Db_crm_contract_productserviceinfo_gtis_appl_change_project()
                        {
                            ProductServiceInfoGtisApplId = gtis_appl.Id,
                            ChangePropertyKey = col.PropKey,
                            ChangePropertyName = col.PropName,
                            IsArray = col.IsArray,
                            OriginValue = serveV == null ? "" : serveV.ToString(),
                            ChangedValue = applyV == null ? "" : applyV.ToString(),
                        };
                        DbOpe_crm_contract_productserviceinfo_gtis_appl_change_project.Instance.InsertData(newItem);
                        changeProjectNum++;
                    }
                }
                else if (col.PropKey == "GtisUserList" && (upd_changeReasonList.Contains(EnumGtisServiceChangeProject.ChangeServiceContent) || upd_changeReasonList.Contains(EnumGtisServiceChangeProject.ApplyResidualService)))
                {
                    var gtisServeUserList = DbOpe_crm_contract_serviceinfo_gtis_user.Instance.GetDataList(gtis_serve.Id);
                    var changeUserList = updateContractProductServiceInfoGtisAppl_In.UpdateContractProductServiceInfoGtisApplUser_In;
                    if (gtisServeUserList.Count != changeUserList.Count())
                        recordChagneItem4Array = true;
                    else if (changeUserList.Any(e => string.IsNullOrEmpty(e.UserId)))
                        recordChagneItem4Array = true;
                    else
                    {
                        foreach (var gtisUser in gtisServeUserList)
                        {
                            var changeUser = changeUserList.Find(e => e.UserId == gtisUser.UserId);
                            if (gtisUser.SharePeopleNum != changeUser.SharePeopleNum)
                            {
                                recordChagneItem4Array = true;
                                break;
                            }
                        };
                    }
                }
                else if (col.PropKey == "GtisApplCountry" && hasVip && (upd_changeReasonList.Contains(EnumGtisServiceChangeProject.ChangeServiceContent) || upd_changeReasonList.Contains(EnumGtisServiceChangeProject.ApplyResidualService)))
                {
                    var gtisCountrySidList = DbOpe_crm_contract_serviceinfo_gtis_country.Instance.GetGtisCountryListByServeId(gtis_serve.Id).Select(e => e.Sid.GetValueOrDefault(0)).ToList();
                    gtisCountrySidList.AddRange(BLL_G4DbNames.Instance.GetG4DbNames(gtis_serve.ProductId).Select(e => e.SID.GetValueOrDefault(0)).ToList());
                    var changeSidList = upd_Country.Select(e => e.Sid).ToList();
                    /*if (gtisCountrySidList.Count != changeSidList.Count)
                        recordChagneItem4Array = true;*/
                    var delSidList = gtisCountrySidList.Except(changeSidList).ToList();
                    var addSidList = changeSidList.Except(gtisCountrySidList).ToList();
                    var contentJo = new JObject();
                    if (delSidList != null && delSidList.Count > 0)
                    {
                        recordChagneItem4Array = true;
                        contentJo.Add("DelSidList", "[" + string.Join(',', delSidList) + "]");
                    }
                    if (addSidList != null && addSidList.Count > 0)
                    {
                        recordChagneItem4Array = true;
                        contentJo.Add("AddSidList", "[" + string.Join(',', addSidList) + "]");
                    }
                    changeSidContent = JsonConvert.SerializeObject(contentJo);
                }

                if (recordChagneItem4Array)
                {
                    var newItem = new Db_crm_contract_productserviceinfo_gtis_appl_change_project()
                    {
                        ProductServiceInfoGtisApplId = gtis_appl.Id,
                        ChangePropertyKey = appl_propKey,
                        ChangePropertyName = col.PropName,
                        ChangedValue = changeSidContent,
                        IsArray = col.IsArray,
                    };
                    DbOpe_crm_contract_productserviceinfo_gtis_appl_change_project.Instance.InsertData(newItem);
                    changeProjectNum++;
                }
            }
            if (changeProjectNum == 0)
                throw new ApiException(gtis_serve.ContractNum.ToString() + "监测未进行服务内容变更,无法提交申请");
            #endregion
            return gtis_appl;
        }
        /// <summary>
        /// 赠送GTIS服务时长（相当于做一次服务变更，变更服务时长）
        /// </summary>
        /// <param name="oldGtis"></param>
        /// <param name="addFreeProductServices_GTIS_In"></param>
        /// <returns></returns>
        public Db_crm_contract_productserviceinfo_gtis_appl AddFreeContractProductServiceInfoGtisAppl(GtisInfo_OUT oldGtis, AddFreeProductServices_GTIS_In addFreeProductServices_GTIS_In)
        {
            //旧的申请要置为无效，只保留最新的这个申请
            var oldAppls = Queryable.Where(a => a.ContractProductInfoId == oldGtis.ContractProductInfoId && a.Deleted == false && a.IsInvalid == (int)EnumIsInvalid.Effective).ToList();
            oldAppls.ForEach(a =>
            {
                a.IsInvalid = (int)EnumIsInvalid.Invalid;
                Update(a);
            });
            #region 申请
            //申请表
            Db_crm_contract_productserviceinfo_gtis_appl gtis_appl = oldGtis.MappingTo<Db_crm_contract_productserviceinfo_gtis_appl>();
            gtis_appl.IsFree = true;
            gtis_appl.ServiceMonth = gtis_appl.ServiceMonth + addFreeProductServices_GTIS_In.AddServiceMonth;
            gtis_appl.ServiceAddMonth = addFreeProductServices_GTIS_In.AddServiceMonth;
            gtis_appl.ServiceCycleEnd = addFreeProductServices_GTIS_In.NewServiceCycleEnd;
            gtis_appl.ApplicantId = TokenModel.Instance.id;
            gtis_appl.ApplicantDate = DateTime.Now;
            gtis_appl.State = EnumProcessStatus.Submit.ToInt();
            gtis_appl.ProcessingType = EnumProcessingType.Change.ToInt();
            gtis_appl.IsInvalid = EnumIsInvalid.Effective.ToInt();
            gtis_appl.UpdateDate = null;
            gtis_appl.UpdateUser = null;
            gtis_appl.Id = Guid.NewGuid().ToString();
            gtis_appl.CouponIds = String.Empty;
            //Guid ProductServiceInfoGtisApplId = InsertDataReturnId(gtis_appl);
            //gtis_appl.Id = ProductServiceInfoGtisApplId.ToString();
            Insert(gtis_appl);
            //申请表-国家
            List<Db_crm_contract_productserviceinfo_gtis_appl_country> CountryList = new List<Db_crm_contract_productserviceinfo_gtis_appl_country>();
            foreach (GtisRetailCountry_OUT g in oldGtis.GtisRetailCountry)
            {
                Db_crm_contract_productserviceinfo_gtis_appl_country Gtis_Appl_Country = new Db_crm_contract_productserviceinfo_gtis_appl_country();
                Gtis_Appl_Country.Sid = g.Sid;
                Gtis_Appl_Country.ProductServiceInfoGtisApplId = gtis_appl.Id;
                Gtis_Appl_Country.UpdateDate = null;
                Gtis_Appl_Country.UpdateUser = null;
                CountryList.Add(Gtis_Appl_Country);
            }
            DbOpe_crm_contract_productserviceinfo_gtis_appl_country.Instance.InsertListData(CountryList);
            //申请表-用户
            List<Db_crm_contract_productserviceinfo_gtis_appl_user> UserList = new List<Db_crm_contract_productserviceinfo_gtis_appl_user>();
            foreach (var u in oldGtis.GtisUserInfo)
            {
                Db_crm_contract_productserviceinfo_gtis_appl_user Gtis_Appl_User = new Db_crm_contract_productserviceinfo_gtis_appl_user();
                u.MappingTo(Gtis_Appl_User);
                Gtis_Appl_User.ContractProductServiceInfoGtisApplId = gtis_appl.Id;
                Gtis_Appl_User.UpdateDate = null;
                Gtis_Appl_User.UpdateUser = null;
                UserList.Add(Gtis_Appl_User);
            }
            DbOpe_crm_contract_productserviceinfo_gtis_appl_user.Instance.InsertListData(UserList);
            //申请表-常驻国家
            List<Db_crm_contract_productserviceinfo_gtis_appl_residentcountry> ResidentCountryList = new List<Db_crm_contract_productserviceinfo_gtis_appl_residentcountry>();
            foreach (GtisResidentCountry g in oldGtis.ResidentCountries)
            {
                Db_crm_contract_productserviceinfo_gtis_appl_residentcountry Gtis_Appl_ResidentCountry = new Db_crm_contract_productserviceinfo_gtis_appl_residentcountry();
                Gtis_Appl_ResidentCountry.ResidentCountry = g.ResidentCountry;
                Gtis_Appl_ResidentCountry.ProductServiceInfoGtisApplId = gtis_appl.Id;
                Gtis_Appl_ResidentCountry.UpdateDate = null;
                Gtis_Appl_ResidentCountry.UpdateUser = null;
                ResidentCountryList.Add(Gtis_Appl_ResidentCountry);
            }
            DbOpe_crm_contract_productserviceinfo_gtis_appl_residentcountry.Instance.InsertListData(ResidentCountryList);
            //申请表-常驻城市
            List<Db_crm_contract_productserviceinfo_gtis_appl_residentcity> ResidentCityList = new List<Db_crm_contract_productserviceinfo_gtis_appl_residentcity>();
            foreach (GtisResidentCity g in oldGtis.ResidentCitys)
            {
                Db_crm_contract_productserviceinfo_gtis_appl_residentcity Gtis_Appl_ResidentCity = new Db_crm_contract_productserviceinfo_gtis_appl_residentcity();
                Gtis_Appl_ResidentCity.ResidentCity = g.ResidentCity;
                Gtis_Appl_ResidentCity.ProductServiceInfoGtisApplId = gtis_appl.Id;
                Gtis_Appl_ResidentCity.UpdateDate = null;
                Gtis_Appl_ResidentCity.UpdateUser = null;
                ResidentCityList.Add(Gtis_Appl_ResidentCity);
            }
            DbOpe_crm_contract_productserviceinfo_gtis_appl_residentcity.Instance.InsertListData(ResidentCityList);
            #endregion
            return gtis_appl;


        }
        public Db_crm_contract_productserviceinfo_gtis_appl GetContractProductServiceInfoGtisApplById(string id, bool dataOwner = false)
        {
            //Db_crm_contract_productserviceinfo_gtis_appl result = Queryable
            //    .Includes(r => r.Countrys)
            //    .Where(r => r.Id == id && r.Deleted == false)
            //    .First();
            //return new ContractProductServiceInfoGtisAppl_Out()
            //{
            //    Id = result.Id,
            //    GtisApplCountry = result.Countrys.Select(r => new GtisApplCountry_Out() { Id = r.Id, Sid = r.Sid.Value }).ToList()
            //};
            //.Select(r => new ContractProductServiceInfoGtisAppl_Out()
            //{
            //    Id = r.Id.SelectAll(),
            //    GtisApplCountry = r.Countrys.Select(r => new GtisApplCountry_Out() { Id = r.Id, Sid = r.Sid.Value }).ToList()
            //}).First();

            return Queryable
                .LeftJoin<Db_crm_contract>((r, c) => r.ContractId == c.Id)
                .LeftJoinIF<Db_v_customer_subcompany_private_user>(dataOwner, ((r, c, cspu) => c.FirstParty == cspu.Id))
                .Includes(r => r.Countrys)
                .Includes(r => r.ResidentCitys)
                .Includes(r => r.ResidentCountrys)
                .Where((r) => r.Id == id && r.Deleted == false)
                //.Where(DataOwner.BusinessData("crm_contract_productserviceinfo_gtis_appl.CreateUser", "crm_contract_productserviceinfo_gtis_appl.Id")).First();
                .WhereIF(dataOwner, DataOwner.BusinessData("r.CreateUser", "r.Id", "cspu.CompanyCurrentUser")).First();
        }
        /// <summary>
        /// 检查哪些可以赠送
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public CheckGtisFreeAble_OUT CheckGtisFreeAble(string id)
        {

            CheckGtisFreeAble_OUT d = new CheckGtisFreeAble_OUT();
            var appl = GetApplGtisInfo(id);
            #region 是否可以赠送
            //LogUtil.AddLog("checkfree start:" + DateTime.Now.ToString("mm-dd HH:mm:ss:fff"));
            if (appl.IsFree == true || appl.IsInvalid != (int)EnumIsInvalid.Effective || appl.State != (int)EnumProcessStatus.Pass)
            {
                //gtis赠送过了，所有都不能再送了
                d.GTISFreeAble = false;
                d.CollegeFreeAble = false;
                d.GlobalFreeAble = false;
            }
            else
            {
                d.GTISFreeAble = true;
                //if (DbOpe_crm_contract_productserviceinfo_college_appl.Instance.CheckContractHasCollegeProduct(appl.ContractId))
                if (DbOpe_crm_contract_productinfo.Instance.CheckContractHasCollegeProduct(appl.ContractId))
                {
                    d.CollegeFreeAble = false;
                }
                else
                {
                    if (DbOpe_crm_contract_productserviceinfo_college_appl.Instance.CheckContractHasPresentedCollegeProduct(appl.ContractId))
                    {
                        //慧思学院赠送过了，所有都不能再送了
                        d.GTISFreeAble = false;
                        d.CollegeFreeAble = false;
                        d.GlobalFreeAble = false;
                    }
                    else
                    {
                        d.CollegeFreeAble = true;
                    }
                }
                if (DbOpe_crm_contract_productinfo.Instance.CheckContractHasGlobalSearchProduct(appl.ContractId))
                {
                    d.GlobalFreeAble = false;
                }
                else
                {
                    if (DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.CheckContractHasPresentedGlobalSearchProduct(appl.ContractId))
                    {
                        //慧思学院赠送过了，所有都不能再送了
                        d.GTISFreeAble = false;
                        d.CollegeFreeAble = false;
                        d.GlobalFreeAble = false;
                    }
                    else
                    {
                        d.GlobalFreeAble = true;
                    }
                }
            }
            //LogUtil.AddLog("checkfree end:" + DateTime.Now.ToString("mm-dd HH:mm:ss:fff"));
            #endregion
            return d;
        }
        /// <summary>
        /// 根据查询条件获取合同服务信息环球慧思申请信息列表 12.5 服务二次审核
        /// 数据要区分身份  登记身份/复核身份
        /// 4.28 除系统管理员之外 不显示编码为空的数据
        /// </summary>
        /// <param name="condition"></param>
        /// <param name="userId"></param>
        /// <param name="total"></param>
        /// <returns></returns>
        public List<SearchContractProductServiceInfoGtisAppl_OUT> SearchContractProductServiceInfoGtisApplList(SearchContractProductServiceInfoGtisAppl_IN condition, string userId, ref int total)
        {
            //LogUtil.AddLog("start:" + DateTime.Now.ToString("mm-dd HH:mm:ss:fff"));
            var states = GetUserStates(userId);
            if (!ArrayUtil.IsNullOrEmpty(condition.State))
            {
                foreach (var state in condition.State)
                {
                    if (!states.Contains(state))
                    {
                        condition.State.Remove(state);
                    }
                }
            }
            else
            {
                condition.State = states;
            }
            //验证超级权限
            bool superRole = BLL_Role.Instance.CheckSuperUser();
            var dt = DateTime.Now;
            var dtDay = DateTime.Parse(dt.ToString("yyyy-MM-dd"));
            var weekEnd = dt.AddDays(1 - Convert.ToInt32(dt.DayOfWeek.ToString("d"))).AddDays(6).ToString("yyyy-MM-dd");
            var weekStart = dt.AddDays(1 - Convert.ToInt32(dt.DayOfWeek.ToString("d"))).ToString("yyyy-MM-dd");
            var approvalStates = new List<int>() { (int)EnumContractServiceState.VALID, (int)EnumContractServiceState.OUT, (int)EnumContractServiceState.INVALID };
            List<SearchContractProductServiceInfoGtisAppl_OUT> r = new List<SearchContractProductServiceInfoGtisAppl_OUT>();
            r = Queryable
                .LeftJoin<Db_crm_contract>((service, contract) => service.ContractId == contract.Id && contract.Deleted == false)
                .LeftJoin<Db_crm_contract_productinfo>((service, contract, product) => service.ContractProductInfoId == product.Id && product.Deleted == false)
                .LeftJoin<Db_crm_customer_subcompany>((service, contract, product, company) => contract.FirstParty == company.Id && company.Deleted == (int)EnumCustomerDel.NotDel)
                .LeftJoin<Db_v_userwithorg>((service, contract, product, company, applicant) => service.ApplicantId == applicant.Id && applicant.Deleted == false)
                .LeftJoin<Db_v_userwithorg>((service, contract, product, company, applicant, reviewer) => service.ReviewerId == reviewer.Id && reviewer.Deleted == false)
                .LeftJoin<Db_crm_contract_serviceinfo_gtis>((service, contract, product, company, applicant, reviewer, gtis) => gtis.ProductServiceInfoGtisApplId == service.Id && gtis.Deleted == false && gtis.IsApplHistory == false)
                .LeftJoin<Db_crm_product>((service, contract, product, company, applicant, reviewer, gtis, productInfo) => service.ProductId == productInfo.Id)// && productInfo.Deleted == false)
                .WhereIF(!superRole, (service, contract, product, company, applicant, reviewer, gtis, productInfo) => !SqlFunc.IsNullOrEmpty(contract.ContractNum))
                .WhereIF(!string.IsNullOrEmpty(condition.ContractNum), (service, contract, product, company, applicant, reviewer, gtis, productInfo) => contract.ContractNum == condition.ContractNum)
                .WhereIF(!string.IsNullOrEmpty(condition.FirstPartyName), (service, contract, product, company, applicant, reviewer, gtis, productInfo) => company.CompanyName.Contains(condition.FirstPartyName))
                .WhereIF(!string.IsNullOrEmpty(condition.ContractName), (service, contract, product, company, applicant, reviewer, gtis, productInfo) => contract.ContractName.Contains(condition.ContractName))
                .WhereIF(!ArrayUtil.IsNullOrEmpty(condition.ApplicantId), (service, contract, product, company, applicant, reviewer, gtis, productInfo) => SqlFunc.ContainsArray(condition.ApplicantId, applicant.Id))
                //.WhereIF(!ArrayUtil.IsNullOrEmpty(condition.ContractType), (service, contract, product, company, applicant, reviewer, gtis, productInfo) => SqlFunc.ContainsArray(condition.ContractType, contract.ContractType))
                .WhereIF(condition.ContractType.IsNotNull(), (service, contract, product, company, applicant, reviewer, gtis, productInfo) => condition.ContractType == contract.ContractType)
                .WhereIF(!ArrayUtil.IsNullOrEmpty(condition.ServiceType), (service, contract, product, company, applicant, reviewer, gtis, productInfo) => SqlFunc.ContainsArray(condition.ServiceType, service.ProcessingType))
                .WhereIF(condition.StampReviewStatus != null, (service, contract, product, company, applicant, reviewer, gtis, productInfo) => contract.StampReviewStatus == condition.StampReviewStatus)
                /*.WhereIF(condition.DiscountType != null, (service, contract, product, company, applicant, reviewer, gtis, productInfo) => service.DiscountType == (int)condition.DiscountType)*/
                //2025年3月21日 修改 增加applid(跳转用)
                .WhereIF(condition.AppleId.IsNotNullOrEmpty(), (service, contract, product, company, applicant, reviewer, gtis, productInfo) => service.Id == condition.AppleId)
                .Where(
                (service, contract, product, company, applicant, reviewer, gtis, productInfo) =>
                    (
                        //空的只能是刚申请的待开通状态
                        string.IsNullOrEmpty(gtis.Id)
                        &&
                        (
                        (condition.State.Contains((int)EnumContractServiceOpenState.TobeChanged) && service.State == (int)EnumProcessStatus.Submit && service.ProcessingType == (int)EnumProcessingType.Change)
                            ||
                            (condition.State.Contains((int)EnumContractServiceOpenState.ToBeOpened) && service.State == (int)EnumProcessStatus.Submit && service.ProcessingType == (int)EnumProcessingType.Add)
                        )
                    )
                    ||
                    (
                        !string.IsNullOrEmpty(gtis.Id)
                        &&
                        (
                            (condition.State.Contains((int)EnumContractServiceOpenState.ToBeReview) && gtis.State == (int)EnumContractServiceState.TO_BE_REVIEW)
                            ||
                            (condition.State.Contains((int)EnumContractServiceOpenState.Refuse) && gtis.State == (int)EnumContractServiceState.REFUSE)
                            ||
                            (condition.State.Contains((int)EnumContractServiceOpenState.Returned) && gtis.State == (int)EnumContractServiceState.RETURNED)
                            ||
                            (condition.State.Contains((int)EnumContractServiceOpenState.Open) && gtis.State == (int)EnumContractServiceState.VALID && (gtis.ServiceCycleEnd != null && SqlFunc.DateDiff(DateType.Day, dtDay, gtis.ServiceCycleEnd.Value) > 30))
                            ||
                            (condition.State.Contains((int)EnumContractServiceOpenState.NearlyEnd) && gtis.State == (int)EnumContractServiceState.VALID && (gtis.ServiceCycleEnd != null && SqlFunc.DateDiff(DateType.Day, dtDay, gtis.ServiceCycleEnd.Value) <= 30 && SqlFunc.DateDiff(DateType.Day, dtDay, gtis.ServiceCycleEnd.Value) >= 0))
                            ||
                            (condition.State.Contains((int)EnumContractServiceOpenState.OverDue) && gtis.State == (int)EnumContractServiceState.OUT)
                            ||
                            (condition.State.Contains((int)EnumContractServiceOpenState.Void) && (gtis.State == (int)EnumContractServiceState.INVALID || gtis.State == (int)EnumContractServiceState.VOID))
                            ||
                            (condition.State.Contains((int)EnumContractServiceOpenState.TobeChanged) && gtis.State == (int)EnumContractServiceState.TO_BE_OPENED && service.ProcessingType == (int)EnumProcessingType.Change)
                            ||
                            (condition.State.Contains((int)EnumContractServiceOpenState.ToBeOpened) && gtis.State == (int)EnumContractServiceState.TO_BE_OPENED && service.ProcessingType == (int)EnumProcessingType.Add)
                        )
                    )
                )
                //.WhereIF(!ArrayUtil.IsNullOrEmpty(condition.AccountStatus),
                //(service, contract, product, company, applicant, reviewer, gtis) =>
                //    (
                //        string.IsNullOrEmpty(gtis.Id)
                //        &&
                //        (
                //            (condition.AccountStatus.Contains((int)EnumGtisAccountStatus.INPROCESS))
                //        )
                //    )
                //    ||
                //    (
                //        !string.IsNullOrEmpty(gtis.Id)
                //        &&
                //        (
                //            SqlFunc.Subqueryable<Db_crm_contract_serviceinfo_gtis_user>()
                //            .Where(item => gtis.Id == item.ContractServiceInfoGtisId
                //                && SqlFunc.ContainsArray(condition.AccountStatus, item.OpeningStatus)
                //                && item.Deleted == false)
                //            .Any()
                //        )
                //    )
                //)
                .WhereIF(condition.CreateDateStart != null, (service, contract, product, company, applicant, reviewer, gtis, productInfo) => gtis.CreateDate != null && condition.CreateDateStart != null && SqlFunc.ToDateShort(condition.CreateDateStart.Value) <= SqlFunc.ToDateShort(gtis.CreateDate.Value))
                .WhereIF(condition.CreateDateEnd != null, (service, contract, product, company, applicant, reviewer, gtis, productInfo) => gtis.CreateDate != null && condition.CreateDateEnd != null && SqlFunc.ToDateShort(condition.CreateDateEnd.Value) >= SqlFunc.ToDateShort(gtis.CreateDate.Value))
                .WhereIF(condition.ServiceDateStart != null, (service, contract, product, company, applicant, reviewer, gtis) =>
                    SqlFunc.IIF(gtis.ServiceCycleStart != null, SqlFunc.ToDateShort(condition.ServiceDateStart.Value) <= SqlFunc.ToDateShort(gtis.ServiceCycleStart.Value), SqlFunc.ToDateShort(condition.ServiceDateStart.Value) <= SqlFunc.ToDateShort(service.ServiceCycleStart.Value))
                )
                .WhereIF(condition.ServiceDateEnd != null, (service, contract, product, company, applicant, reviewer, gtis) =>
                    SqlFunc.IIF(gtis.ServiceCycleStart != null, SqlFunc.ToDateShort(condition.ServiceDateEnd.Value) >= SqlFunc.ToDateShort(gtis.ServiceCycleStart.Value), SqlFunc.ToDateShort(condition.ServiceDateEnd.Value) >= SqlFunc.ToDateShort(service.ServiceCycleStart.Value))
                )
                .WhereIF(condition.ApprovalStartDate != null, (service, contract, product, company, applicant, reviewer, gtis) =>
                    SqlFunc.ToDateShort(condition.ApprovalStartDate.Value) <= SqlFunc.ToDateShort(gtis.ReviewerDate.Value) && approvalStates.Contains(gtis.State.Value))
                .WhereIF(condition.ApprovalEndDate != null, (service, contract, product, company, applicant, reviewer, gtis) =>
                    SqlFunc.ToDateShort(condition.ApprovalEndDate.Value) >= SqlFunc.ToDateShort(gtis.ReviewerDate.Value) && approvalStates.Contains(gtis.State.Value))
                .Where((service, contract, product, company, applicant, reviewer, gtis, productInfo) => service.Deleted == false && contract.Deleted == false)
                .WhereIF(condition.EnumQueryListType == EnumQueryListType.Today, (service, contract, product, company, applicant, reviewer, gtis, productInfo) => service.ApplicantDate != null && SqlFunc.DateIsSame(service.ApplicantDate.Value, dtDay))
                .WhereIF(condition.EnumQueryListType == EnumQueryListType.Week, (service, contract, product, company, applicant, reviewer, gtis, productInfo) => service.ApplicantDate != null && SqlFunc.Between(service.ApplicantDate.Value, weekStart, weekEnd))
                .WhereIF(!string.IsNullOrEmpty(condition.Remark), service => SqlFunc.Contains(service.Remark4List, condition.Remark))
                /* .WhereIF(!string.IsNullOrEmpty(condition.Remark), (service, contract, product, company, applicant, reviewer, gtis, productInfo) =>
                     SqlFunc.Subqueryable<Db_crm_contract_serviceinfo_gtis>()
                     .Where(gAll => gAll.ProductServiceInfoGtisApplId == service.Id && gAll.Deleted == false && service.Deleted == false)
                     .Where(gAll => SqlFunc.Contains(gAll.RegisteredRemark, condition.Remark) || SqlFunc.Contains(gAll.ReviewerRemark, condition.Remark) || SqlFunc.Contains(gAll.Remark, condition.Remark))
                     .Any()
                 )*/
                //.OrderByDescending((service, contract, product, company, applicant, reviewer, gtis, productInfo) => service.ApplicantDate)
                .Select((service, contract, product, company, applicant, reviewer, gtis, productInfo) => new SearchContractProductServiceInfoGtisAppl_OUT()
                {
                    Id = service.Id,
                    ContractNum = contract.ContractNum,
                    FirstPartyName = company.CompanyName,
                    ContractName = contract.ContractName,
                    ContractType = contract.ContractType,
                    ApplicantId = service.ApplicantId,
                    ApplicantName = applicant.UserWithOrgFullName,
                    ApplicantDate = service.ApplicantDate == null ? "" : service.ApplicantDate.Value.ToString("yyyy-MM-dd HH:mm"),
                    ProductName = product.ProductName + ((service.RetailCountry == (int)EnumGtisRetailCountry.Add && productInfo.ProductType != (int)EnumProductType.Vip) ? "（定制零售国家）" : ""),
                    ServiceCycle = (gtis.ServiceCycleStart == null || gtis.ServiceCycleEnd == null) ?
                    service.ServiceCycleStart.Value.ToString("yyyy-MM-dd") + "至" + service.ServiceCycleEnd.Value.ToString("yyyy-MM-dd") :
                    gtis.ServiceCycleStart.Value.ToString("yyyy-MM-dd") + "至" + gtis.ServiceCycleEnd.Value.ToString("yyyy-MM-dd"),
                    ReviewerId = service.ReviewerId,
                    ReviewerName = reviewer.Name,
                    ReviewerDate = gtis.CreateDate,
                    ContractServiceInfoGtisId = gtis.Id,
                    ServiceType = service.ProcessingType,
                    IsInvalid = service.IsInvalid,
                    applState = service.State.Value,
                    processingType = service.ProcessingType.Value,
                    serviceState = SqlFunc.IsNull(gtis.State, (int)EnumContractServiceState.TO_BE_OPENED),
                    ServiceCycleStart = SqlFunc.IsNull(gtis.ServiceCycleStart, service.ServiceCycleStart),
                    ServiceCycleEnd = SqlFunc.IsNull(gtis.ServiceCycleEnd, service.ServiceCycleEnd),
                    IsFree = service.IsFree,
                    SendAccount = service.SendAccount,
                    SendAccountTime = service.SendAccountTime,
                    /*ApplyRemark = service.Remark,
                    RegisteredRemark = gtis.RegisteredRemark,
                    FeedBack = gtis.Remark,
                    ReviewerRemark = gtis.ReviewerRemark,*/
                    Remark = service.Remark4List,
                    CustomerEmail = contract.Email,
                    //DiscountType = (EnumGtisDiscountType)service.DiscountType,
                    CouponIds = service.CouponIds,
                    IsUrgent = service.IsUrgent == true,
                    IsUrgentLable = false,
                }, true)
                .MergeTable()
                .OrderByPropertyName(condition.SortField, condition.IsDESC ? OrderByType.Desc : OrderByType.Asc)
                .OrderByIF(StringUtil.IsNullOrEmpty(condition.SortField), e => SqlFunc.IIF((e.State == 1 || (e.serviceState == 1 && !e.SendAccount)) && e.IsUrgent == true, 1, 0), OrderByType.Desc)
                .OrderByIF(StringUtil.IsNullOrEmpty(condition.SortField), e => SqlFunc.IIF(e.State == 1 && SqlFunc.ToDateShort(e.ServiceCycleStart) <= SqlFunc.ToDateShort(DateTime.Today), 1, 0), OrderByType.Desc)
                .OrderByIF(StringUtil.IsNullOrEmpty(condition.SortField), "ApplicantDate desc")
                .OrderByDescending(e => e.Id)
                .Mapper(it =>
                {
                    it.State = (int)GetContractServiceOpenState(it.applState, it.processingType, it.serviceState, it.ServiceCycleEnd);
                    it.ContractTypeName = it.ContractType == null ? null : Dictionary.ContractType.First(e => e.Value == it.ContractType.ToString()).Name;
                    it.StateName = ((EnumContractServiceOpenState)it.State).GetEnumDescription();
                    it.ServiceTypeName = ((EnumProcessingType)it.ServiceType).GetEnumDescription();
                    it.IsPendingToday = it.applState == 1 && SqlFunc.ToDateShort(it.ServiceCycleStart) <= SqlFunc.ToDateShort(DateTime.Today);
                    it.CouponCount = string.IsNullOrEmpty(it.CouponIds) ? 0 : it.CouponIds.Split(',').ToList().Count;
                    it.IsUrgentLable = it.IsUrgent && (it.applState == (int)EnumProcessStatus.Submit || (it.serviceState == (int)EnumContractServiceState.VALID && !it.SendAccount));
                    /*
                    //备注
                    *//* 9种状态
                        1.待开通：客户经理申请时的备注
                        2.待复核：初审时的备注
                        3.拒绝：初审时的备注
                        4.被驳回：复核时的审核反馈
                        5.已开通：复核时的复核备注
                        6.待变更：客户经理申请时的备注
                        7.过期：复核时的复核备注
                        8.即将到期：复核时的复核备注
                        9.作废：
                            1）合同复制或更新导致的作废：客户经理申请时的备注
                            2）服务变更通过导致的作废：复核时的复核备注
                        *//*
                    if (it.State == (int)EnumContractServiceOpenState.ToBeOpened || it.State == (int)EnumContractServiceOpenState.TobeChanged || (it.State == (int)EnumContractServiceOpenState.Void && it.applState == (int)EnumProcessStatus.Void && (it.serviceState == null || it.serviceState == (int)EnumContractServiceState.VOID)))
                        it.Remark = it.ApplyRemark;
                    else if (it.State == (int)EnumContractServiceOpenState.ToBeReview || it.State == (int)EnumContractServiceOpenState.Refuse)
                        it.Remark = it.RegisteredRemark;
                    else if (it.State == (int)EnumContractServiceOpenState.Returned)
                        it.Remark = it.FeedBack;
                    else if (it.State == (int)EnumContractServiceOpenState.Open || it.State == (int)EnumContractServiceOpenState.NearlyEnd || it.State == (int)EnumContractServiceOpenState.OverDue || (it.State == (int)EnumContractServiceOpenState.Void && it.applState == (int)EnumProcessStatus.Pass && it.serviceState == (int)EnumContractServiceState.INVALID))
                        it.Remark = it.ReviewerRemark;
                    */
                })
                .ToPageList(condition.PageNumber, condition.PageSize, ref total);
            //LogUtil.AddLog("searchend:" + DateTime.Now.ToString("mm-dd HH:mm:ss:fff"));
            var svCodes = r.FindAll(c => StringUtil.IsNotNullOrEmpty(c.ContractNum)).Select(c => c.ContractNum).Distinct().ToArray();
            Dictionary<string, string> accStates = new Dictionary<string, string>();
            if (svCodes.Length > 0)
            {
                accStates = BLL_GtisOpe.Instance.GetUserState(svCodes).Result;
                //LogUtil.AddLog("GetUserStateend:" + DateTime.Now.ToString("mm-dd HH:mm:ss:fff"));
            }
            r.ForEach(d =>
            {
                if (d.State == (int)EnumContractServiceOpenState.Void)
                {
                    d.AccountStatusName = "";
                    d.AccountStatus = (int)EnumGtisAccountStatus.Exception;
                    d.AccountNum = 0;
                }
                else if (d.State != (int)EnumContractServiceOpenState.Open && d.State != (int)EnumContractServiceOpenState.NearlyEnd)
                {
                    d.AccountStatusName = "";
                    d.AccountStatus = (int)EnumGtisAccountStatus.INPROCESS;
                    d.AccountNum = 0;
                }
                else
                {
                    var gtisFound = (d.ContractNum != null && accStates != null && accStates.ContainsKey(d.ContractNum));
                    d.AccountStatusName = gtisFound ? accStates[d.ContractNum] : "";
                    d.AccountStatus = (int)TransGtisServiceAccountStates(d.AccountStatusName);
                    //if (gtisFound)
                    //{
                    //    LogUtil.AddLog("GetContractServiceInfoGtisUserByApplId start:" + DateTime.Now.ToString("mm-dd HH:mm:ss:fff"));
                    //    //对于开通过的账户（在gtis系统有过记录） （******** 不刷新具体账号状态信息了）
                    //    var accounts = DbOpe_crm_contract_productserviceinfo_gtis_appl_user.Instance.
                    //    LogUtil.AddLog("GetContractServiceInfoGtisUserByApplId end:" + DateTime.Now.ToString("mm-dd HH:mm:ss:fff"));
                    //    d.AccountNum = accounts.Count;
                    //}
                    //else
                    //{
                    //    d.AccountNum = 0;
                    //}
                    d.AccountNum = 0;
                    #region 是否可以赠送 注释
                    ////LogUtil.AddLog("checkfree start:" + DateTime.Now.ToString("mm-dd HH:mm:ss:fff"));
                    //if (d.IsFree == true || d.IsInvalid != (int)EnumIsInvalid.Effective || d.State != (int)EnumContractServiceOpenState.Open)
                    //{
                    //    //gtis赠送过了，所有都不能再送了
                    //    d.GTISFreeAble = false;
                    //    d.CollegeFreeAble = false;
                    //    d.GlobalFreeAble = false;
                    //}
                    //else
                    //{
                    //    d.GTISFreeAble = true;
                    //    if (DbOpe_crm_contract_productserviceinfo_college_appl.Instance.CheckContractHasCollegeProduct(d.ContractId))
                    //    {
                    //        d.CollegeFreeAble = false;
                    //    }
                    //    else
                    //    {
                    //        if (DbOpe_crm_contract_productserviceinfo_college_appl.Instance.CheckContractHasPresentedCollegeProduct(d.ContractId))
                    //        {
                    //            //慧思学院赠送过了，所有都不能再送了
                    //            d.GTISFreeAble = false;
                    //            d.CollegeFreeAble = false;
                    //            d.GlobalFreeAble = false;
                    //        }
                    //        else
                    //        {
                    //            d.CollegeFreeAble = true;
                    //        }
                    //    }
                    //    if (DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.CheckContractHasGlobalSearchProduct(d.ContractId))
                    //    {
                    //        d.GlobalFreeAble = false;
                    //    }
                    //    else
                    //    {
                    //        if (DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.CheckContractHasPresentedGlobalSearchProduct(d.ContractId))
                    //        {
                    //            //慧思学院赠送过了，所有都不能再送了
                    //            d.GTISFreeAble = false;
                    //            d.CollegeFreeAble = false;
                    //            d.GlobalFreeAble = false;
                    //        }
                    //        else
                    //        {
                    //            d.GlobalFreeAble = true;
                    //        }
                    //    }
                    //}
                    ////LogUtil.AddLog("checkfree end:" + DateTime.Now.ToString("mm-dd HH:mm:ss:fff"));
                    #endregion

                }
                if (DbOpe_crm_contract_productinfo.Instance.CheckContractHasGlobalSearchProduct(d.ContractId))
                {
                    if (d.ProductName.Contains("（定制零售国家）"))
                        d.ProductName = d.ProductName.Replace("（定制零售国家）", "（定制零售国家+环球搜）");
                    else
                        d.ProductName += "（环球搜）";
                }
            });
            //LogUtil.AddLog("end:" + DateTime.Now.ToString("mm-dd HH:mm:ss:fff"));
            return r;
        }
        public EnumContractServiceOpenState GetContractServiceOpenState(int applState, int processingType, int? serviceState, DateTime? serviceCycleEnd)
        {
            EnumContractServiceOpenState r;
            if (serviceState == null)
            {
                serviceState = (int)EnumContractServiceState.TO_BE_OPENED;
            }
            //开通状态
            if (applState == (int)EnumProcessStatus.Submit)
            {
                if (serviceState == (int)EnumContractServiceState.TO_BE_OPENED && processingType == (int)EnumProcessingType.Add)
                {
                    r = EnumContractServiceOpenState.ToBeOpened;
                }
                else if (serviceState == (int)EnumContractServiceState.TO_BE_OPENED && processingType == (int)EnumProcessingType.Change)
                {
                    r = EnumContractServiceOpenState.TobeChanged;
                }
                else if (serviceState == (int)EnumContractServiceState.TO_BE_REVIEW)
                {
                    r = EnumContractServiceOpenState.ToBeReview;
                }
                else if (serviceState == (int)EnumContractServiceState.RETURNED)
                {
                    r = EnumContractServiceOpenState.Returned;
                }
                else
                {
                    throw new ApiException("读取开通状态错误");
                }
            }
            else if (applState == (int)EnumProcessStatus.Pass)
            {
                if (serviceState == (int)EnumContractServiceState.TO_BE_EFFECTIVE)
                {
                    r = EnumContractServiceOpenState.TO_BE_EFFECTIVE;
                }
                else if (serviceState == (int)EnumContractServiceState.VALID)
                {
                    var leftDate = serviceCycleEnd == null ? int.MaxValue : serviceCycleEnd.Value.GetDaysEnd().Subtract(DateTime.Now).Days;
                    if (leftDate <= 30 && leftDate >= 0)
                    {
                        r = EnumContractServiceOpenState.NearlyEnd;
                    }
                    else
                    {
                        r = EnumContractServiceOpenState.Open;
                    }
                }
                else if (serviceState == (int)EnumContractServiceState.OUT)
                {
                    r = EnumContractServiceOpenState.OverDue;
                }
                else if (serviceState == (int)EnumContractServiceState.INVALID || serviceState == (int)EnumContractServiceState.VOID)
                {
                    //被变更了的
                    r = EnumContractServiceOpenState.Void;
                }
                else
                {
                    throw new ApiException("读取开通状态错误");
                }
            }
            else if (applState == (int)EnumProcessStatus.Refuse)
            {
                r = EnumContractServiceOpenState.Refuse;
            }
            else if (applState == (int)EnumProcessStatus.Void)
            {
                r = EnumContractServiceOpenState.Void;
            }
            else
            {
                throw new ApiException("读取开通状态错误");
            }
            return r;
        }

        public EnumSalesServiceState GetContractSalesServiceState(int applState, DateTime? SendAccountTime, int? serviceState)
        {
            EnumSalesServiceState r;
            if (applState == null)
                r = EnumSalesServiceState.NOTAPPL;
            else if (applState == 1 && serviceState == (int)EnumContractServiceState.TO_BE_OPENED)
                r = EnumSalesServiceState.PENDING;
            else if (applState == 1)
                r = EnumSalesServiceState.PROCESS;
            else if (applState == 3)
                r = EnumSalesServiceState.REFUSE;
            else if (applState == 4)
                r = EnumSalesServiceState.VOID;
            else if (serviceState == (int)EnumContractServiceState.VALID && SendAccountTime == null)
                r = EnumSalesServiceState.PROCESS;
            else if (serviceState == (int)EnumContractServiceState.VALID)
                r = EnumSalesServiceState.VALID;
            else if (serviceState == (int)EnumContractServiceState.OUT)
                r = EnumSalesServiceState.OVERDUE;
            else if (serviceState == (int)EnumContractServiceState.VOID || serviceState == (int)EnumContractServiceState.INVALID)
                r = EnumSalesServiceState.VOID;
            else if (serviceState == (int)EnumContractServiceState.REFUSE)
                r = EnumSalesServiceState.REFUSE;
            else
                r = EnumSalesServiceState.PENDING;
            return r;
        }
        public ApplGtisInfo_OUT GetApplGtisInfo(string applId)
        {
            ApplGtisInfo_OUT agi = Queryable
                    .LeftJoin<Db_sys_user>((a, applUser) => a.ApplicantId == applUser.Id)
                    .LeftJoin<Db_sys_user>((a, applUser, reUser) => a.ReviewerId == reUser.Id)
                    .Where((a, applUser, reUser) => a.Deleted == false)
                    .Where((a, applUser, reUser) => a.Id == applId)
                    .Select((a, applUser, reUser) => new ApplGtisInfo_OUT
                    {
                        Id = a.Id.SelectAll(),
                        ApplicantName = applUser.Name,
                        ReviewerName = reUser.Name,
                        SendAccount = a.SendAccount,
                        SendAccountTime = a.SendAccountTime,
                    })
                    .First();

            if (agi == null)
            {
                throw new ApiException("未找到对应服务产品申请信息");
            }
            agi.AuthorizationNum = agi.AuthorizationNum == null ? 0 : agi.AuthorizationNum.Value;
            agi.ResidentCountries = DbOpe_crm_contract_productserviceinfo_gtis_appl_residentcountry.Instance.GetDataList(u => u.ProductServiceInfoGtisApplId == applId).MappingTo<List<GtisResidentCountry>>();
            agi.ResidentCitys = DbOpe_crm_contract_productserviceinfo_gtis_appl_residentcity.Instance.GetDataList(u => u.ProductServiceInfoGtisApplId == applId).MappingTo<List<GtisResidentCity>>();
            agi.GtisUserInfo = new List<ApplGtisUserInfo_OUT>();
            if (agi.ProcessingType == (int)EnumProcessingType.Change)
            {
                var applUsers = DbOpe_crm_contract_productserviceinfo_gtis_appl_user.Instance.GetDataList(u => u.ContractProductServiceInfoGtisApplId == applId)
                    .OrderBy(e => e.AccountNumber == null).ThenBy(e => e.AccountNumber)
                    .OrderByDescending(e => e.AccountType);
                agi.GtisUserInfo = applUsers.MappingTo<List<ApplGtisUserInfo_OUT>>();
                agi.SharePeopleNum = agi.GtisUserInfo.Count == 0 ? agi.SharePeopleNum : agi.GtisUserInfo.Max(u => u.SharePeopleNum);
                var mainUser = agi.GtisUserInfo.Find(u => u.AccountType == (int)EnumGtisAccountType.Main);
                if (mainUser != null)
                {
                    agi.AuthorizationNum = mainUser.AuthorizationNum;
                }
                if (!string.IsNullOrEmpty(agi.ChangeReasonEnums))
                {
                    agi.ChangeReasons = new List<ApplGtisInfo_OUT_ChangeReason>();
                    try
                    {
                        // 尝试解析JSON格式
                        var changeReasonData = System.Text.Json.JsonSerializer.Deserialize<System.Text.Json.JsonElement>(agi.ChangeReasonEnums);
                        if (changeReasonData.ValueKind != System.Text.Json.JsonValueKind.Null && 
                            changeReasonData.TryGetProperty("ChangeReasons", out var changeReasonsElement) &&
                            changeReasonsElement.ValueKind == System.Text.Json.JsonValueKind.Array &&
                            changeReasonsElement.GetArrayLength() > 0)
                        {
                            foreach (var reason in changeReasonsElement.EnumerateArray())
                            {
                                if (reason.TryGetProperty("ChangeReason", out var changeReasonElement))
                                {
                                    var changeReason = changeReasonElement.GetInt32();
                                    var reasonObj = new ApplGtisInfo_OUT_ChangeReason();
                                    reasonObj.ChangeReasonEnum = changeReason.ToEnum<EnumGtisServiceChangeProject>();
                                    reasonObj.ChangeReasonEnumName = reasonObj.ChangeReasonEnum.GetEnumDescription();
                                    agi.ChangeReasons.Add(reasonObj);
                                }
                            }
                        }
                    }
                    catch (System.Text.Json.JsonException)
                    {
                        // JSON解析失败，尝试解析旧格式（兼容性处理）
                        var changeReasonEnumsList = agi.ChangeReasonEnums.Split(",").ToList();
                        changeReasonEnumsList.ForEach(item =>
                        {
                            var reason = new ApplGtisInfo_OUT_ChangeReason();
                            reason.ChangeReasonEnum = item.ToEnum<EnumGtisServiceChangeProject>();
                            reason.ChangeReasonEnumName = reason.ChangeReasonEnum.GetEnumDescription();
                            agi.ChangeReasons.Add(reason);
                        });
                    }
                }


                /*agi.GtisUserInfo.Sort((p1, p2) =>
                {
                    if (p2.AccountType == null || p1.AccountType == null)
                    {
                        return 0;
                    }
                    else
                    {
                        return p2.AccountType.Value.CompareTo(p1.AccountType.Value);
                    }
                });*/
                /*agi.ChangeProjectTypeList = Db.Queryable<Db_crm_contract_productserviceinfo_gtis_appl_change_project>()
                /*agi.ChangeProjectTypeList = Db.Queryable<Db_crm_contract_productserviceinfo_gtis_appl_change_project>()
                    .LeftJoin<Db_crm_contract_serviceinfo_changetype>((e, f) => e.ChangeProjectId == f.ChangeProjectId && f.Deleted == false && f.ServiceType == (int)EnumServiceType.Gtis)
                    .Where(e => e.Deleted == false)
                    .Where(e => e.ProductServiceInfoGtisApplId == agi.Id)
                    .Select((e, f) => f)
                    .ToList();*/
            }
            agi.GtisRetailCountry = new List<GtisRetailCountry_OUT>();
            if (agi.RetailCountry == (int)EnumGtisRetailCountry.Add)
            {
                var countrys = DbOpe_sys_g4_dbnames.Instance.GetDataAllList().Select<G4DbNames>().ToList();
                var applCountrys = DbOpe_crm_contract_productserviceinfo_gtis_appl_country.Instance.GetDataList(u => u.ProductServiceInfoGtisApplId == applId);
                foreach (var country in applCountrys)
                {
                    GtisRetailCountry_OUT gtisRetailCountry = country.MappingTo<GtisRetailCountry_OUT>();
                    var c = countrys.Find(c => c.SID == gtisRetailCountry.Sid);
                    if (c != null)
                    {
                        gtisRetailCountry.SidName = c.CountryName;
                        gtisRetailCountry.BelongToSid = c.BelongToSid == null ? c.SID.Value : c.BelongToSid.Value;
                        agi.GtisRetailCountry.Add(gtisRetailCountry);
                    }
                }
            }
            if (agi.ServiceMonthAfterDiscount == null)
                agi.ServiceMonthAfterDiscount = agi.ServiceMonth;
            if (agi.ServiceCycleStartAfterDiscount == null)
                agi.ServiceCycleStartAfterDiscount = agi.ServiceCycleStart;
            if (agi.ServiceCycleEndAfterDiscount == null)
                agi.ServiceCycleEndAfterDiscount = agi.ServiceCycleEnd;

            agi.ServiceCycle = (agi.ServiceCycleStart == null ? "" : agi.ServiceCycleStart.Value.ToString("yyyy-MM-dd")) + "至" + (agi.ServiceCycleEnd == null ? "" : agi.ServiceCycleEnd.Value.ToString("yyyy-MM-dd"));
            agi.ServiceCycleAfterDiscount = (agi.ServiceCycleStartAfterDiscount == null ? "" : agi.ServiceCycleStartAfterDiscount.Value.ToString("yyyy-MM-dd")) + "至" + (agi.ServiceCycleEndAfterDiscount == null ? "" : agi.ServiceCycleEndAfterDiscount.Value.ToString("yyyy-MM-dd"));

            /* //补充环球搜的此次执行的服务开始结束时间和月份，此时默认和申请的服务周期一致
             agi.GlobalSearchExecuteServiceCycleStart = agi.ServiceCycleEnd;
             agi.GlobalSearchExecuteServiceCycleEnd = agi.ServiceCycleEnd;
             agi.GlobalSearchExecuteServiceMonth = agi.ServiceMonth;*/

            return agi;
        }
        #region ContractServiceGtisInfo_OUT 逻辑重复，注释
        /// <summary>
        /// 获取服务产品信息（申请信息+ 登记复核信息(仅管理员可见) + 在服信息）
        /// </summary>
        /// <param name="applId"></param>

        /* public ContractServiceGtisInfo_OUT GetContractServiceInfoGtisByApplId(string applId)
         {
             ContractServiceGtisInfo_OUT r = new ContractServiceGtisInfo_OUT();
             #region 申请信息
             var agi = GetApplGtisInfo(applId);
             r.ApplGtisInfo = agi;
             #endregion
             #region 当前产品是否是赠送
             r.IsFree = agi.IsFree;
             #endregion
             #region 合同业绩信息
             r.ContractInfoAndReceipt = DbOpe_crm_contract.Instance.GetContractAndReceiptByContractId(agi.ContractId);
             #endregion
             #region 历史服务信息
             var historyList = new List<HistoryGtisServiceInfo>();
             //判断是否是续约合同的服务
             var isRenew = !string.IsNullOrEmpty(agi.RenewContractNum);
             //判断是否是变更的服务
             var isChange = agi.ProcessingType == (int)EnumProcessingType.Change;
             //查找历史数据要用的ContractId
             var hisParamApplyId = agi.Id;
             //查找历史数据要用的ContractNum
             var hisParamContractNum = agi.RenewContractNum;
             //如果是续约合同的服务或是变更的服务，需要查找历史数据
             while (isRenew || isChange)
             {
                 //声明历史数据
                 var hisServe = new HistoryGtisServiceInfo_Mid();
                 //如果当前服务是变更数据，取被变更的服务信息
                 if (isChange)
                     //获取被变更的服务数据信息
                     hisServe = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetChangedGtisServiceInfoByApplyInfo(hisParamApplyId, r.ContractInfoAndReceipt.ContractNum);
                 else
                     //根据客户编码获取被续约服务信息
                     hisServe = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetHisGtisServiceInfoByContractNum(hisParamContractNum);
                 if (hisServe != null)
                 {
                     //修改isRenew 判断是否是续约合同的服务
                     isRenew = !string.IsNullOrEmpty(hisServe.OldContractNum);
                     //修改isChange 判断是否是变更的服务
                     isChange = hisServe.ProcessingType == (int)EnumProcessingType.Change;
                     //修改查找历史数据要用的ContractId
                     hisParamApplyId = hisServe.ApplyId;
                     //修改查找历史数据要用的ContractNum
                     hisParamContractNum = hisServe.OldContractNum;
                     //填充返回列表
                     historyList.Add(hisServe.MappingTo<HistoryGtisServiceInfo>());
                 }
                 else
                 {
                     isRenew = false;
                     isChange = false;
                 }
             }
             r.HistoryGtisServiceList = historyList;
             #endregion
             //同步g5账号信息
             List<Db_crm_contract_serviceinfo_gtis_user> userDatas = new List<Db_crm_contract_serviceinfo_gtis_user>();
             string gtisContractNum = string.Empty;
             BLL_ContractService.Instance.SynchroGtisUserData(r.ContractInfoAndReceipt.ContractNum, ref userDatas, ref gtisContractNum);

             #region 到账备注信息
             //到账备注列表
             r.ReceiptRemarks = DbOpe_crm_contract_receiptregister.Instance.GetRemarksByContractId(agi.ContractId);
             //合同的所有到账信息
             r.ReceiptRegisterCollectionList = DbOpe_crm_contract_receipt_details.Instance.GetHistoryCollectionInfoItemsByContractReceiptRegisterId(agi.ContractId, String.Empty);
             #endregion
             #region 登记复核信息
             int gtisServiceState = (int)EnumContractServiceState.TO_BE_OPENED;
             DateTime? serviceCycleEnd = null;
             if (DbOpe_sys_user.Instance.CheckUserIsManager(UserId))
             {

                 r.RegisterInfo = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetRegisterInfoByApplId(applId);
                 if (r.RegisterInfo != null)
                 {
                     gtisServiceState = r.RegisterInfo.State.Value;
                     serviceCycleEnd = r.RegisterInfo.ServiceCycleEnd;
                 }
             }

             #endregion
             #region 状态
             //服务类型
             r.ServiceType = agi.ProcessingType;
             r.ServiceTypeName = ((EnumProcessingType)r.ServiceType).GetEnumDescription();
             r.State = (int)GetContractServiceOpenState(agi.State.Value, agi.ProcessingType.Value, gtisServiceState, serviceCycleEnd);
             r.StateName = ((EnumContractServiceOpenState)r.State).GetEnumDescription();
             //账号状态  正常的显示账号状态，其他不显示
             if (gtisServiceState != (int)EnumContractServiceState.VALID)
             {
                 r.AccountStatus = (int)EnumGtisAccountStatus.INPROCESS;
                 r.AccountStatusName = ((EnumGtisAccountStatus)r.AccountStatus).GetEnumDescription();
             }
             else
             {
                 var svCode = r.ContractInfoAndReceipt.ContractNum;
                 var accStates = BLL_GtisOpe.Instance.GetUserState(new string[] { svCode }).Result;
                 r.AccountStatusName = accStates.ContainsKey(svCode) ? accStates[svCode] : "";
                 r.AccountStatus = (int)TransGtisServiceAccountStates(r.AccountStatusName);
                 //方法被其他外部接口重复调用,这里注释
                 //r.Accounts = DbOpe_crm_contract_productserviceinfo_gtis_appl_user.Instance.GetContractServiceInfoGtisUserByApplId(applId);
             }
             #endregion
             #region 产品信息
             //这里的超级子账号数是套餐自带的
             r.ProductInfo = DbOpe_crm_contract_productinfo.Instance.GetContractProductInfoById(agi.ContractProductInfoId);
             //定制报告下载次数
             r.Appl_ReportTotalNum = r.ProductInfo.CustomizedReportDownloadsNum == null ? 0 : r.ProductInfo.CustomizedReportDownloadsNum.Value;
             //这里的超级子账号数是另外买的
             List<ProductInfo_Out> SuperSubAccountList = DbOpe_crm_product_supersubaccount.Instance.GetProductSuperSubAccountByContractId(agi.ContractId);
             if (SuperSubAccountList.Count > 0)
             {
                 r.ProductInfo_SuperSubAccount = SuperSubAccountList.First();
             }
             //是否含VIP零售
             r.IsContainsVIP = false;
             var productList = DbOpe_crm_contract_productinfo.Instance.GetContractProductInfoByContractId(agi.ContractId);
             var vipProduct = productList.Find(p => p.ProductType == (int)EnumProductType.Vip);
             if (vipProduct != null)
             {
                 if (r.ProductInfo.ProductType != (int)EnumProductType.Vip)
                 {
                     r.IsContainsVIP = true;
                     r.ProductInfo_VIP = vipProduct;
                 }
             }
             #endregion
             #region 超级子账号数汇总
             r.SuperSubAccountTotalNum = (r.ProductInfo.SuperSubAccountNum == null ? 0 : r.ProductInfo.SuperSubAccountNum.Value) + ((r.ProductInfo_SuperSubAccount == null || r.ProductInfo_SuperSubAccount.SuperSubAccountNum == null) ? 0 : r.ProductInfo_SuperSubAccount.SuperSubAccountNum.Value);
             #endregion
             #region 在服信息   在通过之前：变更的话取上一份通过的gtis信息，不是变更的话，如果是续约合同的话，这里是公司上一份合同的服务产品信息
             //var gtis = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetInfoByApplContractProductId(agi.ContractProductInfoId);
             var gtis = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetGtisInfoByContractNum(gtisContractNum);
             if (agi.State == (int)EnumProcessStatus.Pass || agi.ProcessingType == (int)EnumProcessingType.Change)
             {
                 r.GtisInfo = gtis;
                 //续约通过或者服务变更通过,优惠券数量使用gtis的数量+历史服务信息historyList的优惠券数量之和
                 if (agi.State == (int)EnumProcessStatus.Pass && (agi.ProcessingType == (int)EnumProcessingType.Change || r.ContractInfoAndReceipt.ContractType == (int)EnumContractType.ReNew))
                 {
                     r.GtisInfo.CouponCount += historyList.Sum(x => x.CouponCount);
                     if (!string.IsNullOrEmpty(r.GtisInfo.CouponIds))
                         r.GtisInfo.CouponIds += ",";
                     r.GtisInfo.CouponIds += string.Join(',', historyList.Select(x => x.CouponIds).ToList());
                 }

                 //服务变更且当前提交或拒绝状态，只使用历史服务信息historyList的优惠券数量之和
                 else if (agi.ProcessingType == (int)EnumProcessingType.Change && (agi.State == (int)EnumProcessStatus.Submit || agi.State == (int)EnumProcessStatus.Refuse))
                 {
                     r.GtisInfo.CouponCount = historyList.Sum(x => x.CouponCount);
                     r.GtisInfo.CouponIds = string.Join(',', historyList.Select(x => x.CouponIds).ToList());
                 }

             }
             else if (r.ContractInfoAndReceipt.ContractType == (int)EnumContractType.ReNew)
             {
                 var gtisContractNumNoUse = string.Empty;
                 BLL_ContractService.Instance.SynchroGtisUserData(agi.RenewContractNum, ref userDatas, ref gtisContractNumNoUse);
                 r.GtisInfo = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetGtisInfoByContractNum(agi.RenewContractNum);
                 if (r.GtisInfo != null)
                 {
                     r.GtisInfo.CouponCount = historyList.Sum(x => x.CouponCount);
                     if (!string.IsNullOrEmpty(r.GtisInfo.CouponIds))
                         r.GtisInfo.CouponIds += ",";
                     r.GtisInfo.CouponIds = string.Join(',', historyList.Select(x => x.CouponIds).ToList());
                 }

                 *//*//r.GtisInfo = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetOldContractGtisInfoByCompanyId(r.ContractInfoAndReceipt.FirstParty, r.ContractInfoAndReceipt.Id);
                 var cpId = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetContractProductIdByContractNum(agi.RenewContractNum);
                 if (cpId != null)
                 {
                     r.GtisInfo = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetInfoByApplContractProductId(cpId, true);
                 }*//*
             }
             else
             {
                 r.GtisInfo = null;
             }
             if (r.GtisInfo != null)
                 r.GtisInfo.CouponCountTotal = DbOpe_crm_customer_coupon.Instance.GetValidCouponDetailCountByCompanyId(r.ContractInfoAndReceipt.FirstParty);
             #endregion
             #region 开通项目 OpenItems   新申请/续约的显示空，变更申请的显示上次开通的，申请通过的显示这次开通的
             r.OpenItems = new List<GtisRetailCountry_OUT>();
             if (agi.State == (int)EnumProcessStatus.Pass && r.RegisterInfo != null)
             {
                 //通过的显示这次开通的
                 var countrys = DbOpe_sys_g4_dbnames.Instance.GetDataAllList().Select<G4DbNames>().ToList();
                 var gtisCountrys = DbOpe_crm_contract_serviceinfo_gtis_country.Instance.GetDataList(u => u.ContractServiceInfoGtisId == r.RegisterInfo.Id);
                 foreach (var country in gtisCountrys)
                 {
                     GtisRetailCountry_OUT gtisRetailCountry = country.MappingTo<GtisRetailCountry_OUT>();
                     var c = countrys.Find(c => c.SID == gtisRetailCountry.Sid);
                     if (c != null)
                     {
                         gtisRetailCountry.SidName = c.CountryName;
                         r.OpenItems.Add(gtisRetailCountry);
                     }
                 }
             }
             else if (agi.ProcessingType == (int)EnumProcessingType.Change)
             {
                 //未通过 变更的 显示上次开通的
                 if (r.GtisInfo != null)
                 {
                     r.OpenItems = r.GtisInfo.GtisRetailCountry;
                 }
             }
             #endregion
             #region 变更信息
             r.GtisChangeItems = new List<GtisChangeItems>();
             if (gtis != null)
             {
                 var changeAbleCols = new List<GtisChangeItems>()
                 {
                      new GtisChangeItems(){ PropKey = "PrimaryAccountsNum", PropName = "主账号数量",IsArray = false },
                      new GtisChangeItems(){ PropKey = "SubAccountsNum", PropName = "子账号数量",IsArray = false },
                      new GtisChangeItems(){ PropKey = "SharePeopleNum", PropName = "每个账户共享人数",IsArray = false },
                      new GtisChangeItems(){ PropKey = "ShareUsageNum", PropName = "共享使用总数",IsArray = false },
                      new GtisChangeItems(){ PropKey = "AuthorizationNum", PropName = "子账号授权国家次数",IsArray = false },
                      new GtisChangeItems(){ PropKey = "ServiceCycle", PropName = "服务周期",IsArray = false },
                      new GtisChangeItems(){ PropKey = "RetailCountry", PropName = "零售国家",IsArray = false },
                      new GtisChangeItems(){ PropKey = "GtisRetailCountry", PropName = "选择国家",IsArray = true },
                      new GtisChangeItems(){ PropKey = "ResidentCountries", PropName = "常驻国家",IsArray = true },
                      new GtisChangeItems(){ PropKey = "ResidentCitys", PropName = "常驻城市",IsArray = true }
                 };
                 foreach (var col in changeAbleCols)
                 {
                     var applV = agi.GetType().GetProperty(col.PropKey).GetValue(agi, null);
                     var gtisV = gtis.GetType().GetProperty(col.PropKey).GetValue(gtis, null);
                     if (!col.IsArray)
                     {
                         if (applV.ToString() != gtisV.ToString())
                         {
                             var newItem = col.MappingTo<GtisChangeItems>();
                             newItem.ChangeValue = gtisV.ToString();
                             r.GtisChangeItems.Add(newItem);
                         }
                     }
                     else
                     {
                         List<int> appVs = new List<int>();
                         List<int> gtisVs = new List<int>();
                         List<string> gtisVsNames = new List<string>();
                         if (col.PropKey == "GtisRetailCountry")
                         {
                             appVs = agi.GtisRetailCountry.Select(c => c.Sid).ToList();
                             gtisVs = gtis.GtisRetailCountry.Select(c => c.Sid).ToList();
                             gtisVsNames = gtis.GtisRetailCountry.Select(c => c.SidName).ToList();
                         }
                         else if (col.PropKey == "ResidentCountries")
                         {
                             appVs = agi.ResidentCountries.Select(c => c.ResidentCountry).ToList();
                             gtisVs = gtis.ResidentCountries.Select(c => c.ResidentCountry).ToList();
                             gtisVsNames = gtis.ResidentCountries.Select(c => c.ResidentCountryName).ToList();
                         }
                         else if (col.PropKey == "ResidentCitys")
                         {
                             appVs = agi.ResidentCitys.Select(c => c.ResidentCity).ToList();
                             gtisVs = gtis.ResidentCitys.Select(c => c.ResidentCity).ToList();
                             gtisVsNames = gtis.ResidentCitys.Select(c => c.ResidentCityName).ToList();
                         }
                         if (!(appVs.Count == 0 && gtisVs.Count == 0) && appVs.Except(gtisVs).ToList().Count != 0)
                         {
                             var newItem = col.MappingTo<GtisChangeItems>();
                             newItem.ChangeValue = string.Join(',', gtisVsNames);
                             r.GtisChangeItems.Add(newItem);
                         }
                     }
                 }
             }
             #endregion
             #region 相关赠送信息
             r.GetFreeGtisServiceInfo = GetFreeGtisServiceInfo(agi.ContractId);
             r.GetPresentedGlobalSearchService = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetPresentedGlobalSearchServiceInfo(agi.ContractId);
             r.PresentedCollegeService = DbOpe_crm_contract_serviceinfo_college.Instance.GetPresentedCollegeServiceInfo(agi.ContractId);
             #endregion
             #region g5备注
             //复核备注 取g5的备注字段
             if (agi.ProcessingType == (int)EnumProcessingType.Change)
             {
                 r.G5Remark = BLL_GtisOpe.Instance.GetCompanyOtherInfo(r.ContractInfoAndReceipt.ContractNum).Result;
             }
             else if (r.ContractInfoAndReceipt.ContractType == (int)EnumContractType.ReNew && r.GtisInfo != null)
             {
                 r.G5Remark = BLL_GtisOpe.Instance.GetCompanyOtherInfo(r.GtisInfo.ContractNum).Result;
             }
             else if (agi.ProcessingType == (int)EnumProcessingType.Add)
             {
                 if (agi.State == (int)EnumProcessStatus.Pass && r.GtisInfo != null)
                 {
                     r.G5Remark = BLL_GtisOpe.Instance.GetCompanyOtherInfo(r.GtisInfo.ContractNum).Result;
                 }
                 else
                 {
                     r.G5Remark = "";
                 }
             }
             #endregion
             #region 本次备注
             if (r.RegisterInfo != null)
             {
                 if (r.State == (int)EnumContractServiceOpenState.ToBeOpened
                     || r.State == (int)EnumContractServiceOpenState.ToBeReview
                     || r.State == (int)EnumContractServiceOpenState.Refuse
                     || r.State == (int)EnumContractServiceOpenState.TobeChanged)
                 {
                     //显示初审备注;
                     r.CurrentRemark = r.RegisterInfo.RegisteredRemark;
                 }
                 else if (r.State == (int)EnumContractServiceOpenState.Open
                     || r.State == (int)EnumContractServiceOpenState.Returned
                     || r.State == (int)EnumContractServiceOpenState.NearlyEnd
                     || r.State == (int)EnumContractServiceOpenState.Void
                     || r.State == (int)EnumContractServiceOpenState.OverDue)
                 {
                     //显示复核备注;
                     r.CurrentRemark = r.RegisterInfo.ReviewerRemark;
                 }
             }
             else
             {
                 r.CurrentRemark = "";
             }
             #endregion
             return r;
         }*/

        #endregion
        /// <summary>
        /// 获取合同gtis赠送信息
        /// </summary>
        /// <param name="contractId"></param>
        /// <returns></returns>
        public GetFreeGtisServiceInfo_Out GetFreeGtisServiceInfo(string contractId)
        {
            GetFreeGtisServiceInfo_Out r = new GetFreeGtisServiceInfo_Out();
            var appl = Queryable
               .Where(g => g.ContractId == contractId)
               .Where(g => g.Deleted == false)
               .Where(g => g.IsFree == true)
               .OrderByDescending(g => new { g.CreateDate })
               .First();
            if (appl == null)
            {
                return null;
            }
            #region 登记复核信息
            int gtisServiceState = (int)EnumContractServiceState.TO_BE_OPENED;
            DateTime? serviceCycleEnd = null;
            Db_crm_contract_serviceinfo_gtis oldGtisInfo;
            var registerInfo = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetRegisterInfoByApplId(appl.Id);
            if (registerInfo != null)
            {
                gtisServiceState = registerInfo.State.Value;
                serviceCycleEnd = registerInfo.ServiceCycleEnd;
                #region 获取赠送开始时间
                var gtisInfo = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetDataById(registerInfo.Id);
                if (string.IsNullOrEmpty(gtisInfo.HistoryId))
                {
                    return null;
                }
                oldGtisInfo = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetDataById(gtisInfo.HistoryId);
                if (oldGtisInfo == null)
                {
                    return null;
                }
                #endregion
            }
            else
            {
                return null;
            }
            #endregion
            //r.ServiceMonth = registerInfo.ServiceMonth == null?0:registerInfo.ServiceMonth.Value;
            r.State = GetContractServiceOpenState(appl.State.Value, appl.ProcessingType.Value, gtisServiceState, serviceCycleEnd);
            r.ServiceCycle = (oldGtisInfo.ServiceCycleEnd == null ? "" : oldGtisInfo.ServiceCycleEnd.Value.ToString("yyyy-MM-dd")) + "至" + (serviceCycleEnd == null ? "" : serviceCycleEnd.Value.ToString("yyyy-MM-dd"));
            return r;
        }
        /// <summary>
        /// 获取这个服务产品当前对应的有效申请（变更不算，只算新开通的申请）//2025.1.14去掉对变更的过滤
        /// </summary>
        /// <param name="contractProductInfoId"></param>
        /// <param name="exceptAppl"></param>
        /// <returns></returns>
        public Db_crm_contract_productserviceinfo_gtis_appl GetContractProductServiceInfoGtisApplByContractProductInfoId(string contractProductInfoId, string exceptAppl = "")
        {
            int Add = EnumProcessingType.Add.ToInt();
            int Effective = EnumIsInvalid.Effective.ToInt();
            return Queryable.Where(r => r.ContractProductInfoId == contractProductInfoId && r.Deleted == false && r.IsInvalid == Effective && /*r.ProcessingType == Add &&*/ r.Id != exceptAppl).First();
        }
        /// <summary>
        /// gtis系统返回的账号状态翻译成EnumGtisAccountStatus
        /// </summary>
        /// <param name="gtisStatesName"></param>
        /// <returns></returns>
        public EnumGtisAccountStatus TransGtisServiceAccountStates(string gtisStatesName)
        {
            switch (gtisStatesName)
            {
                case "正常":
                    return EnumGtisAccountStatus.Ok;
                case "异常":
                    return EnumGtisAccountStatus.Exception;
                case "停用":
                    return EnumGtisAccountStatus.Stop;
                case "过期":
                    return EnumGtisAccountStatus.Expired;
                default:
                    return EnumGtisAccountStatus.INPROCESS;
            }
        }
        /// <summary>
        /// 把现在生效产品对应的申请,状态重新置为有效  (如果剩下也全是拒绝的那就全是无效也无所谓了)
        /// </summary>
        /// <param name="contractProductInfoId"></param>
        public void ReActiveCurrentGtisAppl(string contractProductInfoId)
        {
            var currentGtis = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetData(g =>
                (g.State == (int)EnumContractServiceState.VALID || g.State == (int)EnumContractServiceState.TO_BE_EFFECTIVE || g.State == (int)EnumContractServiceState.OUT)
                && g.ContractProductInfoId == contractProductInfoId
                && g.IsApplHistory == false
            );
            if (currentGtis != null)
            {
                var currentAppl = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.GetDataById(currentGtis.ProductServiceInfoGtisApplId);
                currentAppl.IsInvalid = EnumIsInvalid.Effective.ToInt();
                DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.Update(currentAppl);
            }
        }
        /// <summary>
        /// 获取当前服务产品之前的所有未作废申请（不包括当前操作的申请）按复核时间倒序排列
        /// </summary>
        /// <param name="currentApplId"></param>
        /// <param name="ContractProductInfoId"></param>
        /// <returns></returns>
        public List<Db_crm_contract_productserviceinfo_gtis_appl> GetDatas(string currentApplId, string ContractProductInfoId)
        {
            return Queryable.Where(d =>
                    d.ContractProductInfoId == ContractProductInfoId
                    && d.State != (int)EnumProcessStatus.Void
                    && d.Id != currentApplId
                    && d.Deleted == false
                ).OrderByDescending(d => new { d.ReviewerDate })
                .ToList();

        }
        /// <summary>
        /// 作废未开通状态的GTIS申请
        /// </summary>
        /// <param name="contractId"></param>
        public void VoidGtisAppl(string contractId)
        {
            var cannotVoidStateList = new List<int>() { (int)EnumContractServiceState.TO_BE_REVIEW, (int)EnumContractServiceState.RETURNED };
            var cannotVoid = Db.Queryable<Db_crm_contract_productserviceinfo_gtis_appl>()
                .LeftJoin<Db_crm_contract_serviceinfo_gtis>((a, g) => g.ProductServiceInfoGtisApplId == a.Id)
                .Where((a, g) => a.ContractId == contractId)
                .Where((a, g) => a.Deleted == false)
                .Where((a, g) => SqlFunc.IsNullOrEmpty(g.Deleted) || g.Deleted == false)
                .Where((a, g) => SqlFunc.IsNullOrEmpty(g.IsApplHistory) || g.IsApplHistory == false)
                .Where((a, g) => (a.State == (int)EnumProcessStatus.Submit && SqlFunc.ContainsArray(cannotVoidStateList, g.State)) || a.State == (int)EnumProcessStatus.Pass)
                .Any();
            if (cannotVoid)
                throw new ApiException("合同中存在审核中的gtis申请或已经开通gtis服务，无法作废");

            //获取要删除的数据
            var applList = Db.Queryable<Db_crm_contract_productserviceinfo_gtis_appl>()
                .Where(a => a.ContractId == contractId)
                .ToList();
            //作废数据
            if (applList != null && applList.Count != 0)
            {
                var applIdList = applList.Select(g => g.Id).Distinct().ToList();
                //更新申请数据状态
                Updateable
                    .SetColumns(e => e.State == (int)EnumProcessStatus.Void)
                    .SetColumns(e => e.UpdateUser == UserId)
                    .SetColumns(e => e.UpdateDate == DateTime.Now)
                    .SetColumns(e => e.Deleted == true)
                    .Where(e => applIdList.Contains(e.Id))
                    .ExecuteCommand();
                //更新服务数据状态
                Db.Updateable<Db_crm_contract_serviceinfo_gtis>()
                    .SetColumns(service => service.State == (int)EnumContractServiceState.VOID)
                    .SetColumns(service => service.UpdateUser == UserId)
                    .SetColumns(service => service.UpdateDate == DateTime.Now)
                    .SetColumns(service => service.Deleted == true)
                    .Where(service => applIdList.Contains(service.ProductServiceInfoGtisApplId))
                    .ExecuteCommand();

                DbOpe_crm_privateservice_detail.Instance.RollBackePrivateServiceFromDelContarct(contractId, applIdList);
            }
        }
        public void VoidGtisAppl(string contractId, string userId)
        {
            var cannotVoidStateList = new List<int>() { (int)EnumContractServiceState.TO_BE_REVIEW, (int)EnumContractServiceState.RETURNED };
            var cannotVoid = Db.Queryable<Db_crm_contract_productserviceinfo_gtis_appl>()
                .LeftJoin<Db_crm_contract_serviceinfo_gtis>((a, g) => g.ProductServiceInfoGtisApplId == a.Id)
                .Where((a, g) => a.ContractId == contractId)
                .Where((a, g) => a.Deleted == false)
                .Where((a, g) => SqlFunc.IsNullOrEmpty(g.Deleted) || g.Deleted == false)
                .Where((a, g) => SqlFunc.IsNullOrEmpty(g.IsApplHistory) || g.IsApplHistory == false)
                .Where((a, g) => (a.State == (int)EnumProcessStatus.Submit && SqlFunc.ContainsArray(cannotVoidStateList, g.State)) || a.State == (int)EnumProcessStatus.Pass)
                .Any();
            //if (cannotVoid)
            //    throw new ApiException("合同中存在审核中的gtis申请或已经开通gtis服务，无法作废");

            //获取要删除的数据
            var applList = Db.Queryable<Db_crm_contract_productserviceinfo_gtis_appl>()
                .Where(a => a.ContractId == contractId)
                .ToList();
            //作废数据
            if (applList != null && applList.Count != 0)
            {
                var applIdList = applList.Select(g => g.Id).Distinct().ToList();
                //更新申请数据状态
                Updateable
                    .SetColumns(e => e.State == (int)EnumProcessStatus.Void)
                    .SetColumns(e => e.UpdateUser == userId)
                    .SetColumns(e => e.UpdateDate == DateTime.Now)
                    .SetColumns(e => e.Deleted == true)
                    .Where(e => applIdList.Contains(e.Id))
                    .ExecuteCommand();
                //更新服务数据状态
                Db.Updateable<Db_crm_contract_serviceinfo_gtis>()
                    .SetColumns(service => service.State == (int)EnumContractServiceState.VOID)
                    .SetColumns(service => service.UpdateUser == userId)
                    .SetColumns(service => service.UpdateDate == DateTime.Now)
                    .SetColumns(service => service.Deleted == true)
                    .Where(service => applIdList.Contains(service.ProductServiceInfoGtisApplId))
                    .ExecuteCommand();
            }
        }
        /// <summary>
        /// 获取有过gtis账号的公司(正常、过期)
        /// </summary>
        /// <returns></returns>
        public List<string> GetGtisServiceCompanys()
        {
            int[] gtisType = new int[] { (int)EnumProductType.Gtis, (int)EnumProductType.Combination, (int)EnumProductType.Vip };
            int[] gtisState = new int[] { (int)EnumContractServiceState.VALID, (int)EnumContractServiceState.OUT };
            //开通过gtis账号
            var havePassGtis = Db.Queryable<Db_v_productserviceinfo>()
                .LeftJoin<Db_crm_contract>((p, c) => p.ContractId == c.Id)
                      .Where((p, c) => c.Deleted == false
                          && p.ApplState == (int)EnumProcessStatus.Pass
                          && SqlFunc.ContainsArray<int>(gtisType, p.ProductType.Value)
                          && SqlFunc.ContainsArray<int>(gtisState, p.ServiceState.Value)
                          && !SqlFunc.IsNullOrEmpty(c.FirstParty)
                          && !SqlFunc.IsNullOrEmpty(p.ContractId)
                      )
                      .Select((p, c) => SqlFunc.ToString(c.FirstParty))
                      .Distinct().ToList();
            return havePassGtis;
        }

        /// <summary>
        /// 根据合同Id获取gtis提交状态的申请数据
        /// </summary>
        /// <param name="contractId"></param>
        /// <returns></returns>
        public Db_crm_contract_productserviceinfo_gtis_appl GetGtisApplByContractId(string contractId)
        {
            return Queryable
                .Where(e => e.ContractId == contractId)
                .Where(e => e.Deleted == false)
                .Where(e => e.State == (int)EnumProcessStatus.Submit)
                .First();
        }

        #region 第一次更新时的补数据方法
        public int Updatedate()
        {
            int count = 0;
            try
            {
                var datas = GTISUserTest().FindAll(u => string.IsNullOrEmpty(u.ParentSysUserID) && u.Deleted == 0);
                LogUtil.AddLog("GTISUserTest: " + datas.Count);
                TransDeal(() =>
                {
                    foreach (var date in datas)
                    {
                        LogUtil.AddLog("updatedate_user " + date.SerializeNewtonJson());
                        //RedisHelper.Set("updatedate_user", date.SerializeNewtonJson(), TimeSpan.FromMinutes(60));
                        var code = date.SvCode;
                        var gtisData = Db.Queryable<Db_crm_contract_serviceinfo_gtis>().Where(g => g.ContractNum == code).First();
                        if (gtisData != null)
                        {
                            DateTime s;
                            DateTime e;
                            if (DateTime.TryParse(date.StartServerDate, out s) && DateTime.TryParse(date.EndServerDate, out e))
                            {
                                gtisData.ServiceCycleStart = s;
                                gtisData.ServiceCycleEnd = e;
                                DbOpe_crm_contract_serviceinfo_gtis.Instance.Update(gtisData);
                            }
                            else
                            {
                                LogUtil.AddLog(code + " 时间转换失败");
                            }
                        }
                        count++;
                    }
                });
            }
            catch (Exception e)
            {
                LogUtil.AddLog(e.Message);
            }
            return count;


        }
        public void beixuyue3tiao(string applId, string oldContractNum)
        {
            Dictionary<string, string> dic = new Dictionary<string, string>() {
                { "18686","23354"},
                { "19672","21868"},
                { "20797","23586"}
            };
            if (dic.ContainsKey(oldContractNum))
            {
                var newContractKey = dic[oldContractNum];
                var applData = Queryable.Where(a => a.Id == applId).First();
                var oldGtis = Db.Queryable<Db_crm_contract_serviceinfo_gtis>().Where(g => g.ContractNum == newContractKey).First();
                var addGtis = oldGtis.MappingTo<Db_crm_contract_serviceinfo_gtis>();
                addGtis.Id = Guid.NewGuid().ToString();
                addGtis.ContractId = applData.ContractId;
                addGtis.ProductId = applData.ProductId;
                addGtis.ContractProductInfoId = applData.ContractProductInfoId;
                addGtis.AccountGenerationMethod = 1;
                addGtis.ProductServiceInfoGtisApplId = applId;
                addGtis.ContractNum = oldContractNum;
                addGtis.State = (int)EnumContractServiceState.VOID;
                DbOpe_crm_contract_serviceinfo_gtis.Instance.Insert(addGtis);
                var OldGtisUsers = Db.Queryable<Db_crm_contract_serviceinfo_gtis_user>().Where(g => g.ContractServiceInfoGtisId == oldGtis.Id).ToList();
                var addGtisUsers = OldGtisUsers.MappingTo<List<Db_crm_contract_serviceinfo_gtis_user>>();
                addGtisUsers.ForEach(u =>
                {
                    u.Id = Guid.NewGuid().ToString();
                    u.ContractServiceInfoGtisId = addGtis.Id;
                });
                DbOpe_crm_contract_serviceinfo_gtis_user.Instance.Insert(addGtisUsers);
                var OldGtisCountry = Db.Queryable<Db_crm_contract_serviceinfo_gtis_residentcountry>().Where(g => g.ContractServiceInfoGtisId == oldGtis.Id).ToList();
                var addGtisCountry = OldGtisCountry.MappingTo<List<Db_crm_contract_serviceinfo_gtis_residentcountry>>();
                addGtisCountry.ForEach(u =>
                {
                    u.Id = Guid.NewGuid().ToString();
                    u.ContractServiceInfoGtisId = addGtis.Id;
                });
                DbOpe_crm_contract_serviceinfo_gtis_residentcountry.Instance.Insert(addGtisCountry);
                var OldGtisCity = Db.Queryable<Db_crm_contract_serviceinfo_gtis_residentcity>().Where(g => g.ContractServiceInfoGtisId == oldGtis.Id).ToList();
                var addGtisCity = OldGtisCity.MappingTo<List<Db_crm_contract_serviceinfo_gtis_residentcity>>();
                addGtisCity.ForEach(u =>
                {
                    u.Id = Guid.NewGuid().ToString();
                    u.ContractServiceInfoGtisId = addGtis.Id;
                });
                DbOpe_crm_contract_serviceinfo_gtis_residentcity.Instance.Insert(addGtisCity);
            }
        }
        public int Bushuju()
        {
            int count = 0;
            try
            {
                var nogtisAppls = Queryable
                .LeftJoin<Db_crm_contract_serviceinfo_gtis>((appl, gtis) => appl.Id == gtis.ProductServiceInfoGtisApplId)
                .LeftJoin<Db_crm_contract>((appl, gtis, contract) => appl.ContractId == contract.Id)
                .Where((appl, gtis, contract) => appl.State == (int)EnumProcessStatus.Pass)
                .Where((appl, gtis, contract) => gtis.Id == null)
                .Select((appl, gtis, contract) => new
                {
                    Id = appl.Id,
                    ContractId = appl.ContractId,
                    ContractProductInfoId = appl.ContractProductInfoId,
                    ProductId = appl.ProductId,
                    ContractNum = contract.ContractNum
                }).ToList();
                var datas = GTISUserTest();
                LogUtil.AddLog("GTISUserTest:" + datas.Count);
                LogUtil.AddLog("nogtisAppls:" + nogtisAppls.Count);
                int total = 0;
                foreach (var appl in nogtisAppls)
                {
                    LogUtil.AddLog("for :" + total++);
                    TransDeal(() =>
                    {
                        var gtisId = Guid.NewGuid().ToString();
                        var relatedUsers = datas.FindAll(d => d.SvCode == appl.ContractNum);
                        if (relatedUsers.Count == 0)
                        {
                            //beixuyue3tiao(appl.Id, appl.ContractNum);
                        }
                        foreach (var user in relatedUsers)
                        {
                            LogUtil.AddLog("bushuju_user " + user.SerializeNewtonJson());
                            count++;
                            var userId = Guid.NewGuid().ToString();
                            int accType = string.IsNullOrEmpty(user.ParentSysUserID) ? (int)EnumGtisAccountType.Main : (int)EnumGtisAccountType.Sub;
                            Db_crm_contract_serviceinfo_gtis_user addUser = new Db_crm_contract_serviceinfo_gtis_user()
                            {
                                Id = userId,
                                UserId = user.SysUserID,
                                AccountNumber = user.UID,
                                PassWord = user.PWD,
                                AccountType = accType,
                                OpeningStatus = (int)EnumGtisUserOpeningStatus.Ok,
                                StartDate = DateTime.Parse(user.StartServerDate),
                                EndDate = DateTime.Parse(user.EndServerDate),
                                SharePeopleNum = user.SharingTimesUse == null ? 0 : int.Parse(user.SharingTimesUse),
                                AuthorizationNum = user.MaxCountry == null ? 0 : int.Parse(user.MaxCountry),
                                IsProcessed = 0,
                                CreateUser = Guid.Empty.ToString(),
                                CreateDate = DateTime.Now
                            };
                            DbOpe_crm_contract_serviceinfo_gtis_user.Instance.Insert(addUser);
                            if (accType == (int)EnumGtisAccountType.Main)
                            {
                                int? ResidentCountry = LocalCache.LC_Address.CountryAndAreaCache.Find(c => c.Name == user.crm_country)?.Id;
                                int? ResidentCity = LocalCache.LC_Address.CityCache.Find(c => c.Name == user.crm_city)?.Id;
                                if (ResidentCountry != null)
                                {
                                    Db_crm_contract_serviceinfo_gtis_residentcountry Gtis_ResidentCountry = new Db_crm_contract_serviceinfo_gtis_residentcountry();
                                    Gtis_ResidentCountry.Id = Guid.NewGuid().ToString();
                                    Gtis_ResidentCountry.ResidentCountry = ResidentCountry;
                                    Gtis_ResidentCountry.ContractServiceInfoGtisId = gtisId;
                                    DbOpe_crm_contract_serviceinfo_gtis_residentcountry.Instance.Insert(Gtis_ResidentCountry);
                                    if (ResidentCity != null)
                                    {
                                        Db_crm_contract_serviceinfo_gtis_residentcity Gtis_ResidentCity = new Db_crm_contract_serviceinfo_gtis_residentcity();
                                        Gtis_ResidentCity.Id = Guid.NewGuid().ToString();
                                        Gtis_ResidentCity.ResidentCity = ResidentCountry;
                                        Gtis_ResidentCity.ContractServiceInfoGtisId = gtisId;
                                        DbOpe_crm_contract_serviceinfo_gtis_residentcity.Instance.Insert(Gtis_ResidentCity);
                                    }
                                }
                                Db_crm_contract_serviceinfo_gtis addGtis = new Db_crm_contract_serviceinfo_gtis()
                                {
                                    Id = gtisId,
                                    ContractId = appl.ContractId,
                                    ProductId = appl.ProductId,
                                    ContractProductInfoId = appl.ContractProductInfoId,
                                    ProductServiceInfoGtisApplId = appl.Id,
                                    AccountGenerationMethod = 1,
                                    ContractNum = appl.ContractNum,
                                    PrimaryAccountsNum = 1,
                                    SubAccountsNum = relatedUsers.Count - 1,
                                    SharePeopleNum = user.SharingTimesUse == null ? 0 : int.Parse(user.SharingTimesUse),
                                    ShareUsageNum = user.SharingTimes == null ? 0 : int.Parse(user.SharingTimes),
                                    AuthorizationNum = user.MaxCountry == null ? 0 : int.Parse(user.MaxCountry),
                                    ServiceCycleStart = DateTime.Parse(user.StartServerDate),
                                    ServiceCycleEnd = DateTime.Parse(user.EndServerDate),
                                    RetailCountry = 2,
                                    State = (int)EnumContractServiceState.VALID,
                                    ProcessingType = (int)EnumProcessingType.Add,
                                    IsChanged = 0,
                                    IsProcessed = 1,
                                    CreateUser = Guid.Empty.ToString(),
                                    RegisteredId = Guid.Empty.ToString(),
                                    ReviewerId = Guid.Empty.ToString(),
                                    CreateDate = DateTime.Now

                                };
                                DbOpe_crm_contract_serviceinfo_gtis.Instance.Insert(addGtis);
                            }
                        }
                    });

                }
            }
            catch (Exception e)
            {
                LogUtil.AddLog(e.Message);
            }
            return count;
        }
        public int Buzizhanghao()
        {
            int count = 0;
            try
            {
                var gtisAppls = Queryable
                  .LeftJoin<Db_crm_contract_serviceinfo_gtis>((appl, gtis) => appl.Id == gtis.ProductServiceInfoGtisApplId)
                  .LeftJoin<Db_crm_contract>((appl, gtis, contract) => appl.ContractId == contract.Id)
                  .Where((appl, gtis, contract) => appl.State == (int)EnumProcessStatus.Pass)
                  .Select((appl, gtis, contract) => new
                  {
                      Id = appl.Id,
                      ContractId = appl.ContractId,
                      ContractProductInfoId = appl.ContractProductInfoId,
                      ProductId = appl.ProductId,
                      ContractNum = contract.ContractNum,
                      GtisId = gtis.Id
                  }).ToList();
                var datas = GTISUserTest();
                LogUtil.AddLog("GTISUserTest: " + datas.Count);
                List<Db_crm_contract_serviceinfo_gtis_user> adds = new List<Db_crm_contract_serviceinfo_gtis_user>();
                foreach (var appl in gtisAppls)
                {
                    //TransDeal(() =>
                    //{
                    var findG5SvCode = appl.ContractNum;
                    if (appl.ContractNum == "15618")
                    {
                        findG5SvCode = "15517";
                    }
                    else if (appl.ContractNum == "25844")
                    {

                    }
                    else
                    {
                        continue;
                    }
                    var relatedUsers = datas.FindAll(d => d.SvCode == findG5SvCode);
                    if (relatedUsers.Count != 0)
                    {
                        foreach (var user in relatedUsers)
                        {
                            LogUtil.AddLog("buzizhanghao_user " + user.SerializeNewtonJson());
                            count++;
                            var userId = Guid.NewGuid().ToString();
                            int accType = string.IsNullOrEmpty(user.ParentSysUserID) ? (int)EnumGtisAccountType.Main : (int)EnumGtisAccountType.Sub;
                            //var exist = Db.Queryable<Db_crm_contract_serviceinfo_gtis_user>().Where(u => u.UserId == user.SysUserID).First();
                            //if (exist == null)
                            //{
                            DateTime s;
                            DateTime e;
                            if (DateTime.TryParse(user.StartServerDate, out s) && DateTime.TryParse(user.EndServerDate, out e))
                            {
                                Db_crm_contract_serviceinfo_gtis_user addUser = new Db_crm_contract_serviceinfo_gtis_user()
                                {
                                    Id = userId,
                                    ContractServiceInfoGtisId = appl.GtisId,
                                    UserId = user.SysUserID,
                                    AccountNumber = user.UID,
                                    PassWord = user.PWD,
                                    AccountType = accType,
                                    OpeningStatus = (int)EnumGtisUserOpeningStatus.Ok,
                                    StartDate = s,
                                    EndDate = e,
                                    SharePeopleNum = user.SharingTimesUse == null ? 0 : int.Parse(user.SharingTimesUse),
                                    AuthorizationNum = user.MaxCountry == null ? 0 : int.Parse(user.MaxCountry),
                                    IsProcessed = 0,
                                    CreateUser = Guid.Empty.ToString(),
                                    CreateDate = DateTime.Now,
                                    GlobalSearchCode = user.HqsCode
                                };
                                adds.Add(addUser);
                                //DbOpe_crm_contract_serviceinfo_gtis_user.Instance.insery(addUser);
                            }
                            else
                            {
                                LogUtil.AddLog(user.SysUserID + " 时间转换失败");
                            }

                            //}
                            //else
                            //{
                            //    exist.GlobalSearchCode = user.HqsCode;
                            //    DbOpe_crm_contract_serviceinfo_gtis_user.Instance.Update(exist);
                            //}

                        }
                    }
                    else
                    {
                        //在gtis里没找到账号 （可能是被续约了的）

                    }
                    //});
                }
                //listSize为集合长度
                int listSize = adds.Count;
                LogUtil.AddLog("共：" + listSize + "条");
                //每次取1000条
                int index = 1000;
                for (int i = 0; i < listSize; i += 1000)
                {
                    //作用为Index最后没有1000条数据，则剩余的条数newList中就装几条
                    if (i + 1000 > listSize)
                    {
                        index = listSize - i;
                    }
                    LogUtil.AddLog("从" + i + "到" + i + index + "开始插入");
                    List<Db_crm_contract_serviceinfo_gtis_user> newList = adds.SubList(i, i + index).ToList();
                    DbOpe_crm_contract_serviceinfo_gtis_user.Instance.Insert(newList);
                    LogUtil.AddLog("本次循环插入数据成功");
                }
                LogUtil.AddLog("结束");
            }
            catch (Exception e)
            {
                LogUtil.AddLog(e.Message);
            }
            return count;
        }
        public int Buhuanqiusou()
        {
            /*

             -- 找一下ContractServiceInfoGlobalSearchId空的里面 合同没有环球搜的  368
                select u.ContractServiceInfoGlobalSearchId,u.GlobalSearchCode,g.Id,g.ContractNum
                from crm_contract_serviceinfo_gtis_user u
                LEFT JOIN crm_contract_serviceinfo_gtis g ON u.ContractServiceInfoGtisId = g.Id
                where u.ContractServiceInfoGlobalSearchId is null 
                and u.GlobalSearchCode !=''  
                and g.IsApplHistory = false
                and g.Deleted = 0
                and g.IsChanged = 0
                and g.ContractId NOT IN (
                    select ContractId from v_productserviceinfo v WHERE ProductType = 5
                )
                -- 368
                and g.State !=3
                -- 368
                and g.State !=0
             */
            var rlist = Db.Ado.SqlQuery<Db_crm_contract_serviceinfo_gtis_user>(" select u.* from crm_contract_serviceinfo_gtis_user u  LEFT JOIN crm_contract_serviceinfo_gtis g ON u.ContractServiceInfoGtisId = g.Id  where u.ContractServiceInfoGlobalSearchId is null  and u.GlobalSearchCode !=''  and g.IsApplHistory = false  and g.Deleted = 0  and g.IsChanged = 0  and g.ContractId NOT IN (select ContractId from v_productserviceinfo v WHERE ProductType = 5 ) and g.State !=3 and g.State !=0");
            int count = 0;
            var groupList = rlist.GroupBy(u => u.ContractServiceInfoGtisId).Select(u => new
            {
                Key = u.Key,
                List = u.ToList()
            }).ToList();
            LogUtil.AddLog("补环球搜开始，共" + groupList.Count);
            foreach (var o in groupList)
            {
                var list = o.List;
                var cstids = string.Join(';', list.Select(u => u.GlobalSearchCode).Distinct().ToList());
                var url = string.Format(AppSettings.GlobalSearchAPI.CheckUserStatus, cstids);
                int codeNum = list.Select(u => u.GlobalSearchCode).Distinct().Count();
                int successNum = 0;
                try
                {
                    var ret = NetUtil.Post_ReturnString(url).DeserializeNewtonJson<JObject>();
                    if ("200".Equals(ret["status"].ToString()))
                    {//查询接口调用成功，根据查询的结果回写数据库中各个账号的状态
                        var results = ret["results"].ToList();
                        var gtis = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetData(g => g.Id == o.Key);
                        results.ForEach(result =>
                        {
                            if ("active".Equals(result["status"].ToString()))
                            {
                                //返回正常，账号状态标记正常
                                DateTime codeStartDate;
                                DateTime codeEndDate;
                                if (DateTime.TryParse(result["begindate"].ToString(), out codeStartDate)
                                && DateTime.TryParse(result["enddate"].ToString(), out codeEndDate))
                                {
                                    //时间获取成功
                                    if (gtis != null)
                                    {
                                        if (codeEndDate >= gtis.ServiceCycleEnd)
                                        {
                                            //环球搜码结束时间 比 gtis服务时间 晚  = 环球搜续约成功
                                            successNum++;
                                        }
                                    }
                                }
                            }
                            else if ("disabled".Equals(result["status"].ToString())) { }
                            //返回过期，账号状态标记停用
                            else if ("noexisted".Equals(result["status"].ToString())) { }
                            //返回不存在，账号状态标记异常
                        });
                        if (successNum == codeNum)
                        {
                            //所有环球搜码都是续约成功的 = 补环球搜续约信息
                            TransDeal(() =>
                            {
                                var cpId = Db.Queryable<Db_v_productserviceinfo>().Where(v => v.ContractId == gtis.ContractId).Where(v => v.ProductType == (int)EnumProductType.Global).Select(v => v.Id).First();
                                if (cpId == null)
                                {
                                    // 合同里没有环球搜 环球搜添加成赠送的
                                    var contractInfo = DbOpe_crm_contract.Instance.GetData(g => g.Id == gtis.ContractId);
                                    if (contractInfo == null)
                                    {
                                        //记录问题，个别处理
                                        LogUtil.AddLog(o.Key + "未找到合同");
                                        LogUtil.AddLog(results.ToString());
                                    }
                                    var applId = Guid.NewGuid().ToString();
                                    var gAppl = new Db_crm_contract_productserviceinfo_globalsearch_appl()
                                    {
                                        Id = applId,
                                        ContractId = gtis.ContractId,
                                        ProductId = "a2f16437-e95f-4c3f-8e97-a22a1bbf0ab8",
                                        ContractProductInfoId = null,
                                        PrimaryAccountsNum = 1,
                                        SubAccountsNum = codeNum - 1,
                                        ServiceCycleStart = gtis.ServiceCycleStart,
                                        ServiceCycleEnd = gtis.ServiceCycleEnd,
                                        OldFirstParty = contractInfo.FirstParty,
                                        ApplicantId = contractInfo.Issuer,
                                        ApplicantDate = gtis.ServiceCycleStart,
                                        ReviewerId = gtis.ReviewerId,
                                        ReviewerDate = gtis.ReviewerDate,
                                        State = (int)EnumProcessStatus.Pass,
                                        ServiceMonth = gtis.ServiceMonth,
                                        IsInvalid = EnumIsInvalid.Effective.ToInt()
                                    };
                                    DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.Insert(gAppl);
                                    var gId = Guid.NewGuid().ToString();
                                    var g = new Db_crm_contract_serviceinfo_globalsearch()
                                    {
                                        Id = gId,
                                        ProductServiceInfoGlobalSearchApplId = applId,
                                        ContractId = gtis.ContractId,
                                        ProductId = "a2f16437-e95f-4c3f-8e97-a22a1bbf0ab8",
                                        ContractProductInfoId = null,
                                        PrimaryAccountsNum = 1,
                                        SubAccountsNum = codeNum - 1,
                                        ServiceCycleStart = gtis.ServiceCycleStart,
                                        ServiceCycleEnd = gtis.ServiceCycleEnd,
                                        AccountGenerationMethod = (int)EnumGtisAccountGenerationMethod.Generate,
                                        ContractNum = contractInfo.ContractNum,
                                        ProcessingType = (int)EnumProcessingType.Add,
                                        IsChanged = false,
                                        IsHistory = false,
                                        RegisteredId = gtis.RegisteredId,
                                        RegisteredTime = gtis.RegisteredDate,
                                        ReviewerId = gtis.ReviewerId,
                                        ReviewerTime = gtis.ReviewerDate,
                                        State = EnumContractServiceState.VALID,
                                        ServiceMonth = gtis.ServiceMonth,
                                        OldContractNum = gtis.OldContractNum
                                    };
                                    DbOpe_crm_contract_serviceinfo_globalsearch.Instance.Insert(g);
                                    List<Db_crm_contract_serviceinfo_globalsearch_user> users = new List<Db_crm_contract_serviceinfo_globalsearch_user>();
                                    foreach (var u in list)
                                    {
                                        users.Add(new Db_crm_contract_serviceinfo_globalsearch_user()
                                        {
                                            Id = Guid.NewGuid().ToString(),
                                            ProductServiceInfoGlobalSearchApplId = applId,
                                            ContractServiceInfoGlobalSearchId = gId,
                                            AccountNumber = u.GlobalSearchCode,
                                            AccountType = u.AccountType == (int)EnumGtisAccountType.Main ? EnumContractServiceGlobalSearchAccountType.PrimaryAccount : EnumContractServiceGlobalSearchAccountType.SubAccount,
                                            StartDate = g.ServiceCycleStart,
                                            EndDate = g.ServiceCycleEnd,
                                            OpeningStatus = EnumContractServiceGlobalSearchUserState.Normal
                                        });
                                        u.ContractServiceInfoGlobalSearchId = gId;
                                        DbOpe_crm_contract_serviceinfo_gtis_user.Instance.Update(u);
                                    }
                                    DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.InsertListData(users);
                                }
                                else
                                {
                                    //记录问题，个别处理
                                    LogUtil.AddLog(o.Key + "这个是有环球搜合同产品的");
                                    LogUtil.AddLog(results.ToString());
                                }
                            });
                        }
                        else
                        {
                            //记录问题，个别处理
                            LogUtil.AddLog(o.Key + " 成功：" + successNum + " 共：" + codeNum);
                            LogUtil.AddLog(results.SerializeNewtonJson());
                        }
                    }
                    else
                    {
                        throw new ApiException(ret["message"].ToString());
                    }
                }
                catch (Exception ex)
                {
                    throw new ApiException(ex.Message);
                }
                finally
                {
                    count++;
                }
            }
            return count;
        }
        #endregion
        /// <summary>
        /// 判断能否申请服务  
        /// 当前合同服务产品是否存在有效的服务申请且申请状态是提交或通过,如果存在则返回false无法再申请服务,如果不存在返回true
        /// </summary>
        /// <param name="ContractId"></param>
        /// <returns></returns>
        public bool CheckCouldApplyServiceByContractId(string ContractId)
        {
            return !Queryable
                .Where(e => e.ContractId == ContractId)
                .Where(e => e.State == (int)EnumProcessStatus.Submit || e.State == (int)EnumProcessStatus.Pass)
                .Where(e => e.IsInvalid == (int)EnumIsInvalid.Effective)
                .Any();
        }
        /// <summary>
        /// 判断能否服务变更
        /// </summary>
        /// <param name="ContractId"></param>
        /// <param name="ApplyId"></param>
        /// <returns></returns>
        public bool CheckCouldUpdateServiceByContractId(string ContractId, string ApplyId)
        {
            //获取当前的apply数据
            var apply = QueryByPrimaryKey(ApplyId);
            //获取当前的service数据
            var service = Db.Queryable<Db_crm_contract_serviceinfo_gtis>().Where(e => e.ProductServiceInfoGtisApplId == ApplyId && e.IsChanged == 0 && e.IsApplHistory == false && e.Deleted == false).First();
            //如果当前apply是通过状态、service是通过或过期状态且均是有效数据，可以进行服务变更，返回true
            if (apply.State == (int)EnumProcessStatus.Pass && apply.IsInvalid == (int)EnumIsInvalid.Effective && (service.State == (int)EnumContractServiceState.VALID || service.State == (int)EnumContractServiceState.OUT))
                return true;
            //如果当前apply是拒绝状态，且再apply之后没有新的有效的申请存在，不可进行服务变更，返回true
            else if (apply.State == (int)EnumProcessStatus.Refuse && !Queryable.Where(e => e.ContractId == ContractId && e.IsInvalid == (int)EnumIsInvalid.Effective && e.CreateDate > apply.CreateDate).Any())
                return true;
            else
                return false;
            #region 辅助总结的不可变更情况，注释
            /*
            //如果当前apply是申请状态且是有效数据，不可进行服务变更，返回false
            else if (apply.State == (int)EnumProcessStatus.Submit && apply.IsInvalid == (int)EnumIsInvalid.Effective)
                return false;
            //如果当前合同服务存在申请中的服务，不可进行服务变更，返回false （这个分支基本不会进入）
            else if (Queryable.Where(e => e.ContractId == ContractId && e.State == (int)EnumProcessStatus.Submit && e.IsInvalid == (int)EnumIsInvalid.Effective).Any())
                return false;
            */
            #endregion
        }

        /// <summary>
        /// 申请加急处理
        /// </summary>
        /// <param name="ApplyIds"></param>
        /// <returns></returns>
        public void UpdateApplUrgent(List<string> ApplyIds)
        {
            if (Queryable.Where(e => ApplyIds.Contains(e.Id) && e.IsUrgent == true).Any())
                throw new ApiException("所选申请存在已加急的申请，无法再次申请加急");
            Updateable
                .SetColumns(e => e.IsUrgent == true)
                .SetColumns(e => e.UpdateDate == DateTime.Now)
                .SetColumns(e => e.UpdateUser == UserId)
                .Where(e => ApplyIds.Contains(e.Id))
                .AddQueue();
        }

        /// <summary>
        /// 将相同contractProductInfoGropuId的数据置位失效
        /// </summary>
        /// <param name="contractProductInfoId"></param>
        public void InvalidOldApply(string contractProductInfoId)
        {
            Updateable
                .SetColumns(e => e.IsInvalid == (int)EnumIsInvalid.Invalid)
                .SetColumns(e => e.UpdateDate == DateTime.Now)
                .SetColumns(e => e.UpdateUser == UserId)
                .Where(e => e.ContractProductInfoId == contractProductInfoId)
                .Where(e => e.Deleted == false)
                .Where(e => e.IsInvalid == (int)EnumIsInvalid.Effective)
                .ExecuteCommand();
        }

        /// <summary>
        /// 获取慧思产品中Gtis产品的申请信息
        /// </summary>
        /// <param name="witsApplId"></param>
        /// <returns></returns>
        public GetWitsApplyInfo4Audit_Out_ApplyInfo_Gtis GetApplyInfoByWitsApplyId(string witsApplId)
        {
            return Queryable
                .LeftJoin<Db_crm_product>((e, f) => e.ProductId == f.Id && f.Deleted == false)
                .Where(e => e.WitsApplId == witsApplId)
                .Where(e => e.Deleted == false)
                .Select((e,f) => new GetWitsApplyInfo4Audit_Out_ApplyInfo_Gtis
                {
                    Id = e.Id,
                    ProducttId = e.ProductId,
                    SubAccountNum = e.SubAccountsNum.Value,
                    AuthorizationNum = e.AuthorizationNum.Value,
                    ForbidSearchExport = e.ForbidSearchExport,
                    WordRptPermissions = e.WordRptPermissions,
                    WordRptMaxTimes = e.WordRptMaxTimes.Value,
                    ServiceCycleStart = e.ServiceCycleStartAfterDiscount.Value,
                    ServiceCycleEnd = e.ServiceCycleEndAfterDiscount.Value,
                    ServiceMonth = e.ServiceMonthAfterDiscount.Value,
                    RetailCountry = e.RetailCountry.Value,
                    ProductName = f.ProductName,
                    ProductType = (EnumProductType)f.ProductType.Value
                })
                .Mapper(item =>
                {
                    if (item.RetailCountry == (int)EnumGtisRetailCountry.Add)
                    {
                        var countrys = DbOpe_sys_g4_dbnames.Instance.GetDataAllList().Select<G4DbNames>().ToList();
                        var applCountrys = DbOpe_crm_contract_productserviceinfo_gtis_appl_country.Instance.GetDataList(u => u.ProductServiceInfoGtisApplId == item.Id);
                        foreach (var country in applCountrys)
                        {
                            GtisRetailCountry_OUT gtisRetailCountry = country.MappingTo<GtisRetailCountry_OUT>();
                            var c = countrys.Find(c => c.SID == gtisRetailCountry.Sid);
                            if (c != null)
                            {
                                gtisRetailCountry.SidName = c.CountryName;
                                gtisRetailCountry.BelongToSid = c.BelongToSid == null ? c.SID.Value : c.BelongToSid.Value;
                                item.RetailCountryList.Add(gtisRetailCountry);
                            }
                        }
                    }
                })
                .First();
        }

        /// <summary>
        /// 根据witsId获取Gtis申请信息
        /// </summary>
        /// <param name="witsApplId"></param>
        /// <returns></returns>
        public Db_crm_contract_productserviceinfo_gtis_appl GetGtisApplByWitsId(string witsApplId)
        {
            return Queryable
                .Where(e => e.WitsApplId == witsApplId)
                .Where(e => e.Deleted == false)
                .Where(e => e.IsInvalid == (int)EnumIsInvalid.Effective)
                .First();
        }

    }
}
