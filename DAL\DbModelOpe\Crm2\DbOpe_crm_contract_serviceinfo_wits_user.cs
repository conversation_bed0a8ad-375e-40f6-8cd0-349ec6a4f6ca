using System;
using System.Collections.Generic;
using System.Linq;
using CRM2_API.DAL.DbCommon;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.ControllersViewModel;
using SqlSugar;

namespace CRM2_API.DAL.DbModelOpe.Crm2
{
    /// <summary>
    /// crm_contract_serviceinfo_wits_user表操作
    /// </summary>
    public class DbOpe_crm_contract_serviceinfo_wits_user : DbOperateCrm2Ex<Db_crm_contract_serviceinfo_wits_user, DbOpe_crm_contract_serviceinfo_wits_user>
    {
        /// <summary>
        /// 根据服务表ID获取用户列表
        /// </summary>
        /// <param name="serveId">申请ID</param>
        /// <returns>用户列表</returns>
        public List<Db_crm_contract_serviceinfo_wits_user> GetUsersByServelId(string serveId)
        {
            return Queryable
                .Where(x => x.WitsServeId == serveId && (x.Deleted == null || x.Deleted == false))
                .ToList();
        }
        
        /// <summary>
        /// 根据账号获取用户信息
        /// </summary>
        /// <param name="accountNumber">账号</param>
        /// <returns>用户信息</returns>
        public Db_crm_contract_serviceinfo_wits_user GetUserByAccount(string accountNumber)
        {
            return Queryable
                .Where(x => x.AccountNumber == accountNumber && (x.Deleted == null || x.Deleted == false))
                .First();
        }

        /// <summary>
        /// 根据用户ID获取用户信息
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户信息</returns>
        public Db_crm_contract_serviceinfo_wits_user GetUserByUserId(string userId)
        {
            return Queryable
                .Where(x => x.UserId == userId && (x.Deleted == null || x.Deleted == false))
                .First();
        }

        /// <summary>
        /// 根据申请ID和权限类型获取用户列表
        /// </summary>
        /// <param name="applId">申请ID</param>
        /// <param name="hasGtisPermission">是否有GTIS权限</param>
        /// <param name="hasGlobalSearchPermission">是否有环球搜权限</param>
        /// <param name="hasSalesWitsPermission">是否有SalesWits权限</param>
        /// <param name="hasCollegePermission">是否有学院权限</param>
        /// <returns>用户列表</returns>
        public List<Db_crm_contract_serviceinfo_wits_user> GetUsersByPermission(string applId, 
            bool? hasGtisPermission = null, 
            bool? hasGlobalSearchPermission = null, 
            bool? hasSalesWitsPermission = null, 
            bool? hasCollegePermission = null)
        {
            var query = Queryable
                .Where(x => x.WitsServeId == applId && (x.Deleted == null || x.Deleted == false));

            if (hasGtisPermission.HasValue)
                query = query.Where(x => x.GtisPermission == hasGtisPermission.Value);
            
            if (hasGlobalSearchPermission.HasValue)
                query = query.Where(x => x.GlobalSearchPermission == hasGlobalSearchPermission.Value);
            
            if (hasSalesWitsPermission.HasValue)
                query = query.Where(x => x.SalesWitsPermission == hasSalesWitsPermission.Value);
            
            if (hasCollegePermission.HasValue)
                query = query.Where(x => x.CollegePermission == hasCollegePermission.Value);

            return query.ToList();
        }

        /// <summary>
        /// 根据开通状态获取用户列表
        /// </summary>
        /// <param name="applId">申请ID</param>
        /// <param name="openingStatus">开通状态</param>
        /// <returns>用户列表</returns>
        public List<Db_crm_contract_serviceinfo_wits_user> GetUsersByOpeningStatus(string applId, int? openingStatus = null)
        {
            var query = Queryable
                .Where(x => x.WitsServeId == applId && (x.Deleted == null || x.Deleted == false));

            if (openingStatus.HasValue)
                query = query.Where(x => x.OpeningStatus == openingStatus.Value);

            return query.ToList();
        }


        public List<GetWitsApplyInfo4Audit_Out_RegisterInfo_User> GetUserListByWitsId(string witsApplId)
        {
            return Queryable
                .Where(e => e.WitsServeId == witsApplId)
                .Where(e => e.Deleted == false)
                .Select(e => new GetWitsApplyInfo4Audit_Out_RegisterInfo_User
                {
                    Id = e.Id,
                    AccountNumber = e.AccountNumber,
                    AccountType = e.AccountType.Value,
                    OpeningStatus = e.OpeningStatus.Value,
                    SharePeopleNum = e.SharePeopleNum.Value,
                    GtisPermission = e.GtisPermission,
                    VipPermission = e.VipPermission,
                    GlobalSearchPermission = e.GlobalSearchPermission,
                    CollegePermission = e.CollegePermission,
                    SalesWitsPermission = e.SalesWitsPermission,
                    SalesWitsBindPhoneId = e.SalesWitsPhoneId
                })
                .ToList();
        }
    }
} 