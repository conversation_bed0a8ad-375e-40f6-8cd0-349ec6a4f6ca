﻿using CRM2_API.BLL.Common;
using CRM2_API.Common;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.Model.BLLModel.ServiceOpening;
using System;
using System.Collections.Generic;
using System.Linq;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.System;

namespace CRM2_API.BLL.ServiceOpening
{
    /// <summary>
    /// 服务开通 - 数据准备
    /// </summary>
    public partial class BLL_ServiceOpening
    {
        #region 数据准备
        
        /// <summary>
        /// 准备开通参数
        /// </summary>
        /// <param name="serviceId">服务ID（crm_contract_serviceinfo_wits表ID）</param>
        /// <returns>开通参数</returns>
        private ServiceOpeningParams PrepareOpeningParams(string serviceId)
        {
            // 先判断开通类型，然后准备参数
            var openingType = DetermineOpeningTypeEarly(serviceId);
            return PrepareOpeningParams(serviceId, false, openingType);
        }

        /// <summary>
        /// 准备开通参数
        /// </summary>
        /// <param name="serviceId">服务ID（crm_contract_serviceinfo_wits表ID）</param>
        /// <param name="isTestMode">是否为测试模式</param>
        /// <param name="openingType">开通类型</param>
        /// <param name="operatorId">操作人ID（可选，用于人工审核下发）</param>
        /// <returns>开通参数</returns>
        private ServiceOpeningParams PrepareOpeningParams(string serviceId, bool isTestMode, ServiceOpeningType openingType, string operatorId = null)
        {
            var openingParams = new ServiceOpeningParams();

            try
            {
                // 设置测试模式和开通类型
                openingParams.IsTestMode = isTestMode;
                openingParams.OpeningType = openingType;

                LogUtil.AddLog($"准备开通参数，服务ID: {serviceId}, 测试模式: {isTestMode}, 开通类型: {openingType}");

                // 1. 获取主服务信息
                var mainService = DbOpe_crm_contract_serviceinfo_wits.Instance
                    .GetData(x => x.Id == serviceId);
                if (mainService == null)
                {
                    openingParams.SetError("未找到服务申请记录");
                    return openingParams;
                }
                openingParams.MainService = mainService;
                
                // 2. 获取合同信息
                //var contract = DbOpe_crm_contract.Instance.GetContractAndReceiptByContractId(mainService.ContractId);
                var contract = DbOpe_crm_contract.Instance.GetData(c=>c.Id == mainService.ContractId);
                if (contract == null)
                {
                    openingParams.SetError("未找到合同信息");
                    return openingParams;
                }
                openingParams.Contract = contract;
                
                // 3. 获取用户权限配置
                var users = DbOpe_crm_contract_serviceinfo_wits_user.Instance
                    .GetUsersByServelId(serviceId);
                openingParams.Users = users;

                // 4. 设置操作人信息
                if (!string.IsNullOrEmpty(operatorId))
                {
                    openingParams.OperatorId = operatorId;
                    // 获取操作人姓名
                    try
                    {
                        var user = DbOpe_sys_user.Instance.GetDbSysUserById(operatorId);
                        openingParams.OperatorName = user?.Name ?? operatorId;
                    }
                    catch (Exception ex)
                    {
                        LogUtil.AddErrorLog($"获取操作人姓名失败，用户ID：{operatorId}，错误：{ex.Message}");
                        openingParams.OperatorName = operatorId; // 使用ID作为备用
                    }
                }

                // 5. 加载各服务的具体参数
                LoadServiceSpecificParams(openingParams);

                openingParams.SetValid();
                
            }
            catch (Exception ex)
            {
                var errorMsg = $"准备开通参数异常，服务ID: {serviceId}, 错误: {ex.Message}";
                LogUtil.AddErrorLog($"{errorMsg}，详细异常: {ex}");
                throw new ApiException(errorMsg);
            }

            // 注意：新方案通过CurrentXxxServiceId字段直接引用历史配置，不再需要数据继承

            return openingParams;
        }
        /// <summary>
        /// 加载各服务具体参数
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        private void LoadServiceSpecificParams(ServiceOpeningParams openingParams)
        {
            var applId = openingParams.MainService.WitsApplId;

            // 【重要】无论是否需要GTIS服务，都要加载GTIS相关数据和执行预处理分析
            // 因为GTIS账号是其他服务的基础，即使不开通GTIS服务，也需要处理权限变更
            // 但是是否有GTIS权限是不一定的 还是要判断
            LoadGtisRelatedDataAlways(openingParams, applId);

            // 执行GTIS预处理分析（无论是否开通GTIS都需要，用于权限变更检测）
            openingParams.GtisPreprocessResult = AnalyzeGtisOperation(openingParams);

            // 仅在需要GTIS服务时执行特殊处理逻辑
            if (openingParams.MainService.HasGtisApp == true)
            {
                // 执行GTIS特殊处理逻辑
                ValidateProductGtisRelation(openingParams);
                ValidateSharingTimesLogic(openingParams);
                ProcessCountryFiltering(openingParams);
            }

            // 加载环球搜相关数据
            if (openingParams.MainService.HasGlobalSearchApp == true)
            {
                LoadGlobalSearchRelatedData(openingParams, applId);
            }

            // 加载SaleWits相关数据
            if (openingParams.MainService.HasSalesWitsApp == true)
            {
                LoadSaleWitsRelatedData(openingParams, applId);
            }

            // 加载慧思学院相关数据
            if (openingParams.MainService.HasCollegeApp == true)
            {
                LoadCollegeRelatedData(openingParams, applId);
            }
        }
        
        /// <summary>
        /// 加载GTIS相关数据（无论是否需要GTIS服务都要加载，因为GTIS账号是其他服务的基础）
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <param name="applId">申请ID</param>
        private void LoadGtisRelatedDataAlways(ServiceOpeningParams openingParams, string applId)
        {
            try
            {
                LogUtil.AddLog("开始加载GTIS相关数据（无论是否需要GTIS服务，因为GTIS账号是其他服务的基础）");

                // 1. 从Wits主服务表读取基本配置（通用配置）
                var witsService = openingParams.MainService;

                // 2. 根据是否需要变更来获取GTIS特殊配置
                Db_crm_contract_serviceinfo_gtis gtisSpecialParams = null;

                if (openingParams.MainService.IsGtisAudit)
                {
                    // 需要变更：查询当前申请的新特殊配置
                    gtisSpecialParams = DbOpe_crm_contract_serviceinfo_gtis.Instance
                        .GetData(x => x.WitsApplId == applId && x.IsProcessed == 0 && x.IsChanged == 0 && x.IsApplHistory == false && x.Deleted == false);
                    if (gtisSpecialParams != null)
                    {
                        LogUtil.AddLog($"GTIS变更特殊配置加载成功，申请ID: {applId}");
                    }
                }
                else
                {
                    // 不需要变更：通过当前生效的服务配置ID获取历史配置
                    var currentGtisServiceId = openingParams.MainService.CurrentGtisServiceId;
                    if (!string.IsNullOrEmpty(currentGtisServiceId))
                    {
                        gtisSpecialParams = DbOpe_crm_contract_serviceinfo_gtis.Instance
                            .GetData(x => x.Id == currentGtisServiceId && x.Deleted == false);
                        if (gtisSpecialParams != null)
                        {
                            LogUtil.AddLog($"GTIS历史特殊配置加载成功，配置ID: {currentGtisServiceId}");
                        }
                        else
                        {
                            var errorMsg = $"未找到GTIS历史配置，配置ID: {currentGtisServiceId}";
                            LogUtil.AddErrorLog(errorMsg);
                            throw new ApiException(errorMsg);
                        }
                    }
                    else
                    {
                        // 如果需要GTIS服务但没有历史配置ID，这是数据异常
                        if (openingParams.MainService.HasGtisApp == true)
                        {
                            var errorMsg = "需要GTIS服务但当前生效的GTIS服务配置ID为空，数据异常";
                            LogUtil.AddErrorLog(errorMsg);
                            throw new ApiException(errorMsg);
                        }
                        else
                        {
                            LogUtil.AddLog("不需要GTIS服务，当前生效的GTIS服务配置ID为空是正常的");
                        }
                    }
                }

                // 3. 构建GTIS基础配置
                var gtisBasicConfig = new GtisBasicConfig
                {
                    ContractId = openingParams.Contract.Id,
                    HasAppPermission = openingParams.MainService.HasGtisApp == true,
                    MaxAccountsNum = (witsService.GtisPrimaryAccountsNum ?? 0) + (witsService.GtisSubAccountsNum ?? 0), // 基类通用字段
                    PrimaryAccountsNum = witsService.GtisPrimaryAccountsNum,
                    SubAccountsNum = witsService.GtisSubAccountsNum,
                    AuthorizationNum = witsService.GtisAuthorizationNum,
                    ServiceCycleStart = witsService.GtisServiceCycleStart,
                    ServiceCycleEnd = witsService.GtisServiceCycleEnd
                };

                // 4. 构建GTIS特殊配置
                var gtisSpecialConfig = new GtisSpecialConfig();

                if (gtisSpecialParams != null)
                {
                    // 设置特殊配置的服务信息
                    gtisSpecialConfig.ServiceInfo = gtisSpecialParams;

                    // 获取关联的详细配置
                    // 获取常驻国家权限
                    var gtisCountries = DbOpe_crm_contract_serviceinfo_gtis_residentcountry.Instance
                        .GetDataList(x => x.ContractServiceInfoGtisId == gtisSpecialParams.Id);
                    gtisSpecialConfig.ResidentCountries = gtisCountries?.Select(x => x.ResidentCountry ?? 0).ToList() ?? new List<int>();

                    // 获取常驻城市权限
                    var gtisCities = DbOpe_crm_contract_serviceinfo_gtis_residentcity.Instance
                        .GetDataList(x => x.ContractServiceInfoGtisId == gtisSpecialParams.Id);
                    gtisSpecialConfig.ResidentCities = gtisCities?.Select(x => x.ResidentCity ?? 0).ToList() ?? new List<int>();

                    // 获取零售国家SID配置
                    if (gtisSpecialParams.RetailCountry == (int)EnumGtisRetailCountry.Add)
                    {
                        var gtisCountryConfigs = DbOpe_crm_contract_serviceinfo_gtis_country.Instance
                            .GetDataList(x => x.ContractServiceInfoGtisId == gtisSpecialParams.Id);
                        gtisSpecialConfig.CountrySids = gtisCountryConfigs?.Where(x => x.Sid.HasValue).Select(x => x.Sid.Value).ToArray() ?? new int[0];
                    }
                }

                // 5. 设置到开通参数中
                openingParams.GtisBasicConfig = gtisBasicConfig;
                openingParams.GtisSpecialConfig = gtisSpecialConfig;

                LogUtil.AddLog($"GTIS配置构建成功，主账号: {gtisBasicConfig.PrimaryAccountsNum}, 子账号: {gtisBasicConfig.SubAccountsNum}, 服务周期: {gtisBasicConfig.ServiceCycleStart?.ToString("yyyy-MM-dd")} 至 {gtisBasicConfig.ServiceCycleEnd?.ToString("yyyy-MM-dd")}, 国家数量: {gtisSpecialConfig.ResidentCountries?.Count ?? 0}");

            }
            catch (Exception ex)
            {
                throw new ApiException($"加载GTIS相关数据异常，申请ID: {applId}, 错误: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 加载环球搜相关数据
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <param name="applId">申请ID（WitsApplId）</param>
        private void LoadGlobalSearchRelatedData(ServiceOpeningParams openingParams, string applId)
        {
            try
            {
                LogUtil.AddLog("开始加载环球搜相关数据");

                // 检查是否需要环球搜服务
                if (openingParams.MainService.HasGlobalSearchApp != true)
                {
                    LogUtil.AddLog($"当前服务组合不包含环球搜应用权限，跳过环球搜配置加载");
                    return;
                }

                // 1. 从Wits主服务表读取基本配置（通用配置）
                var witsService = openingParams.MainService;
                LogUtil.AddLog("环球搜基本配置读取完成");

                // 2. 根据是否需要变更来获取环球搜特殊配置
                Db_crm_contract_serviceinfo_globalsearch globalSearchSpecialParams = null;

                if (openingParams.MainService.IsGlobalSearchAudit)
                {
                    // 需要变更：查询当前申请的新特殊配置
                    globalSearchSpecialParams = DbOpe_crm_contract_serviceinfo_globalsearch.Instance
                        .GetData(x => x.WitsApplId == applId && x.IsProcessed == (int)EnumGtisServiceIsProcess.Not && x.IsChanged == false && x.IsHistory == false);
                    if (globalSearchSpecialParams != null)
                    {
                        LogUtil.AddLog($"环球搜变更特殊配置加载成功，申请ID: {applId}");
                    }
                }
                else
                {
                    // 不需要变更：通过当前生效的服务配置ID获取历史配置
                    var currentGlobalSearchServiceId = openingParams.MainService.CurrentGlobalSearchServiceId;
                    if (!string.IsNullOrEmpty(currentGlobalSearchServiceId))
                    {
                        globalSearchSpecialParams = DbOpe_crm_contract_serviceinfo_globalsearch.Instance
                            .GetData(x => x.Id == currentGlobalSearchServiceId && x.Deleted == false);
                        if (globalSearchSpecialParams != null)
                        {
                            LogUtil.AddLog($"环球搜历史特殊配置加载成功，配置ID: {currentGlobalSearchServiceId}");
                        }
                        else
                        {
                            var errorMsg = $"未找到环球搜历史配置，配置ID: {currentGlobalSearchServiceId}";
                            LogUtil.AddErrorLog(errorMsg);
                            throw new ApiException(errorMsg);
                        }
                    }
                    else
                    {
                        // 如果需要环球搜服务但没有历史配置ID，这是数据异常
                        if (openingParams.MainService.HasGlobalSearchApp == true)
                        {
                            var errorMsg = "需要环球搜服务但当前生效的环球搜服务配置ID为空，数据异常";
                            LogUtil.AddErrorLog(errorMsg);
                            throw new ApiException(errorMsg);
                        }
                        else
                        {
                            LogUtil.AddLog("不需要环球搜服务，当前生效的环球搜服务配置ID为空是正常的");
                        }
                    }
                }

                // 3. 构建环球搜基础配置
                var totalAccounts = witsService.GlobalSearchAccountsNum ?? 0;
                var globalSearchBasicConfig = new GlobalSearchBasicConfig
                {
                    ContractId = openingParams.Contract.Id,
                    HasAppPermission = true,
                    MaxAccountsNum = totalAccounts, // 基类通用字段
                    PrimaryAccountsNum = totalAccounts > 0 ? 1 : 0, // 主账号固定为1
                    SubAccountsNum = totalAccounts > 1 ? totalAccounts - 1 : 0, // 剩余为子账号
                    ServiceCycleStart = witsService.GlobalSearchServiceCycleStart,
                    ServiceCycleEnd = witsService.GlobalSearchServiceCycleEnd
                };

                // 4. 构建环球搜特殊配置
                var globalSearchSpecialConfig = new GlobalSearchSpecialConfig();
                if (globalSearchSpecialParams != null)
                {
                    // 设置特殊配置的服务信息
                    globalSearchSpecialConfig.ServiceInfo = globalSearchSpecialParams;
                }

                // 5. 设置基础配置和特殊配置到开通参数中
                openingParams.GlobalSearchBasicConfig = globalSearchBasicConfig;
                openingParams.GlobalSearchSpecialConfig = globalSearchSpecialConfig;

                // 6. 执行环球搜预处理分析（在所有基础数据加载完成后）
                openingParams.GlobalSearchPreprocessResult = AnalyzeGlobalSearchOperation(openingParams, globalSearchSpecialParams);
                LogUtil.AddLog($"环球搜预处理分析完成，操作类型: {openingParams.GlobalSearchPreprocessResult.OperationType}");

                // 7. 获取历史环球搜码（用于服务变更时传递给GTIS系统）
                LoadHistoryGlobalSearchCodes(openingParams);

                LogUtil.AddLog($"环球搜配置构建成功，主账号: {globalSearchBasicConfig.PrimaryAccountsNum}, 子账号: {globalSearchBasicConfig.SubAccountsNum}, 服务周期: {globalSearchBasicConfig.ServiceCycleStart?.ToString("yyyy-MM-dd")} 至 {globalSearchBasicConfig.ServiceCycleEnd?.ToString("yyyy-MM-dd")}");
                LogUtil.AddLog("环球搜相关数据加载完成");
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"加载环球搜相关数据异常，申请ID: {applId}, 错误: {ex}");
                throw new ApiException($"加载环球搜相关数据异常，申请ID: {applId}, 错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 加载历史环球搜码（用于服务变更时传递给GTIS系统）
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        private void LoadHistoryGlobalSearchCodes(ServiceOpeningParams openingParams)
        {
            try
            {
                LogUtil.AddLog("开始加载历史环球搜码");

                // 根据开通类型确定查询的客户编码
                string queryCustomerCode = GetQueryCustomerCode(openingParams);

                if (string.IsNullOrEmpty(queryCustomerCode))
                {
                    LogUtil.AddLog("未找到有效的客户编码，无法查询历史环球搜码");
                    return;
                }

                // 查询该合同编码下的历史环球搜码
                var historyGlobalSearchCodes = DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance
                    .GetHistoryGlobalSearchCodesByContractNum(queryCustomerCode);

                if (historyGlobalSearchCodes != null && historyGlobalSearchCodes.Count > 0)
                {
                    // 将历史环球搜码保存到开通参数中，供后续使用
                    if (openingParams.GlobalSearchSpecialConfig == null)
                    {
                        openingParams.GlobalSearchSpecialConfig = new GlobalSearchSpecialConfig();
                    }

                    // 保存历史环球搜码字典（包含账户类型信息）
                    openingParams.GlobalSearchSpecialConfig.HistoryGlobalSearchCodes = historyGlobalSearchCodes;

                    LogUtil.AddLog($"加载历史环球搜码成功，数量: {historyGlobalSearchCodes.Count}, 码列表: {string.Join(", ", historyGlobalSearchCodes.Keys)}");
                }
                else
                {
                    LogUtil.AddLog("未找到历史环球搜码");
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"加载历史环球搜码异常，错误: {ex.Message}");
                // 不抛出异常，避免影响整个开通流程
            }
        }

        /// <summary>
        /// 加载SaleWits相关数据（包含资源下发数据准备）
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <param name="applId">申请ID</param>
        private void LoadSaleWitsRelatedData(ServiceOpeningParams openingParams, string applId)
        {
            try
            {
                LogUtil.AddLog("开始加载SaleWits相关数据");

                // 检查是否需要SalesWits服务
                if (openingParams.MainService.HasSalesWitsApp != true)
                {
                    LogUtil.AddLog($"当前服务组合不包含SalesWits应用权限，跳过SalesWits配置加载");
                    return;
                }

                // 1. 从Wits主服务表读取基本配置（通用配置）
                var witsService = openingParams.MainService;

                // 2. 获取SalesWits特殊配置
                // 分两种情况：
                // - 需要变更：使用当前申请的配置（包含本次要下发的资源）
                // - 不需要变更：使用历史配置（获取基础信息），但资源下发数量应该为0
                Db_crm_contract_serviceinfo_saleswits salesWitsSpecialParams = null;

                if (openingParams.MainService.IsSalesWitsAudit)
                {
                    // 需要变更：查询当前申请的新特殊配置
                    salesWitsSpecialParams = _dbOpe_crm_contract_serviceinfo_saleswits
                        .GetData(x => x.WitsApplId == applId && x.IsProcessed == (int)EnumGtisServiceIsProcess.Not && x.Deleted == false && x.IsHistory == false && x.IsChanged == false);
                    if (salesWitsSpecialParams != null)
                    {
                        LogUtil.AddLog($"SalesWits变更特殊配置加载成功，申请ID: {applId}");
                    }
                }
                else
                {
                    // 不需要变更：通过当前生效的服务配置ID获取历史配置
                    var currentSalesWitsServiceId = openingParams.MainService.CurrentSalesWitsServiceId;
                    if (!string.IsNullOrEmpty(currentSalesWitsServiceId))
                    {
                        salesWitsSpecialParams = _dbOpe_crm_contract_serviceinfo_saleswits
                            .GetData(x => x.Id == currentSalesWitsServiceId && x.Deleted == false);
                        if (salesWitsSpecialParams != null)
                        {
                            LogUtil.AddLog($"SalesWits历史特殊配置加载成功，配置ID: {currentSalesWitsServiceId}");

                            // 重要：不需要变更时，清空资源下发数量，避免重复下发
                            // 但保留基础信息（如账号数量、服务时间等）用于判断下发类型
                            salesWitsSpecialParams = new Db_crm_contract_serviceinfo_saleswits
                            {
                                // 保留基础信息
                                Id = salesWitsSpecialParams.Id,
                                ContractId = salesWitsSpecialParams.ContractId,
                                WitsApplId = salesWitsSpecialParams.WitsApplId,
                                AccountsNum = salesWitsSpecialParams.AccountsNum,
                                ServiceMonth = salesWitsSpecialParams.ServiceMonth,
                                HistoryId = salesWitsSpecialParams.HistoryId,
                                IsProcessed = salesWitsSpecialParams.IsProcessed,
                                Deleted = salesWitsSpecialParams.Deleted,

                                // 清空资源下发数量，避免重复下发历史资源
                                CurrentGiftTokens = 0,
                                CurrentGiftEmails = 0,
                                RechargeAmount = 0
                            };

                            LogUtil.AddLog("不需要变更，已清空资源下发数量，避免重复下发历史资源");
                        }
                        else
                        {
                            var errorMsg = $"未找到SalesWits历史配置，配置ID: {currentSalesWitsServiceId}";
                            LogUtil.AddErrorLog(errorMsg);
                            throw new ApiException(errorMsg);
                        }
                    }
                    else
                    {
                        // 如果需要SalesWits服务但没有历史配置ID，这是数据异常
                        if (openingParams.MainService.HasSalesWitsApp == true)
                        {
                            var errorMsg = "需要SalesWits服务但当前生效的SalesWits服务配置ID为空，数据异常";
                            LogUtil.AddErrorLog(errorMsg);
                            throw new ApiException(errorMsg);
                        }
                        else
                        {
                            LogUtil.AddLog("不需要SalesWits服务，当前生效的SalesWits服务配置ID为空是正常的");
                        }
                    }
                }

                // 3. 构建SalesWits基础配置
                var salesWitsBasicConfig = new SalesWitsBasicConfig
                {
                    ContractId = openingParams.Contract.Id,
                    HasAppPermission = true,
                    MaxAccountsNum = witsService.SalesWitsAccountsNum, // 基类通用字段
                    ServiceCycleStart = witsService.SalesWitsServiceCycleStart,
                    ServiceCycleEnd = witsService.SalesWitsServiceCycleEnd
                };

                // 4. 构建SalesWits特殊配置
                var salesWitsSpecialConfig = new SalesWitsSpecialConfig();

                if (salesWitsSpecialParams != null)
                {
                    // 设置ResourceManagementId
                    if (string.IsNullOrEmpty(salesWitsSpecialParams.ResourceManagementId))
                    {
                        salesWitsSpecialParams.ResourceManagementId = DetermineResourceManagementId(openingParams);
                        LogUtil.AddLog($"设置ResourceManagementId: {salesWitsSpecialParams.ResourceManagementId}");
                    }

                    // 设置特殊配置的服务信息
                    salesWitsSpecialConfig.ServiceInfo = salesWitsSpecialParams;

                    // 5. 执行SaleWits资源下发数据准备
                    var resourceDataResult = PrepareSaleWitsResourceData(openingParams, salesWitsSpecialParams);
                    salesWitsSpecialConfig.ResourceDataResult = resourceDataResult;

                    LogUtil.AddLog($"SaleWits资源下发数据准备完成: {resourceDataResult.Message}");
                }

                // 6. 设置到开通参数中
                openingParams.SalesWitsBasicConfig = salesWitsBasicConfig;
                openingParams.SalesWitsSpecialConfig = salesWitsSpecialConfig;

                LogUtil.AddLog($"SalesWits配置构建成功，账号数量: {salesWitsBasicConfig.MaxAccountsNum}, 服务周期: {salesWitsBasicConfig.ServiceCycleStart?.ToString("yyyy-MM-dd")} 至 {salesWitsBasicConfig.ServiceCycleEnd?.ToString("yyyy-MM-dd")}");

            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"加载SaleWits相关数据异常，申请ID: {applId}, 错误: {ex}");
                throw new ApiException($"加载SaleWits相关数据异常，申请ID: {applId}, 错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 加载慧思学院相关数据
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <param name="applId">申请ID</param>
        private void LoadCollegeRelatedData(ServiceOpeningParams openingParams, string applId)
        {
            try
            {
                // 检查是否需要慧思学院服务
                if (openingParams.MainService.HasCollegeApp != true)
                {
                    LogUtil.AddLog($"当前服务组合不包含慧思学院应用权限，跳过慧思学院配置加载");
                    return;
                }

                // 1. 从Wits主服务表读取基本配置（通用配置）
                var witsService = openingParams.MainService;

                // 2. 根据是否需要变更来获取慧思学院特殊配置
                Db_crm_contract_serviceinfo_college collegeSpecialParams = null;

                if (openingParams.MainService.IsCollegeAudit)
                {
                    // 需要变更：查询当前申请的新特殊配置
                    collegeSpecialParams = _dbOpe_crm_contract_serviceinfo_college
                        .GetData(x => x.WitsApplId == applId && x.IsProcessed == (int)EnumGtisServiceIsProcess.Not && x.Deleted == false && x.IsChanged == false && x.IsChanged == false);
                    if (collegeSpecialParams != null)
                    {
                        LogUtil.AddLog($"慧思学院变更特殊配置加载成功，申请ID: {applId}");
                    }
                }
                else
                {
                    // 不需要变更：通过当前生效的服务配置ID获取历史配置
                    var currentCollegeServiceId = openingParams.MainService.CurrentCollegeServiceId;
                    if (!string.IsNullOrEmpty(currentCollegeServiceId))
                    {
                        collegeSpecialParams = _dbOpe_crm_contract_serviceinfo_college
                            .GetData(x => x.Id == currentCollegeServiceId && x.Deleted == false);
                        if (collegeSpecialParams != null)
                        {
                            LogUtil.AddLog($"慧思学院历史特殊配置加载成功，配置ID: {currentCollegeServiceId}");
                        }
                        else
                        {
                            LogUtil.AddLog($"未找到慧思学院历史配置，配置ID: {currentCollegeServiceId}");
                        }
                    }
                    else
                    {
                        LogUtil.AddLog("当前生效的慧思学院服务配置ID为空，跳过历史配置加载");
                    }
                }

                // 3. 构建慧思学院基础配置
                var collegeBasicConfig = new CollegeBasicConfig
                {
                    ContractId = openingParams.Contract.Id,
                    HasAppPermission = true,
                    MaxAccountsNum = witsService.CollegeAccountsNum, // 基类通用字段
                    ServiceCycleStart = witsService.CollegeServiceCycleStart,
                    ServiceCycleEnd = witsService.CollegeServiceCycleEnd
                };

                // 4. 构建慧思学院特殊配置
                var collegeSpecialConfig = new CollegeSpecialConfig();

                if (collegeSpecialParams != null)
                {
                    // 设置特殊配置的服务信息
                    collegeSpecialConfig.ServiceInfo = collegeSpecialParams;
                }

                // 5. 设置到开通参数中
                openingParams.CollegeBasicConfig = collegeBasicConfig;
                openingParams.CollegeSpecialConfig = collegeSpecialConfig;

                LogUtil.AddLog($"慧思学院配置构建成功，账号数量: {collegeBasicConfig.MaxAccountsNum}, 服务周期: {collegeBasicConfig.ServiceCycleStart?.ToString("yyyy-MM-dd")} 至 {collegeBasicConfig.ServiceCycleEnd?.ToString("yyyy-MM-dd")}");

            }
            catch (Exception ex)
            {
                throw new ApiException($"加载慧思学院相关数据异常，申请ID: {applId}, 错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 判断续约是否为重新开通模式
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <returns>true表示重新开通，false表示续期服务</returns>
        private bool IsRenewalWithNewAccount(ServiceOpeningParams openingParams)
        {
            try
            {
                // 从主服务配置中获取账号生成方式
                var accountGenerationMethod = openingParams.MainService.AccountGenerationMethod;

                if (accountGenerationMethod.HasValue)
                {
                    // 0: 重新生成（重新开通）, 1: 使用原有（续期服务）
                    bool isRemake = accountGenerationMethod.Value == (int)EnumGtisAccountGenerationMethod.Remake;
                    LogUtil.AddLog($"续约账号生成方式: {(isRemake ? "重新生成" : "使用原有")}");
                    return isRemake;
                }

                // 如果没有设置账号生成方式，默认为续期服务
                LogUtil.AddLog("续约账号生成方式未设置，默认为续期服务");
                return false;
            }
            catch (Exception ex)
            {
                var errorMsg = $"判断续约账号生成方式异常，合同ID: {openingParams.Contract?.Id}, 错误: {ex.Message}";
                LogUtil.AddErrorLog($"{errorMsg}，详细异常: {ex}");
                throw new ApiException(errorMsg);
            }
        }

        #endregion

        #region GTIS特殊处理逻辑
        
        /// <summary>
        /// 校验产品GTIS关联配置
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        private void ValidateProductGtisRelation(ServiceOpeningParams openingParams)
        {
            try
            {
                // 检查是否有GTIS特殊配置
                if (openingParams.GtisSpecialConfig?.ServiceInfo == null)
                {
                    LogUtil.AddLog("GTIS特殊配置为空，跳过产品关联校验");
                    return;
                }

                var gtisRelation = DbOpe_crm_product_gtis_relation.Instance.GetData(r => r.ProductId == openingParams.GtisSpecialConfig.ServiceInfo.ProductId);
                if (gtisRelation == null)
                {
                    throw new ArgumentException("该服务产品与GTIS系统连接未配置");
                }

                // 保存产品关联信息供后续使用
                openingParams.GtisSpecialConfig.ProductRelation = gtisRelation;
                
                LogUtil.AddLog($"GTIS产品关联校验通过，客户类型: {gtisRelation.GTIS_ProductCustomerType}");
            }
            catch (Exception ex)
            {
                throw new ApiException($"GTIS产品关联校验失败，错误: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 校验共享使用总数逻辑
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        private void ValidateSharingTimesLogic(ServiceOpeningParams openingParams)
        {
            try
            {
                // 检查是否有GTIS特殊配置
                if (openingParams.GtisSpecialConfig?.ServiceInfo == null)
                {
                    LogUtil.AddLog("GTIS特殊配置为空，跳过共享使用总数校验");
                    return;
                }

                var gtisParams = openingParams.GtisSpecialConfig.ServiceInfo;
                var users = openingParams.Users;
                
                if (users == null || users.Count == 0)
                {
                    LogUtil.AddLog("无用户信息，跳过共享使用总数校验");
                    return;
                }
                
                // 计算所有用户的共享人数总和
                var totalUserSharingTimes = users.Sum(u => u.SharePeopleNum ?? 0);
                
                if (gtisParams.ShareUsageNum == null || gtisParams.ShareUsageNum.Value == 0)
                {
                    // 共享使用总数没写，自动设置为所有用户数+10
                    var calculatedSharingTimes = totalUserSharingTimes + 10;
                    LogUtil.AddLog($"共享使用总数未设置，自动计算为: {calculatedSharingTimes} (用户总数{totalUserSharingTimes} + 10)");
                    // 这里只记录，实际设置在格式化阶段进行
                }
                else
                {
                    // 校验共享使用总数是否足够
                    if (gtisParams.ShareUsageNum.Value < totalUserSharingTimes)
                    {
                        throw new ArgumentException($"绑定使用总次数{gtisParams.ShareUsageNum.Value}不能小于每个账号绑定人数之和（{totalUserSharingTimes}）");
                    }
                    LogUtil.AddLog($"共享使用总数校验通过: 设置值{gtisParams.ShareUsageNum.Value} >= 用户总数{totalUserSharingTimes}");
                }
            }
            catch (Exception ex)
            {
                throw new ApiException($"共享使用总数校验失败，错误: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 处理特殊国家过滤逻辑
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        private void ProcessCountryFiltering(ServiceOpeningParams openingParams)
        {
            try
            {
                var sidsRemoveList = new List<int>();
                var contract = openingParams.Contract;
                // 检查是否有GTIS特殊配置
                if (openingParams.GtisSpecialConfig?.ServiceInfo == null)
                {
                    LogUtil.AddLog("GTIS特殊配置为空，跳过国家过滤处理");
                    return;
                }

                var gtisParams = openingParams.GtisSpecialConfig.ServiceInfo;

                // 获取产品信息用于特殊过滤
                var product = DbOpe_crm_product.Instance.QueryByPrimaryKey(gtisParams.ProductId);
                if (product == null)
                {
                    LogUtil.AddLog("未找到产品信息，跳过国家过滤");
                    return;
                }
                
                // 境外客户特殊国家过滤
                if (contract.IsOverseasCustomer == true && product.ProductType != (int)EnumProductType.Vip)
                {
                    if (contract.SalesCountry.HasValue)
                    {
                        // 获取客户所在地区需要删除的sid列表
                        var overseasRemoveSids = DbOpe_crm_product_rules_localdatasourcesnotsupport.Instance.GetSidsRemoveList(contract.SalesCountry.Value);
                        sidsRemoveList.AddRange(overseasRemoveSids);
                        LogUtil.AddLog($"境外客户，销售国家ID: {contract.SalesCountry.Value}, 过滤不支持当地销售的国家数量: {overseasRemoveSids.Count}");

                        // 港澳台地区ID列表
                        var HK_MO_TWNIdList = new List<int>() { 32, 33, 34 };
                        if (!HK_MO_TWNIdList.Contains(contract.SalesCountry.Value))
                        {
                            // 非港澳台地区，需要去掉只支持中国地区的数据
                            var supportChinaSids = DbOpe_crm_product_rules_supportchina.Instance.GetSidsRemoveList();
                            sidsRemoveList.AddRange(supportChinaSids);
                            LogUtil.AddLog($"境外客户非港澳台地区，过滤仅支持中国地区的国家数量: {supportChinaSids.Count}");
                        }
                        else
                        {
                            // 港澳台地区，需要根据仅支持中国地区数据对港澳台客户开放截止日，计算要删除的sid列表
                            var supportChinaHKSids = DbOpe_crm_product_rules_supportchina.Instance.GetSidsRemoveList4HK_MO_TWN();
                            sidsRemoveList.AddRange(supportChinaHKSids);
                            LogUtil.AddLog($"境外客户港澳台地区，过滤已过开放截止日的仅支持中国地区国家数量: {supportChinaHKSids.Count}");
                        }
                    }
                    else
                    {
                        LogUtil.AddLog($"境外客户但销售国家为空，跳过境外客户特殊过滤");
                    }
                }
                
                // 48800以下产品要去除的国家
                if (product.ProductType != (int)EnumProductType.Vip && 
                    (product.Id == "761c3f51-afd0-4087-8cd3-9f9db8225cad" || product.Id == "716c3f51-afd0-4087-8cd3-9f9db8225cad"))
                {
                    var abovePricedSids = DbOpe_crm_product_rules_abovepricedsupport.Instance.GetSidsRemoveList();
                    sidsRemoveList.AddRange(abovePricedSids);
                    LogUtil.AddLog($"48800以下产品，过滤特殊国家数量: {abovePricedSids.Count}");
                }
                
                // 保存过滤后的国家列表
                openingParams.GtisSidsRemove = sidsRemoveList.Distinct().ToArray();
                
                if (sidsRemoveList.Count > 0)
                {
                    LogUtil.AddLog($"国家过滤完成，移除国家数量: {sidsRemoveList.Count}");
                }
            }
            catch (Exception ex)
            {
                // 国家过滤失败不应该中断流程，设置为空数组并记录警告
                LogUtil.AddLog($"国家过滤处理失败，将使用空过滤列表: {ex.Message}");
                openingParams.GtisSidsRemove = Array.Empty<int>();
            }
        }

        /// <summary>
        /// 分析环球搜操作类型并预处理数据
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <param name="globalSearchSpecialParams">环球搜特殊配置</param>
        /// <returns>预处理结果</returns>
        private GlobalSearchPreprocessResult AnalyzeGlobalSearchOperation(ServiceOpeningParams openingParams, Db_crm_contract_serviceinfo_globalsearch globalSearchSpecialParams)
        {
            var result = new GlobalSearchPreprocessResult();

            try
            {
                LogUtil.AddLog("开始分析环球搜操作类型");

                // 1. 根据开通类型决定查询策略
                var openingType = DetermineOpeningType(openingParams);

                if (openingType == ServiceOpeningType.NewAccount)
                {
                    // 新开通：查询当前合同的环球搜服务（通常不存在）
                    var existingAccounts = GetExistingGlobalSearchAccounts(openingParams.Contract.Id);
                    SetExistingServiceInfo(result, existingAccounts);
                    result.OperationType = GlobalSearchOperationType.NewService;

                    LogUtil.AddLog($"新开通场景，当前合同环球搜服务状态: {(result.HasExistingService ? "存在" : "不存在")}");
                }
                else if (openingType == ServiceOpeningType.Renewal)
                {
                    // 续约场景：根据账号生成方式决定操作类型
                    var renewalResult = AnalyzeRenewalGlobalSearchService(openingParams);
                    result.HasExistingService = renewalResult.HasExistingService;
                    result.ExistingAccounts = renewalResult.ExistingAccounts;
                    result.ExistingPrimaryCode = renewalResult.ExistingPrimaryCode;
                    result.ExistingSubCodes = renewalResult.ExistingSubCodes;
                    result.ExistingAccountCount = renewalResult.ExistingAccountCount;

                    // 根据账号生成方式决定操作类型
                    if (IsRenewalWithNewAccount(openingParams))
                    {
                        result.OperationType = GlobalSearchOperationType.NewService;
                        LogUtil.AddLog($"续约场景（重新开通），操作类型: NewService，原合同环球搜服务状态: {(result.HasExistingService ? "存在" : "不存在")}");
                    }
                    else
                    {   
                        if (result.ExistingPrimaryCode == null)
                        {
                            throw new ApiException("原合同找不到环球搜服务，无法续约");
                        }
                        result.OperationType = GlobalSearchOperationType.UpdateService;
                        LogUtil.AddLog($"续约场景（续期服务），操作类型: UpdateService，原合同环球搜服务状态: {(result.HasExistingService ? "存在" : "不存在")}");
                    }
                }
                else if (openingType == ServiceOpeningType.Change)
                {
                    // 变更场景：查询当前合同的环球搜服务
                    var existingAccounts = GetExistingGlobalSearchAccounts(openingParams.Contract.Id);
                    SetExistingServiceInfo(result, existingAccounts);
                    // 根据是否存在现有服务决定操作类型
                    if (result.HasExistingService && result.ExistingPrimaryCode != null)
                    {
                        result.OperationType = GlobalSearchOperationType.UpdateService;
                        LogUtil.AddLog($"变更场景，发现现有环球搜服务，主账号: {result.ExistingPrimaryCode}, 子账号数量: {result.ExistingSubCodes.Count}");
                    }
                    else
                    {
                        result.OperationType = GlobalSearchOperationType.NewService;
                        LogUtil.AddLog("变更场景，未发现现有环球搜服务或者未找到环球搜主账号，作为新增处理");
                    }
                }

                LogUtil.AddLog($"环球搜操作类型分析完成: {result.OperationType}");

            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"分析环球搜操作类型异常: {ex}");
                throw new ApiException($"环球搜预处理分析失败: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 分析续约场景的环球搜服务状态
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <returns>续约分析结果</returns>
        private GlobalSearchPreprocessResult AnalyzeRenewalGlobalSearchService(ServiceOpeningParams openingParams)
        {
            var result = new GlobalSearchPreprocessResult();

            try
            {
                LogUtil.AddLog("开始分析续约场景的环球搜服务状态");

                // 1. 获取被续约的原合同
                Db_crm_contract oldContract = null;

                // 方法1：通过RenewalContractNum获取原合同
                if (!string.IsNullOrEmpty(openingParams.Contract.RenewalContractNum))
                {
                    oldContract = DbOpe_crm_contract.Instance.GetContractByContractNum(openingParams.Contract.RenewalContractNum);
                    LogUtil.AddLog($"通过RenewalContractNum查找原合同: {openingParams.Contract.RenewalContractNum}");
                }

                // 方法2：如果没有明确的续约合同号，查找相同甲方公司的最近一份包含环球搜服务的历史合同
                if (oldContract == null)
                {
                    oldContract = DbOpe_crm_contract_serviceinfo_globalsearch.Instance
                        .GetLastSameFirstPartyServiceContractInfo(openingParams.Contract.FirstParty, openingParams.Contract.Id);
                    LogUtil.AddLog($"通过甲方公司查找最近的服务合同，甲方: {openingParams.Contract.FirstParty}");
                }

                // 2. 查询原合同的环球搜服务状态
                if (oldContract != null)
                {
                    LogUtil.AddLog($"找到原合同: {oldContract.ContractNum}，开始查询环球搜服务");

                    var existingAccounts = GetExistingGlobalSearchAccounts(oldContract.Id);
                    result.ExistingAccounts = existingAccounts;
                    result.HasExistingService = existingAccounts.Count > 0;

                    if (result.HasExistingService)
                    {
                        // 提取现有账号信息
                        result.ExistingPrimaryCode = existingAccounts
                            .FirstOrDefault(a => a.AccountType == EnumContractServiceGlobalSearchAccountType.PrimaryAccount)?.AccountNumber;
                        result.ExistingSubCodes = existingAccounts
                            .Where(a => a.AccountType == EnumContractServiceGlobalSearchAccountType.SubAccount)
                            .Select(a => a.AccountNumber).ToList();
                        result.ExistingAccountCount = existingAccounts.Count;


                        LogUtil.AddLog($"原合同存在环球搜服务，主账号: {result.ExistingPrimaryCode}, 子账号数量: {result.ExistingSubCodes.Count}");
                    }
                    else
                    {
                        LogUtil.AddLog("原合同不存在环球搜服务");
                    }
                }
                else
                {
                    LogUtil.AddLog("未找到被续约的原合同");
                }

            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"分析续约场景环球搜服务状态异常: {ex}");
                throw new ApiException($"续约场景分析失败: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 设置现有服务信息到预处理结果中
        /// </summary>
        /// <param name="result">预处理结果</param>
        /// <param name="existingAccounts">现有账号列表</param>
        private void SetExistingServiceInfo(GlobalSearchPreprocessResult result, List<Db_crm_contract_serviceinfo_globalsearch_user> existingAccounts)
        {
            result.ExistingAccounts = existingAccounts;
            result.HasExistingService = existingAccounts.Count > 0;

            if (result.HasExistingService)
            {
                // 提取现有账号信息
                result.ExistingPrimaryCode = existingAccounts
                    .FirstOrDefault(a => a.AccountType == EnumContractServiceGlobalSearchAccountType.PrimaryAccount)?.AccountNumber;
                result.ExistingSubCodes = existingAccounts
                    .Where(a => a.AccountType == EnumContractServiceGlobalSearchAccountType.SubAccount)
                    .Select(a => a.AccountNumber).ToList();
                result.ExistingAccountCount = existingAccounts.Count;
            }
        }

        /// <summary>
        /// 获取现有环球搜账号
        /// </summary>
        /// <param name="contractId">合同ID</param>
        /// <returns>现有账号列表</returns>
        private List<Db_crm_contract_serviceinfo_globalsearch_user> GetExistingGlobalSearchAccounts(string contractId)
        {
            // 获取该合同的现有环球搜账号
            var existingService = DbOpe_crm_contract_serviceinfo_globalsearch.Instance
                .CheckContractHasGlobalSearchServiceByContractId(contractId);

            if (existingService != null)
            {
                return DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance
                    .GetGlobalSearchUserByServiceId(existingService.Id);
            }

            return new List<Db_crm_contract_serviceinfo_globalsearch_user>();
        }

        /// <summary>
        /// 分析GTIS操作类型和准备数据
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <returns>GTIS预处理结果</returns>
        private GtisPreprocessResult AnalyzeGtisOperation(ServiceOpeningParams openingParams)
        {
            var result = new GtisPreprocessResult();

            try
            {
                LogUtil.AddLog("开始分析GTIS操作类型");

                // 1. 获取当前用户数据（统一使用wits_user，保留完整权限信息）
                result.CurrentWitsUsers = openingParams.Users;

                // 2. 判断操作类型
                var openingType = DetermineOpeningType(openingParams);

                if (openingType == ServiceOpeningType.NewAccount)
                {
                    // 首次开通：无论是否勾选GTIS，都要创建账号
                    result.OperationType = GtisOperationType.NewService;
                    result.IsFirstTimeOpening = true;
                    LogUtil.AddLog("首次开通场景，操作类型: NewService");
                }
                else if (openingType == ServiceOpeningType.Change)
                {
                    // 变更：账号必然已存在，走更新流程
                    result.OperationType = GtisOperationType.UpdateService;
                    result.IsFirstTimeOpening = false;

                    // 执行用户分类（变更场景需要）
                    result.UserClassification = ClassifyUsersForRenewalOrChange(openingParams, result.CurrentWitsUsers);
                    LogUtil.AddLog($"变更场景，操作类型: UpdateService，用户分类: {result.UserClassification.AddUsers?.Count ?? 0}新增, {result.UserClassification.UpdateUsers?.Count ?? 0}更新, {result.UserClassification.DelUsers?.Count ?? 0}删除");
                }
                else if (openingType == ServiceOpeningType.Renewal)
                {
                    // 续约：根据账号生成方式决定
                    if (IsRenewalWithNewAccount(openingParams))
                    {
                        // 重新开通：无需历史数据，直接使用当前wits_user数据
                        result.OperationType = GtisOperationType.NewService;
                        result.IsFirstTimeOpening = false; // 不是首次，但是重新开通
                        LogUtil.AddLog("续约场景（重新开通），操作类型: NewService");
                    }
                    else
                    {
                        // 续期服务：历史用户数据仍然从当前wits_user获取（因为续期时用户配置可能有调整）
                        result.OperationType = GtisOperationType.UpdateService;
                        result.IsFirstTimeOpening = false;

                        // 执行用户分类（续期场景需要）
                        result.UserClassification = ClassifyUsersForRenewalOrChange(openingParams, result.CurrentWitsUsers);
                        LogUtil.AddLog($"续约场景（续期服务），操作类型: UpdateService，用户分类: {result.UserClassification.AddUsers?.Count ?? 0}新增, {result.UserClassification.UpdateUsers?.Count ?? 0}更新, {result.UserClassification.DelUsers?.Count ?? 0}删除");
                    }
                }

                LogUtil.AddLog($"GTIS操作类型分析完成: {result.OperationType}，用户数量: {result.CurrentWitsUsers.Count}");

            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"分析GTIS操作类型异常: {ex}");
                throw new ApiException($"GTIS预处理分析失败: {ex.Message}");
            }

            return result;
        }

        #endregion

        #region ResourceManagementId处理

        /// <summary>
        /// 判断是否为服务变更
        /// </summary>
        /// <param name="openingParams">服务开通参数</param>
        /// <returns>是否为服务变更</returns>
        private bool IsServiceChange(ServiceOpeningParams openingParams)
        {
            // 检查处理类型是否为变更
            return openingParams.MainService.ProcessingType == (int)EnumProcessingType.Change;
        }

        /// <summary>
        /// 从原服务查找ResourceManagementId（统一处理服务变更和续约）
        /// </summary>
        /// <param name="openingParams">服务开通参数</param>
        /// <returns>原服务的ResourceManagementId</returns>
        /// <exception cref="ApiException">找不到原服务记录时抛出异常</exception>
        private string? FindResourceManagementIdFromOriginalService(ServiceOpeningParams openingParams)
        {
            LogUtil.AddLog("开始统一查找原服务的ResourceManagementId");

            // 使用统一的历史服务查找方法
            var historyResult = FindHistorySaleWitsService(openingParams);

            if (!historyResult.Success || historyResult.Service == null)
            {
                LogUtil.AddLog($"查找历史SaleWits服务失败: {historyResult.Message}");
                return null;
            }

            var historySaleWitsService = historyResult.Service;
            if (string.IsNullOrEmpty(historySaleWitsService.ResourceManagementId))
            {
                var errorMsg = $"历史SaleWits服务中ResourceManagementId为空，服务ID: {historyResult.HistoryServiceId}";
                LogUtil.AddLog(errorMsg);
                return null;
            }

            LogUtil.AddLog($"成功获取历史ResourceManagementId: {historySaleWitsService.ResourceManagementId}");
            return historySaleWitsService.ResourceManagementId;
        }

        /// <summary>
        /// 获取历史Wits服务ID（统一处理服务变更和续约）
        /// </summary>
        /// <param name="openingParams">服务开通参数</param>
        /// <returns>历史Wits服务ID</returns>
        private string? GetHistoryWitsServiceId(ServiceOpeningParams openingParams)
        {
            var applId = openingParams.MainService.WitsApplId;
            if (!string.IsNullOrEmpty(applId))
            {
                var applData = DbOpe_crm_contract_productserviceinfo_wits_appl.Instance
                    .GetData(x => x.Id == applId && x.Deleted == false);

                if (applData != null && !string.IsNullOrEmpty(applData.OriWitsServiceId))
                {
                    LogUtil.AddLog($"续约：从申请表OriWitsServiceId获取历史Wits服务ID: {applData.OriWitsServiceId}");
                    return applData.OriWitsServiceId;
                }
                else
                {
                    LogUtil.AddLog($"续约但申请表中OriWitsServiceId为空，申请ID: {applId}");
                    return null;
                }
            }

            LogUtil.AddLog("无法确定历史Wits服务ID的获取方式");
            return null;
        }

        /// <summary>
        /// 确定ResourceManagementId
        /// </summary>
        /// <param name="openingParams">服务开通参数</param>
        /// <returns>ResourceManagementId</returns>
        /// <exception cref="ApiException">续约或服务变更时找不到原服务记录时抛出异常</exception>
        private string DetermineResourceManagementId(ServiceOpeningParams openingParams)
        {
            string? existingResourceManagementId = null;
            // 1. 检查是否为服务变更
            if (IsServiceChange(openingParams))
            {
                // 服务变更：查找原服务的ResourceManagementId
                existingResourceManagementId = FindResourceManagementIdFromOriginalService(openingParams);
            }
            // 2. 检查是否为续约（使用原有账号）
            else if (openingParams.MainService.AccountGenerationMethod == (int)CRM2_API.Model.BLLModel.Enum.EnumGtisAccountGenerationMethod.Generate)
            {
                existingResourceManagementId = FindResourceManagementIdFromOriginalService(openingParams);
            }

            if(string.IsNullOrEmpty(existingResourceManagementId))
            {
                LogUtil.AddLog("检测到新开通服务（重新生成账号），生成新的ResourceManagementId");

                // 生成新的ResourceManagementId
                var newResourceManagementId = Guid.NewGuid().ToString();
                LogUtil.AddLog($"生成新的ResourceManagementId: {newResourceManagementId}");
                return newResourceManagementId;
            }
            return existingResourceManagementId;
        }

        #endregion

        #region 统一历史服务查找方法

        /// <summary>
        /// 统一的历史服务查找方法
        /// </summary>
        /// <typeparam name="T">服务实体类型</typeparam>
        /// <param name="openingParams">服务开通参数</param>
        /// <param name="serviceType">服务类型</param>
        /// <returns>历史服务查找结果</returns>
        public HistoryServiceResult<T> FindHistoryService<T>(ServiceOpeningParams openingParams, HistoryServiceType serviceType) where T : class
        {
            try
            {
                LogUtil.AddLog($"开始查找历史{serviceType}服务");

                // 1. 获取历史Wits服务ID
                var historyWitsServiceId = GetHistoryWitsServiceId(openingParams);
                if (string.IsNullOrEmpty(historyWitsServiceId))
                {
                    var message = $"无法获取历史Wits服务ID，无法查找历史{serviceType}服务";
                    LogUtil.AddLog(message);
                    return HistoryServiceResult<T>.CreateFailure(message, serviceType);
                }

                // 2. 通过历史Wits服务获取对应的子服务ID
                var historyWitsService = DbOpe_crm_contract_serviceinfo_wits.Instance
                    .GetData(x => x.Id == historyWitsServiceId && x.Deleted == false);

                if (historyWitsService == null)
                {
                    var message = $"未找到历史Wits服务记录，ID: {historyWitsServiceId}";
                    LogUtil.AddLog(message);
                    return HistoryServiceResult<T>.CreateFailure(message, serviceType);
                }

                // 3. 根据服务类型获取对应的子服务ID
                string? historyServiceId = GetHistoryServiceIdByType(historyWitsService, serviceType);
                if (string.IsNullOrEmpty(historyServiceId))
                {
                    var message = $"历史Wits服务中没有{serviceType}服务配置ID";
                    LogUtil.AddLog(message);
                    return HistoryServiceResult<T>.CreateFailure(message, serviceType);
                }

                // 4. 查询历史服务实体
                T? historyService = GetHistoryServiceEntity<T>(historyServiceId, serviceType);
                if (historyService != null)
                {
                    LogUtil.AddLog($"成功找到历史{serviceType}服务，ID: {historyServiceId}");
                    return HistoryServiceResult<T>.CreateSuccess(historyService, historyWitsServiceId, historyServiceId, serviceType);
                }
                else
                {
                    var message = $"未找到历史{serviceType}服务记录，ID: {historyServiceId}";
                    LogUtil.AddLog(message);
                    return HistoryServiceResult<T>.CreateFailure(message, serviceType);
                }
            }
            catch (Exception ex)
            {
                var message = $"查找历史{serviceType}服务异常: {ex.Message}";
                LogUtil.AddErrorLog(message);
                return HistoryServiceResult<T>.CreateFailure(message, serviceType);
            }
        }

        /// <summary>
        /// 根据服务类型从历史Wits服务中获取对应的子服务ID
        /// </summary>
        /// <param name="historyWitsService">历史Wits服务</param>
        /// <param name="serviceType">服务类型</param>
        /// <returns>子服务ID</returns>
        private static string? GetHistoryServiceIdByType(Db_crm_contract_serviceinfo_wits historyWitsService, HistoryServiceType serviceType)
        {
            return serviceType switch
            {
                HistoryServiceType.GTIS => historyWitsService.CurrentGtisServiceId,
                HistoryServiceType.GlobalSearch => historyWitsService.CurrentGlobalSearchServiceId,
                HistoryServiceType.SaleWits => historyWitsService.CurrentSalesWitsServiceId,
                HistoryServiceType.College => historyWitsService.CurrentCollegeServiceId,
                _ => null
            };
        }

        /// <summary>
        /// 根据服务类型和ID获取历史服务实体
        /// </summary>
        /// <typeparam name="T">服务实体类型</typeparam>
        /// <param name="serviceId">服务ID</param>
        /// <param name="serviceType">服务类型</param>
        /// <returns>服务实体</returns>
        private T? GetHistoryServiceEntity<T>(string serviceId, HistoryServiceType serviceType) where T : class
        {
            return serviceType switch
            {
                HistoryServiceType.GTIS => DbOpe_crm_contract_serviceinfo_gtis.Instance
                    .GetData(x => x.Id == serviceId && x.Deleted == false) as T,
                HistoryServiceType.GlobalSearch => DbOpe_crm_contract_serviceinfo_globalsearch.Instance
                    .GetData(x => x.Id == serviceId && x.Deleted == false) as T,
                HistoryServiceType.SaleWits => _dbOpe_crm_contract_serviceinfo_saleswits
                    .GetData(x => x.Id == serviceId && x.Deleted == false) as T,
                HistoryServiceType.College => DbOpe_crm_contract_serviceinfo_college.Instance
                    .GetData(x => x.Id == serviceId && x.Deleted == false) as T,
                _ => null
            };
        }

        /// <summary>
        /// 批量查找历史服务
        /// </summary>
        /// <param name="openingParams">服务开通参数</param>
        /// <returns>历史服务集合</returns>
        public HistoryServicesCollection FindAllHistoryServices(ServiceOpeningParams openingParams)
        {
            LogUtil.AddLog("开始批量查找所有历史服务");

            var result = new HistoryServicesCollection();

            try
            {
                // 获取历史Wits服务ID
                var historyWitsServiceId = GetHistoryWitsServiceId(openingParams);
                if (string.IsNullOrEmpty(historyWitsServiceId))
                {
                    LogUtil.AddLog("无法获取历史Wits服务ID，返回空的历史服务集合");
                    return result;
                }

                // 获取历史Wits服务
                var historyWitsService = DbOpe_crm_contract_serviceinfo_wits.Instance
                    .GetData(x => x.Id == historyWitsServiceId && x.Deleted == false);

                if (historyWitsService == null)
                {
                    LogUtil.AddLog($"未找到历史Wits服务记录，ID: {historyWitsServiceId}");
                    return result;
                }

                result.WitsService = historyWitsService;

                // 批量查找各个子服务
                if (!string.IsNullOrEmpty(historyWitsService.CurrentGtisServiceId))
                {
                    result.GtisService = DbOpe_crm_contract_serviceinfo_gtis.Instance
                        .GetData(x => x.Id == historyWitsService.CurrentGtisServiceId && x.Deleted == false);
                }

                if (!string.IsNullOrEmpty(historyWitsService.CurrentGlobalSearchServiceId))
                {
                    result.GlobalSearchService = DbOpe_crm_contract_serviceinfo_globalsearch.Instance
                        .GetData(x => x.Id == historyWitsService.CurrentGlobalSearchServiceId && x.Deleted == false);
                }

                if (!string.IsNullOrEmpty(historyWitsService.CurrentSalesWitsServiceId))
                {
                    result.SaleWitsService = _dbOpe_crm_contract_serviceinfo_saleswits
                        .GetData(x => x.Id == historyWitsService.CurrentSalesWitsServiceId && x.Deleted == false);
                }

                if (!string.IsNullOrEmpty(historyWitsService.CurrentCollegeServiceId))
                {
                    result.CollegeService = DbOpe_crm_contract_serviceinfo_college.Instance
                        .GetData(x => x.Id == historyWitsService.CurrentCollegeServiceId && x.Deleted == false);
                }

                LogUtil.AddLog($"批量查找历史服务完成，找到服务数量: GTIS={result.GtisService != null}, GlobalSearch={result.GlobalSearchService != null}, SaleWits={result.SaleWitsService != null}, College={result.CollegeService != null}");

            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"批量查找历史服务异常: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 查找历史GTIS服务
        /// </summary>
        /// <param name="openingParams">服务开通参数</param>
        /// <returns>历史GTIS服务查找结果</returns>
        public HistoryServiceResult<Db_crm_contract_serviceinfo_gtis> FindHistoryGtisService(ServiceOpeningParams openingParams)
        {
            return FindHistoryService<Db_crm_contract_serviceinfo_gtis>(openingParams, HistoryServiceType.GTIS);
        }

        /// <summary>
        /// 查找历史环球搜服务
        /// </summary>
        /// <param name="openingParams">服务开通参数</param>
        /// <returns>历史环球搜服务查找结果</returns>
        public HistoryServiceResult<Db_crm_contract_serviceinfo_globalsearch> FindHistoryGlobalSearchService(ServiceOpeningParams openingParams)
        {
            return FindHistoryService<Db_crm_contract_serviceinfo_globalsearch>(openingParams, HistoryServiceType.GlobalSearch);
        }

        /// <summary>
        /// 查找历史SaleWits服务
        /// </summary>
        /// <param name="openingParams">服务开通参数</param>
        /// <returns>历史SaleWits服务查找结果</returns>
        public HistoryServiceResult<Db_crm_contract_serviceinfo_saleswits> FindHistorySaleWitsService(ServiceOpeningParams openingParams)
        {
            return FindHistoryService<Db_crm_contract_serviceinfo_saleswits>(openingParams, HistoryServiceType.SaleWits);
        }

        /// <summary>
        /// 查找历史慧思学院服务
        /// </summary>
        /// <param name="openingParams">服务开通参数</param>
        /// <returns>历史慧思学院服务查找结果</returns>
        public HistoryServiceResult<Db_crm_contract_serviceinfo_college> FindHistoryCollegeService(ServiceOpeningParams openingParams)
        {
            return FindHistoryService<Db_crm_contract_serviceinfo_college>(openingParams, HistoryServiceType.College);
        }

        #endregion
    }
}