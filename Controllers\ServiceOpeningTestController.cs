using Microsoft.AspNetCore.Mvc;
using CRM2_API.BLL.ServiceOpening;
using CRM2_API.BLL.GtisOpe;
using CRM2_API.Model.BusinessModel.BM_GtisOpe;
using CRM2_API.Model.BLLModel.ServiceOpeningTest;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.Enum;
using CRM2_API.Common;
using CRM2_API.Common.JWT;
using CRM2_API.Common.Filter;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using CRM2_API.BLL.GtisOpe;
using CRM2_API.Controllers.Base;

namespace CRM2_API.Controllers
{
    /// <summary>
    /// 服务开通测试数据管理控制器
    /// </summary>
    [ApiController]
    [SkipRightCheck]
    [SkipIPCheck]
    [SkipRecordLog]
    [SkipAuthCheck]
    [SkipRS<PERSON>Key]
    public class ServiceOpeningTestController : MyControllerBase
    {
        private readonly BLL_ServiceOpening _serviceOpeningBLL;
        private readonly BLL_GtisOpe _gtisBLL;
        private const string TEST_USER_ID = "00000000-0000-0000-0000-000000000009"; // 测试用户ID

        public ServiceOpeningTestController()
        {
            _serviceOpeningBLL = new BLL_ServiceOpening();
            _gtisBLL = new BLL_GtisOpe();

            // 为测试环境设置模拟的TokenModel
            SetMockTokenModel();
        }

        /// <summary>
        /// 设置模拟的TokenModel用于测试
        /// </summary>
        private void SetMockTokenModel()
        {
            var mockTokenModel = new TokenModel
            {
                id = TEST_USER_ID,
                name = "测试用户",
                suppled = 1
            };
            TokenModel.SetTokenModel(mockTokenModel);
        }

        /// <summary>
        /// 创建测试数据
        /// </summary>
        /// <param name="testData">测试数据</param>
        /// <returns>创建结果</returns>
        [HttpPost]
        public async Task<CRM2_API.Model.BLLModel.ServiceOpeningTest.CreateTestDataResult> CreateTestData([FromBody] ServiceOpeningTestData testData)
        {
            try
            {
                // 确保TokenModel已设置
                SetMockTokenModel();

                LogUtil.AddLog("开始创建服务开通测试数据");

                var result = new CRM2_API.Model.BLLModel.ServiceOpeningTest.CreateTestDataResult
                {
                    Success = false,
                    Message = "",
                    DataIds = new CRM2_API.Model.BLLModel.ServiceOpeningTest.CreateTestDataIds
                    {
                        ContractId = "",
                        ServiceId = "",
                        UserIds = new List<string>()
                    }
                };

                // 1. 创建合同数据（如果需要）
                var contractId = await CreateTestContract(testData.Contract);

                // 1.5. 获取GTIS用户信息（用于判断历史服务状态）
                List<CRM2_API.Model.BusinessModel.BM_GtisOpe.BM_GtisOpeUserInfo> gtisUserInfoList = null;
                if (testData.MainService.ProcessingType == 2) // 服务变更时需要获取历史信息
                {
                    try
                    {
                        var gtisOpe = new BLL_GtisOpe();
                        gtisUserInfoList = await gtisOpe.GetUserInfo(testData.Contract.CustomerCode);
                        LogUtil.AddLog($"获取GTIS用户信息成功，数量: {gtisUserInfoList?.Count ?? 0}");
                    }
                    catch (Exception ex)
                    {
                        LogUtil.AddLog($"获取GTIS用户信息失败: {ex.Message}，将使用默认逻辑");
                    }
                }

                // 2. 创建各服务专门配置数据（先创建，获取配置ID）
                var witsApplId = testData.MainService.Id;
                var configIds = await CreateTestServiceConfigs(testData.Services, contractId, witsApplId, testData.MainService);

                // 3. 创建主服务数据（使用配置ID设置CurrentServiceId字段）
                var serviceId = await CreateTestMainService(testData, contractId, gtisUserInfoList, configIds);

                // 4. 创建用户数据
                var userIds = await CreateTestUsers(testData.Users, serviceId, testData.MainService);

                result.Success = true;
                result.Message = "测试数据创建成功";
                result.DataIds.ContractId = contractId;
                result.DataIds.ServiceId = serviceId;
                result.DataIds.UserIds = userIds;

                LogUtil.AddLog($"服务开通测试数据创建成功，服务ID: {serviceId}");
                return result;
            }
            catch (Exception ex)
            {
                LogUtil.AddLog($"创建服务开通测试数据异常: {ex.Message}", ex);
                return new CRM2_API.Model.BLLModel.ServiceOpeningTest.CreateTestDataResult
                {
                    Success = false,
                    Message = $"创建测试数据失败: {ex.Message}",
                    DataIds = new CRM2_API.Model.BLLModel.ServiceOpeningTest.CreateTestDataIds
                    {
                        ContractId = "",
                        ServiceId = "",
                        UserIds = new List<string>()
                    }
                };
            }
        }

        /// <summary>
        /// 获取客户的未开通SalesWits服务
        /// </summary>
        [HttpPost]
        public GetPendingServicesResult GetPendingServices([FromBody] GetPendingServicesRequest request)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(request.CustomerCode))
                {
                    return new GetPendingServicesResult
                    {
                        Success = false,
                        Message = "客户编码不能为空",
                        ServiceId = "",
                    ContractId = ""
                    };
                }

                // 查找该客户的未开通SalesWits服务ID
                var (serviceId, contractId) = GetCustomerPendingServiceWitsId(request.CustomerCode);

                if (string.IsNullOrEmpty(serviceId))
                {
                    return new GetPendingServicesResult
                    {
                        Success = false,
                        Message = "该客户没有找到未开通的SalesWits服务",
                        ServiceId = "",
                        ContractId = ""
                    };
                }

                return new GetPendingServicesResult
                {
                    Success = true,
                    Message = "找到未开通的SalesWits服务",
                    ServiceId = serviceId,
                    ContractId = contractId
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddLog($"获取未开通服务失败：{ex.Message}");
                return new GetPendingServicesResult
                {
                    Success = false,
                    Message = $"获取未开通服务失败：{ex.Message}",
                    ServiceId = "",
                    ContractId = ""
                };
            }
        }

        /// <summary>
        /// 获取客户的变更服务
        /// </summary>
        [HttpPost]
        public GetPendingServicesResult GetChangeServices([FromBody] GetPendingServicesRequest request)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(request.CustomerCode))
                {
                    return new GetPendingServicesResult
                    {
                        Success = false,
                        Message = "客户编码不能为空",
                        ServiceId = "",
                        ContractId = ""
                    };
                }

                // 查找该客户的变更服务ID
                var (serviceId, contractId) = GetCustomerPendingServiceWitsId(request.CustomerCode, true);

                if (string.IsNullOrEmpty(serviceId))
                {
                    return new GetPendingServicesResult
                    {
                        Success = false,
                        Message = "该客户没有找到变更服务",
                        ServiceId = "",
                        ContractId = ""
                    };
                }

                return new GetPendingServicesResult
                {
                    Success = true,
                    Message = "找到变更服务",
                    ServiceId = serviceId,
                    ContractId = contractId
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddLog($"获取变更服务失败：{ex.Message}");
                return new GetPendingServicesResult
                {
                    Success = false,
                    Message = $"获取变更服务失败：{ex.Message}",
                    ServiceId = "",
                    ContractId = ""
                };
            }
        }

        /// <summary>
        /// 执行服务开通测试
        /// </summary>
        /// <param name="request">开通请求</param>
        /// <returns>开通结果</returns>
        [HttpPost]
        public async Task<CRM2_API.Model.BLLModel.ServiceOpeningTest.ExecuteServiceOpeningResult> ExecuteServiceOpening([FromBody] ServiceOpeningTestRequest request)
        {
            try
            {
                // 确保TokenModel已设置
                SetMockTokenModel();

                LogUtil.AddLog($"开始执行服务开通测试，服务ID: {request.ServiceId}");

                // 调用真实的服务开通接口，启用测试模式
                var result = await _serviceOpeningBLL.ExecuteServiceOpening(request.ServiceId, true);

                // 如果服务开通成功，执行后处理操作
                if (result.Success)
                {
                    try
                    {
                        // 1. 处理环球搜码写回
                        await ProcessGlobalSearchCodeWriteBack(request.ServiceId, result);

                        // 2. 更新所有关联服务表的处理状态
                        UpdateAllServiceTablesStatus(request.ServiceId);

                        LogUtil.AddLog($"服务开通后处理完成: ServiceId={request.ServiceId}");
                    }
                    catch (Exception updateEx)
                    {
                        LogUtil.AddLog($"服务开通后处理失败: {updateEx.Message}");
                        // 不影响主流程，只记录日志
                    }
                }

                return new CRM2_API.Model.BLLModel.ServiceOpeningTest.ExecuteServiceOpeningResult
                {
                    Success = result.Success,
                    Message = result.Message,
                    Details = new CRM2_API.Model.BLLModel.ServiceOpeningTest.ServiceOpeningDetails
                    {
                        ServiceId = request.ServiceId,
                        ProcessedServices = new List<string>
                        {
                            result.GtisResult?.ToString() ?? "GTIS",
                            result.GlobalSearchResult?.ToString() ?? "GlobalSearch",
                            result.SaleWitsResult?.ToString() ?? "SaleWits",
                            result.CollegeResult?.ToString() ?? "College"
                        }
                    }
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddLog($"执行服务开通测试异常: {ex.Message}", ex);
                return new CRM2_API.Model.BLLModel.ServiceOpeningTest.ExecuteServiceOpeningResult
                {
                    Success = false,
                    Message = $"服务开通执行失败: {ex.Message}",
                    Details = new CRM2_API.Model.BLLModel.ServiceOpeningTest.ServiceOpeningDetails
                    {
                        ServiceId = request.ServiceId,
                        ProcessedServices = new List<string>()
                    }
                };
            }
        }

        /// <summary>
        /// 获取客户编码对应的账户信息
        /// </summary>
        /// <param name="request">客户编码请求</param>
        /// <returns>账户信息</returns>
        [HttpPost]
        public async Task<CRM2_API.Model.BLLModel.ServiceOpeningTest.GetCustomerInfoResult> GetCustomerInfo([FromBody] GetCustomerInfoRequest request)
        {
            try
            {
                // 确保TokenModel已设置
                SetMockTokenModel();

                LogUtil.AddLog($"开始获取客户编码对应的账户信息: {request.CustomerCode}");

                if (string.IsNullOrEmpty(request.CustomerCode))
                {
                    return new GetCustomerInfoResult
                    {
                        Success = false,
                        Message = "客户编码不能为空",
                        Data = new CustomerInfoData
                        {
                            CustomerCode = request.CustomerCode ?? "",
                            Exists = false,
                            AccountInfo = null
                        }
                    };
                }

                // 调用GTIS接口获取账户信息
                var userInfoList = await _gtisBLL.GetUserInfo(request.CustomerCode);

                if (userInfoList == null || userInfoList.Count == 0)
                {
                    return new CRM2_API.Model.BLLModel.ServiceOpeningTest.GetCustomerInfoResult
                    {
                        Success = true,
                        Message = "未找到对应的账户信息",
                        Data = new CRM2_API.Model.BLLModel.ServiceOpeningTest.CustomerInfoData
                        {
                            CustomerCode = request.CustomerCode,
                            Exists = false,
                            GtisUserInfoList = null
                        }
                    };
                }

                LogUtil.AddLog($"成功获取客户编码 {request.CustomerCode} 的账户信息，共 {userInfoList.Count} 个账户");

                // 直接返回GTIS接口的完整原始数据
                return new CRM2_API.Model.BLLModel.ServiceOpeningTest.GetCustomerInfoResult
                {
                    Success = true,
                    Message = "获取账户信息成功",
                    Data = new CRM2_API.Model.BLLModel.ServiceOpeningTest.CustomerInfoData
                    {
                        CustomerCode = request.CustomerCode,
                        Exists = true,
                        GtisUserInfoList = userInfoList
                    }
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取客户编码 {request.CustomerCode} 的账户信息失败: {ex.Message}");
                return new CRM2_API.Model.BLLModel.ServiceOpeningTest.GetCustomerInfoResult
                {
                    Success = false,
                    Message = $"获取账户信息失败: {ex.Message}",
                    Data = new CRM2_API.Model.BLLModel.ServiceOpeningTest.CustomerInfoData
                    {
                        CustomerCode = request.CustomerCode,
                        Exists = false,
                        AccountInfo = null
                    }
                };
            }
        }

        /// <summary>
        /// 清理测试数据
        /// </summary>
        /// <param name="request">清理请求</param>
        /// <returns>清理结果</returns>
        [HttpPost]
        public CRM2_API.Model.BLLModel.ServiceOpeningTest.ClearTestDataResult ClearTestData([FromBody] ClearTestDataRequest request)
        {
            try
            {
                // 确保TokenModel已设置
                SetMockTokenModel();

                LogUtil.AddLog("开始清理服务开通测试数据");

                var deletedCount = 0;

                // 清理主服务表测试数据
                var witsServices = DbOpe_crm_contract_serviceinfo_wits.Instance
                    .GetDataList(x => x.CreateUser != null && x.CreateUser == TEST_USER_ID);
                foreach (var service in witsServices)
                {
                    // 清理用户数据
                    var users = DbOpe_crm_contract_serviceinfo_wits_user.Instance
                        .GetDataList(x => x.WitsServeId == service.Id);
                    foreach (var user in users)
                    {
                        DbOpe_crm_contract_serviceinfo_wits_user.Instance.Delete(user);
                        deletedCount++;
                    }

                    // 清理各服务配置数据
                    ClearServiceConfigs(service.Id, service.ContractId);

                    // 删除主服务数据
                    DbOpe_crm_contract_serviceinfo_wits.Instance.Delete(service);
                    deletedCount++;
                }

                // 清理合同测试数据
                var contracts = DbOpe_crm_contract.Instance
                    .GetDataList(x => x.CreateUser != null && x.CreateUser == TEST_USER_ID);
                foreach (var contract in contracts)
                {
                    DbOpe_crm_contract.Instance.Delete(contract);
                    deletedCount++;
                }

                LogUtil.AddLog($"服务开通测试数据清理完成，共删除 {deletedCount} 条记录");

                return new CRM2_API.Model.BLLModel.ServiceOpeningTest.ClearTestDataResult
                {
                    Success = true,
                    Message = $"测试数据清理成功，共删除 {deletedCount} 条记录",
                    Details = new CRM2_API.Model.BLLModel.ServiceOpeningTest.ClearTestDataDetails
                    {
                        ClearedCount = deletedCount,
                        ClearedTables = new List<string>
                        {
                            "crm_contract_serviceinfo_wits",
                            "crm_contract_serviceinfo_wits_user",
                            "crm_contract_serviceinfo_gtis",
                            "crm_contract_serviceinfo_globalsearch",
                            "crm_contract_serviceinfo_college",
                            "crm_contract"
                        }
                    }
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddLog($"清理服务开通测试数据异常: {ex.Message}", ex);
                return new CRM2_API.Model.BLLModel.ServiceOpeningTest.ClearTestDataResult
                {
                    Success = false,
                    Message = $"清理测试数据失败: {ex.Message}",
                    Details = new CRM2_API.Model.BLLModel.ServiceOpeningTest.ClearTestDataDetails
                    {
                        ClearedCount = 0,
                        ClearedTables = new List<string>()
                    }
                };
            }
        }

        /// <summary>
        /// 查询GTIS客户信息（用于服务变更）
        /// </summary>
        [HttpPost]
        public async Task<GetGtisCustomerInfoResult> GetGtisCustomerInfo([FromBody] GetGtisCustomerInfoRequest request)
        {
            try
            {
                LogUtil.AddLog($"开始查询GTIS客户信息，客户编码: {request.CustomerCode}");

                var gtisOpe = new BLL_GtisOpe();
                var gtisUserInfoList = await gtisOpe.GetUserInfo(request.CustomerCode);

                LogUtil.AddLog($"查询到GTIS用户信息 {gtisUserInfoList?.Count ?? 0} 条");

                return new GetGtisCustomerInfoResult
                {
                    Success = true,
                    Message = "查询成功",
                    Data = gtisUserInfoList ?? new List<CRM2_API.Model.BusinessModel.BM_GtisOpe.BM_GtisOpeUserInfo>()
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddLog($"查询GTIS客户信息异常: {ex.Message}", ex);
                return new GetGtisCustomerInfoResult
                {
                    Success = false,
                    Message = $"查询失败: {ex.Message}",
                    Data = new List<CRM2_API.Model.BusinessModel.BM_GtisOpe.BM_GtisOpeUserInfo>()
                };
            }
        }

        /// <summary>
        /// 独立SalesWits资源下发测试
        /// </summary>
        /// <param name="request">资源下发请求</param>
        /// <returns>下发结果</returns>
        [HttpPost]
        public async Task<SalesWitsResourceDistributionResult> TestSalesWitsResourceDistribution([FromBody] SalesWitsResourceDistributionRequest request)
        {
            try
            {
                // 确保TokenModel已设置
                SetMockTokenModel();

                LogUtil.AddLog($"开始独立SalesWits资源下发测试，合同ID: {request.ContractId}");

                // 1. 验证输入参数
                if (string.IsNullOrEmpty(request.ContractId))
                {
                    return new SalesWitsResourceDistributionResult
                    {
                        Success = false,
                        Message = "合同ID不能为空"
                    };
                }

                if (request.EmailCount <= 0 && request.TokenCount <= 0 && request.RechargeAmount <= 0)
                {
                    return new SalesWitsResourceDistributionResult
                    {
                        Success = false,
                        Message = "邮件数量、Token数量和充值金额不能全部为0"
                    };
                }

                // 2. 获取合同信息
                var contract = DbOpe_crm_contract.Instance.GetData(x => x.Id == request.ContractId);
                if (contract == null)
                {
                    return new SalesWitsResourceDistributionResult
                    {
                        Success = false,
                        Message = $"未找到合同信息，合同ID: {request.ContractId}"
                    };
                }

                // 3. 获取SalesWits服务配置
                var salesWitsService = DbOpe_crm_contract_serviceinfo_saleswits.Instance
                    .GetDataList(x => x.ContractId == request.ContractId && x.Deleted == false)
                    .OrderByDescending(x => x.CreateDate)
                    .FirstOrDefault();

                if (salesWitsService == null)
                {
                    return new SalesWitsResourceDistributionResult
                    {
                        Success = false,
                        Message = $"未找到SalesWits服务配置，合同ID: {request.ContractId}"
                    };
                }

                // 4. 执行资源下发
                var distributionResult = await ExecuteSalesWitsResourceDistribution(
                    contract,
                    salesWitsService,
                    request.EmailCount,
                    request.TokenCount,
                    request.RechargeAmount,
                    request.DistributionType);

                LogUtil.AddLog($"独立SalesWits资源下发测试完成: {distributionResult.Message}");
                return distributionResult;
            }
            catch (Exception ex)
            {
                LogUtil.AddLog($"独立SalesWits资源下发测试异常: {ex.Message}", ex);
                return new SalesWitsResourceDistributionResult
                {
                    Success = false,
                    Message = $"资源下发测试失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 获取有SalesWits服务的合同列表
        /// </summary>
        /// <returns>合同列表</returns>
        [HttpGet]
        public SalesWitsContractsResult GetSalesWitsContracts()
        {
            try
            {
                LogUtil.AddLog("开始获取有SalesWits服务的合同列表");

                // 查询有SalesWits服务配置的合同
                var salesWitsServices = DbOpe_crm_contract_serviceinfo_saleswits.Instance
                    .GetDataList(x => x.Deleted == false)
                    .GroupBy(x => x.ContractId)
                    .Select(g => g.OrderByDescending(x => x.CreateDate).First())
                    .ToList();

                var contractList = new List<SalesWitsContractInfo>();

                foreach (var salesWitsService in salesWitsServices)
                {
                    // 获取合同信息
                    var contract = DbOpe_crm_contract.Instance.GetData(x => x.Id == salesWitsService.ContractId);
                    if (contract != null)
                    {
                        contractList.Add(new SalesWitsContractInfo
                        {
                            ContractId = contract.Id,
                            ContractNum = contract.ContractNum,
                            CustomerName = contract.FirstParty,
                            AccountsNum = salesWitsService.AccountsNum ?? 0,
                            ServiceCycle = $"{salesWitsService.ServiceCycleStart:yyyy-MM-dd} 至 {salesWitsService.ServiceCycleEnd:yyyy-MM-dd}"
                        });
                    }
                }

                LogUtil.AddLog($"获取SalesWits合同列表成功，共{contractList.Count}个合同");

                return new SalesWitsContractsResult
                {
                    Success = true,
                    Message = $"获取成功，共{contractList.Count}个合同",
                    Data = contractList
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取SalesWits合同列表异常: {ex.Message}");
                return new SalesWitsContractsResult
                {
                    Success = false,
                    Message = $"获取合同列表失败: {ex.Message}",
                    Data = new List<SalesWitsContractInfo>()
                };
            }
        }

        /// <summary>
        /// 查询客户现有合同列表（用于服务变更）
        /// </summary>
        [HttpPost]
        public GetCustomerContractsResult GetCustomerContract([FromBody] GetCustomerContractsRequest request)
        {
            try
            {
                LogUtil.AddLog($"开始查询客户现有合同，客户编码: {request.CustomerCode}");

                // 查询该客户编码下的所有合同
                var contract = DbOpe_crm_contract.Instance
                    .GetData(x => x.ContractNum == request.CustomerCode && x.Deleted == false
                    && x.ContractType != (int)EnumContractType.AddItem);


                return new GetCustomerContractsResult
                {
                    Success = true,
                    Message = "查询成功",
                    Data = contract
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddLog($"查询客户合同异常: {ex.Message}", ex);
                return new GetCustomerContractsResult
                {
                    Success = false,
                    Message = $"查询失败: {ex.Message}",
                    Data = null
                };
            }
        }




        #region 私有方法

        /// <summary>
        /// 创建测试合同
        /// </summary>
        private async Task<string> CreateTestContract(TestContractData contractData)
        {
            // 检查是否已存在
            var existingContract = DbOpe_crm_contract.Instance
                .GetData(x => x.Id == contractData.Id);
            
            if (existingContract != null)
            {
                LogUtil.AddLog($"使用现有合同: {contractData.Id}");
                return contractData.Id;
            }

            var contract = new Db_crm_contract
            {
                Id = contractData.Id,
                CustomerId = Guid.NewGuid().ToString(), // 测试客户ID
                ContractNum = contractData.CustomerCode,
                ContractName = contractData.ContractName,
                ContractType = contractData.ContractType,
                SigningDate = DateTime.Parse(contractData.SigningDate),
                CreateUser = TEST_USER_ID,
                CreateDate = DateTime.Now,
                ContractStatus = (int)EnumContractStatus.Pass,
                Deleted = false,
 
                    ContactInformation = 2,
                    ContractMethod = 2,
                    ContractPaperEntityId = null,
                    FirstParty = Guid.NewGuid().ToString(), // 测试客户ID,
                    Contacts = "张测试",
                    Job = "经理",
                    ContactWay = "18900000000",
                    Email = "<EMAIL>",
                    Telephone = "010-12345678",
                    Fax = "010-12345678",
                    Country = 337,
                    Province = 2,
                    City = 2,
                    Address = "测试地址",
                    PostalCode = "100000",
                    IsOverseasCustomer = true,
                    SecondParty = "f27399d1-ceac-11ed-bc7b-30d042e24322",
                    RenewalContractNum = "",
                    SalesCountry = 255,
                    IsMerged = false,
                    SigningAgentName = "测试代理人",
                    SigningAgentPhone = "***********",
                    SigningAgentEmail = "<EMAIL>",
                    SigningAgentFax = "010-********",
                    CompanyAddressId = null,
                    
                    IsSpecial = 0,
                    SpecialDescription = "",
                    
                    Remark = "",
                    
                    Currency = 1,
                    ContractAmount = 48800,
                    FCContractAmount = null,
                    IsApplySuperSubAccount = false,
                    IsCustomContractDescription = 2

            };

            DbOpe_crm_contract.Instance.Insert(contract);
            LogUtil.AddLog($"创建测试合同成功: {contract.Id}");
            
            return contract.Id;
        }

        /// <summary>
        /// 创建测试主服务数据
        /// </summary>
        private async Task<string> CreateTestMainService(ServiceOpeningTestData testData, string contractId, List<CRM2_API.Model.BusinessModel.BM_GtisOpe.BM_GtisOpeUserInfo> gtisUserInfoList = null, ServiceConfigIds configIds = null)
        {
            var serviceData = testData.MainService;
            // 根据服务类型决定如何设置HasXxxApp字段
            bool hasGlobalSearchApp, hasSalesWitsApp, hasCollegeApp, hasGtisApp;

            if (serviceData.ProcessingType == 2) // 服务变更
            {
                // 使用GTIS用户信息中的apps字段来判断历史服务
                if (gtisUserInfoList != null && gtisUserInfoList.Count > 0)
                {
                    var firstUser = gtisUserInfoList.First();
                    var apps = firstUser.apps ?? new string[0];

                    // 根据apps数组判断历史是否开通过各项服务，同时考虑本次申请
                    hasGlobalSearchApp = apps.Contains("HQS") || serviceData.IsGlobalSearchAudit;
                    hasSalesWitsApp = apps.Contains("CRM") || serviceData.IsSalesWitsAudit;
                    hasCollegeApp = apps.Contains("College") || serviceData.IsCollegeAudit;
                    hasGtisApp = apps.Contains("Gtis6") || serviceData.IsGtisAudit;

                    LogUtil.AddLog($"服务变更 - 从GTIS获取历史服务状态: apps=[{string.Join(",", apps)}], 最终状态: GlobalSearch={hasGlobalSearchApp}, SalesWits={hasSalesWitsApp}, College={hasCollegeApp}, Gtis={hasGtisApp}");
                }
                else
                {
                    // 如果没有GTIS信息，回退到本次申请状态
                    hasGlobalSearchApp = serviceData.IsGlobalSearchAudit;
                    hasSalesWitsApp = serviceData.IsSalesWitsAudit;
                    hasCollegeApp = serviceData.IsCollegeAudit;
                    hasGtisApp = serviceData.IsGtisAudit;

                    LogUtil.AddLog("服务变更 - 未获取到GTIS历史信息，使用本次申请状态");
                }
            }
            else if (serviceData.ProcessingType == 1 && testData.Contract.ContractType == 2) // 服务申请 + 合同续约 = 续约场景
            {
                // 续约时，从被续约的旧数据中获取历史服务状态
                if (gtisUserInfoList != null && gtisUserInfoList.Count > 0)
                {
                    var firstUser = gtisUserInfoList.First();
                    var apps = firstUser.apps ?? new string[0];

                    // 续约时只取历史服务状态，不考虑本次申请
                    hasGlobalSearchApp = apps.Contains("HQS");
                    hasSalesWitsApp = apps.Contains("CRM");
                    hasCollegeApp = apps.Contains("College");
                    hasGtisApp = apps.Contains("Gtis6");

                    LogUtil.AddLog($"服务续约 - 从被续约客户获取历史服务状态: apps=[{string.Join(",", apps)}], 最终状态: GlobalSearch={hasGlobalSearchApp}, SalesWits={hasSalesWitsApp}, College={hasCollegeApp}, Gtis={hasGtisApp}");
                }
                else
                {
                    // 如果没有GTIS信息，回退到本次申请状态
                    hasGlobalSearchApp = serviceData.IsGlobalSearchAudit;
                    hasSalesWitsApp = serviceData.IsSalesWitsAudit;
                    hasCollegeApp = serviceData.IsCollegeAudit;
                    hasGtisApp = serviceData.IsGtisAudit;

                    LogUtil.AddLog("服务续约 - 未获取到GTIS历史信息，使用本次申请状态");
                }
            }
            else // 新增服务（服务申请 + 新增合同/新增项目）
            {
                // 新增服务时，直接根据本次申请情况设置
                hasGlobalSearchApp = serviceData.IsGlobalSearchAudit;
                hasSalesWitsApp = serviceData.IsSalesWitsAudit;
                hasCollegeApp = serviceData.IsCollegeAudit;
                hasGtisApp = serviceData.IsGtisAudit;
            }

            var service = new Db_crm_contract_serviceinfo_wits
            {
                Id = Guid.NewGuid().ToString(),
                WitsApplId = serviceData.Id,
                ContractId = contractId,
                ProcessingType = serviceData.ProcessingType,
                State = (EnumContractServiceState)serviceData.State,
                IsGtisAudit = serviceData.IsGtisAudit,
                IsGlobalSearchAudit = serviceData.IsGlobalSearchAudit,
                IsSalesWitsAudit = serviceData.IsSalesWitsAudit,
                IsCollegeAudit = serviceData.IsCollegeAudit,
                HasGlobalSearchApp = hasGlobalSearchApp,
                HasSalesWitsApp = hasSalesWitsApp,
                HasCollegeApp = hasCollegeApp,
                HasGtisApp = hasGtisApp,
                ShareUsageNum = testData.Users.Count * 10,
                // 设置原合同编码（服务变更/续约时使用）
                OldContractNum = serviceData.OldContractNum,
                CreateUser = TEST_USER_ID,
                CreateDate = DateTime.Now,
                Deleted = false
            };

            // 设置各服务的详细配置字段
            if (serviceData.ProcessingType == 2) // 服务变更
            {
                // 查询历史服务记录，获取历史配置
                var historicalServices = DbOpe_crm_contract_serviceinfo_wits.Instance
                    .GetDataList(x => x.ContractId == contractId && x.Deleted == false && x.IsProcessed == 1 )
                    .OrderByDescending(x => x.ProcessedTime)
                    .FirstOrDefault();

                // 🆕 先继承历史的CurrentServiceId字段
                if (historicalServices != null)
                {
                    service.CurrentGtisServiceId = historicalServices.CurrentGtisServiceId;
                    service.CurrentGlobalSearchServiceId = historicalServices.CurrentGlobalSearchServiceId;
                    service.CurrentSalesWitsServiceId = historicalServices.CurrentSalesWitsServiceId;
                    service.CurrentCollegeServiceId = historicalServices.CurrentCollegeServiceId;

                    LogUtil.AddLog($"服务变更 - 已继承CurrentServiceId字段，保持现有配置引用");
                }

                // 🆕 如果本次变更了某些服务，创建新配置并更新对应的CurrentServiceId
                if (configIds != null)
                {
                    if (!string.IsNullOrEmpty(configIds.GtisServiceId))
                    {
                        service.CurrentGtisServiceId = configIds.GtisServiceId;
                        LogUtil.AddLog($"服务变更 - 更新CurrentGtisServiceId: {configIds.GtisServiceId}");
                    }
                    if (!string.IsNullOrEmpty(configIds.GlobalSearchServiceId))
                    {
                        service.CurrentGlobalSearchServiceId = configIds.GlobalSearchServiceId;
                        LogUtil.AddLog($"服务变更 - 更新CurrentGlobalSearchServiceId: {configIds.GlobalSearchServiceId}");
                    }
                    if (!string.IsNullOrEmpty(configIds.SalesWitsServiceId))
                    {
                        service.CurrentSalesWitsServiceId = configIds.SalesWitsServiceId;
                        LogUtil.AddLog($"服务变更 - 更新CurrentSalesWitsServiceId: {configIds.SalesWitsServiceId}");
                    }
                    if (!string.IsNullOrEmpty(configIds.CollegeServiceId))
                    {
                        service.CurrentCollegeServiceId = configIds.CollegeServiceId;
                        LogUtil.AddLog($"服务变更 - 更新CurrentCollegeServiceId: {configIds.CollegeServiceId}");
                    }
                }

                // GTIS服务字段设置
                if (hasGtisApp) // 如果拥有GTIS服务
                {
                    if (serviceData.IsGtisAudit) // 本次申请了，使用新配置
                    {
                        service.GtisPrimaryAccountsNum = testData.Services.Gtis.PrimaryAccountsNum;
                        service.GtisSubAccountsNum = testData.Services.Gtis.SubAccountsNum;
                        service.GtisServiceCycleStart = DateTime.Parse(testData.Services.Gtis.ServiceCycleStart);
                        service.GtisServiceCycleEnd = DateTime.Parse(testData.Services.Gtis.ServiceCycleEnd);
                    }
                    else // 本次未申请，使用历史配置
                    {
                        service.GtisPrimaryAccountsNum = historicalServices?.GtisPrimaryAccountsNum;
                        service.GtisSubAccountsNum = historicalServices?.GtisSubAccountsNum;
                        service.GtisServiceCycleStart = historicalServices?.GtisServiceCycleStart;
                        service.GtisServiceCycleEnd = historicalServices?.GtisServiceCycleEnd;
                    }
                }

                // 环球搜服务字段设置
                if (hasGlobalSearchApp) // 如果拥有环球搜服务
                {
                    if (serviceData.IsGlobalSearchAudit) // 本次申请了，使用新配置
                    {
                        service.GlobalSearchAccountsNum = testData.Services.GlobalSearch.GetTotalAccountsNum();
                        service.GlobalSearchServiceCycleStart = DateTime.Parse(testData.Services.GlobalSearch.ServiceCycleStart);
                        service.GlobalSearchServiceCycleEnd = DateTime.Parse(testData.Services.GlobalSearch.ServiceCycleEnd);
                    }
                    else // 本次未申请，使用历史配置
                    {
                        service.GlobalSearchAccountsNum = historicalServices?.GlobalSearchAccountsNum;
                        service.GlobalSearchServiceCycleStart = historicalServices?.GlobalSearchServiceCycleStart;
                        service.GlobalSearchServiceCycleEnd = historicalServices?.GlobalSearchServiceCycleEnd;
                    }
                }

                // SalesWits服务字段设置
                if (hasSalesWitsApp) // 如果拥有SalesWits服务
                {
                    if (serviceData.IsSalesWitsAudit) // 本次申请了，使用新配置
                    {
                        service.SalesWitsAccountsNum = testData.Services.SalesWits.AccountsNum;
                        service.SalesWitsServiceCycleStart = DateTime.Parse(testData.Services.SalesWits.ServiceCycleStart);
                        service.SalesWitsServiceCycleEnd = DateTime.Parse(testData.Services.SalesWits.ServiceCycleEnd);
                    }
                    else // 本次未申请，使用历史配置
                    {
                        service.SalesWitsAccountsNum = historicalServices?.SalesWitsAccountsNum;
                        service.SalesWitsServiceCycleStart = historicalServices?.SalesWitsServiceCycleStart;
                        service.SalesWitsServiceCycleEnd = historicalServices?.SalesWitsServiceCycleEnd;
                    }
                }

                // 慧思学院服务字段设置
                if (hasCollegeApp) // 如果拥有慧思学院服务
                {
                    if (serviceData.IsCollegeAudit) // 本次申请了，使用新配置
                    {
                        service.CollegeAccountsNum = testData.Services.College.PrimaryAccountsNum;
                        service.CollegeServiceCycleStart = DateTime.Parse(testData.Services.College.ServiceCycleStart);
                        service.CollegeServiceCycleEnd = DateTime.Parse(testData.Services.College.ServiceCycleEnd);
                    }
                    else // 本次未申请，使用历史配置
                    {
                        service.CollegeAccountsNum = historicalServices?.CollegeAccountsNum;
                        service.CollegeServiceCycleStart = historicalServices?.CollegeServiceCycleStart;
                        service.CollegeServiceCycleEnd = historicalServices?.CollegeServiceCycleEnd;
                    }
                }
            }
            else if (serviceData.ProcessingType == 1 && testData.Contract.ContractType == 2) // 服务续约
            {
                // 续约时，需要从被续约的客户编码对应的历史服务中获取配置
                // 这里应该查询被续约客户的历史服务配置，而不是当前合同的历史配置
                Db_crm_contract_serviceinfo_wits historicalRenewalServices = null;

                if (!string.IsNullOrEmpty(serviceData.OldContractNum))
                {
                    // 通过原合同编码查询被续约客户的历史服务配置
                    var oldContract = DbOpe_crm_contract.Instance
                        .GetData(x => x.ContractNum == serviceData.OldContractNum && x.Deleted == false);

                    if (oldContract != null)
                    {
                        historicalRenewalServices = DbOpe_crm_contract_serviceinfo_wits.Instance
                            .GetDataList(x => x.ContractId == oldContract.Id && x.Deleted == false && x.IsProcessed == 1)
                            .OrderByDescending(x => x.ProcessedTime)
                            .FirstOrDefault();
                    }
                }

                // 🆕 先设置当前生效的服务配置ID（指向原合同的配置）
                if (historicalRenewalServices != null)
                {
                    service.CurrentGtisServiceId = historicalRenewalServices.CurrentGtisServiceId;
                    service.CurrentGlobalSearchServiceId = historicalRenewalServices.CurrentGlobalSearchServiceId;
                    service.CurrentSalesWitsServiceId = historicalRenewalServices.CurrentSalesWitsServiceId;
                    service.CurrentCollegeServiceId = historicalRenewalServices.CurrentCollegeServiceId;

                    LogUtil.AddLog($"服务续约 - 已设置CurrentServiceId字段，指向原合同配置");
                }

                // 🆕 如果续约时同时变更了某些服务，更新对应的CurrentServiceId
                if (configIds != null)
                {
                    if (!string.IsNullOrEmpty(configIds.GtisServiceId))
                    {
                        service.CurrentGtisServiceId = configIds.GtisServiceId;
                        LogUtil.AddLog($"服务续约 - 更新CurrentGtisServiceId: {configIds.GtisServiceId}");
                    }
                    if (!string.IsNullOrEmpty(configIds.GlobalSearchServiceId))
                    {
                        service.CurrentGlobalSearchServiceId = configIds.GlobalSearchServiceId;
                        LogUtil.AddLog($"服务续约 - 更新CurrentGlobalSearchServiceId: {configIds.GlobalSearchServiceId}");
                    }
                    if (!string.IsNullOrEmpty(configIds.SalesWitsServiceId))
                    {
                        service.CurrentSalesWitsServiceId = configIds.SalesWitsServiceId;
                        LogUtil.AddLog($"服务续约 - 更新CurrentSalesWitsServiceId: {configIds.SalesWitsServiceId}");
                    }
                    if (!string.IsNullOrEmpty(configIds.CollegeServiceId))
                    {
                        service.CurrentCollegeServiceId = configIds.CollegeServiceId;
                        LogUtil.AddLog($"服务续约 - 更新CurrentCollegeServiceId: {configIds.CollegeServiceId}");
                    }
                }

                // GTIS服务字段设置 - 续约时从历史服务获取配置
                if (hasGtisApp && historicalRenewalServices != null)
                {
                    service.GtisPrimaryAccountsNum = historicalRenewalServices.GtisPrimaryAccountsNum;
                    service.GtisSubAccountsNum = historicalRenewalServices.GtisSubAccountsNum;
                    service.GtisServiceCycleStart = historicalRenewalServices.GtisServiceCycleStart;
                    service.GtisServiceCycleEnd = historicalRenewalServices.GtisServiceCycleEnd;
                }

                // 环球搜服务字段设置 - 续约时从历史服务获取配置
                if (hasGlobalSearchApp && historicalRenewalServices != null)
                {
                    service.GlobalSearchAccountsNum = historicalRenewalServices.GlobalSearchAccountsNum;
                    service.GlobalSearchServiceCycleStart = historicalRenewalServices.GlobalSearchServiceCycleStart;
                    service.GlobalSearchServiceCycleEnd = historicalRenewalServices.GlobalSearchServiceCycleEnd;
                }

                // SalesWits服务字段设置 - 续约时从历史服务获取配置
                if (hasSalesWitsApp && historicalRenewalServices != null)
                {
                    service.SalesWitsAccountsNum = historicalRenewalServices.SalesWitsAccountsNum;
                    service.SalesWitsServiceCycleStart = historicalRenewalServices.SalesWitsServiceCycleStart;
                    service.SalesWitsServiceCycleEnd = historicalRenewalServices.SalesWitsServiceCycleEnd;
                }

                // 慧思学院服务字段设置 - 续约时从历史服务获取配置
                if (hasCollegeApp && historicalRenewalServices != null)
                {
                    service.CollegeAccountsNum = historicalRenewalServices.CollegeAccountsNum;
                    service.CollegeServiceCycleStart = historicalRenewalServices.CollegeServiceCycleStart;
                    service.CollegeServiceCycleEnd = historicalRenewalServices.CollegeServiceCycleEnd;
                }

                LogUtil.AddLog($"服务续约 - 从被续约客户获取服务配置: OldContractNum={serviceData.OldContractNum}, 历史服务配置={historicalRenewalServices != null}");
            }
            else // 新增服务（服务申请 + 新增合同/新增项目）
            {
                // 🆕 新增服务时，设置CurrentServiceId字段为刚创建的配置ID
                if (configIds != null)
                {
                    service.CurrentGtisServiceId = configIds.GtisServiceId;
                    service.CurrentGlobalSearchServiceId = configIds.GlobalSearchServiceId;
                    service.CurrentSalesWitsServiceId = configIds.SalesWitsServiceId;
                    service.CurrentCollegeServiceId = configIds.CollegeServiceId;

                    LogUtil.AddLog($"新增服务 - CurrentServiceId字段设置完成，GTIS: {configIds.GtisServiceId}, 环球搜: {configIds.GlobalSearchServiceId}, SalesWits: {configIds.SalesWitsServiceId}, 慧思学院: {configIds.CollegeServiceId}");
                }
                else
                {
                    // 如果没有传入配置ID，设置为空
                    service.CurrentGtisServiceId = string.Empty;
                    service.CurrentGlobalSearchServiceId = string.Empty;
                    service.CurrentSalesWitsServiceId = string.Empty;
                    service.CurrentCollegeServiceId = string.Empty;

                    LogUtil.AddLog($"新增服务 - 未传入配置ID，CurrentServiceId字段设置为空");
                }

                // 新增服务时，只有当本次申请了才设置相关字段
                if (serviceData.IsGtisAudit)
                {
                    service.GtisPrimaryAccountsNum = testData.Services.Gtis.PrimaryAccountsNum;
                    service.GtisSubAccountsNum = testData.Services.Gtis.SubAccountsNum;
                    service.GtisServiceCycleStart = DateTime.Parse(testData.Services.Gtis.ServiceCycleStart);
                    service.GtisServiceCycleEnd = DateTime.Parse(testData.Services.Gtis.ServiceCycleEnd);
                }

                if (serviceData.IsGlobalSearchAudit)
                {
                    service.GlobalSearchAccountsNum = testData.Services.GlobalSearch.GetTotalAccountsNum();
                    service.GlobalSearchServiceCycleStart = DateTime.Parse(testData.Services.GlobalSearch.ServiceCycleStart);
                    service.GlobalSearchServiceCycleEnd = DateTime.Parse(testData.Services.GlobalSearch.ServiceCycleEnd);
                }

                if (serviceData.IsSalesWitsAudit)
                {
                    service.SalesWitsAccountsNum = testData.Services.SalesWits.AccountsNum;
                    service.SalesWitsServiceCycleStart = DateTime.Parse(testData.Services.SalesWits.ServiceCycleStart);
                    service.SalesWitsServiceCycleEnd = DateTime.Parse(testData.Services.SalesWits.ServiceCycleEnd);
                }

                if (serviceData.IsCollegeAudit)
                {
                    service.CollegeAccountsNum = testData.Services.College.PrimaryAccountsNum;
                    service.CollegeServiceCycleStart = DateTime.Parse(testData.Services.College.ServiceCycleStart);
                    service.CollegeServiceCycleEnd = DateTime.Parse(testData.Services.College.ServiceCycleEnd);
                }
            }

            DbOpe_crm_contract_serviceinfo_wits.Instance.Insert(service);
            LogUtil.AddLog($"创建测试主服务成功: {service.Id}");
            
            return service.Id;
        }

        /// <summary>
        /// 创建测试用户数据
        /// </summary>
        private async Task<List<string>> CreateTestUsers(List<TestUserData> usersData, string serviceId, TestMainServiceData mainServiceData)
        {
            var userIds = new List<string>();

            foreach (var userData in usersData)
            {
                var user = new Db_crm_contract_serviceinfo_wits_user
                {
                    Id = userData.Id,
                    WitsServeId = serviceId,
                    AccountNumber = userData.AccountNumber,
                    PassWord = userData.Password,
                    // gtis的类型和crm系统的类型枚举值不同
                    AccountType = userData.AccountType == 0? 1 : 2,
                    // 只有当服务开通时才设置相应权限
                    GtisPermission = userData.GtisPermission,
                    GlobalSearchPermission = userData.GlobalSearchPermission ,
                    SalesWitsPermission = userData.SalesWitsPermission ,
                    CollegePermission = userData.CollegePermission ,
                    // 服务变更时：使用原有用户信息，新增时：使用默认值
                    SharePeopleNum = userData.IsExistingUser ? (userData.SharePeopleNum ?? 10) : 10,
                    AuthorizationNum = userData.IsExistingUser ? userData.AuthorizationNum : null,
                    // SalesWits使用者信息
                    SalesWitsPhoneId = userData.SalesWitsUser?.Id,
                    // 如果是现有用户且有原始系统ID，保留它
                    UserId = userData.IsExistingUser ? userData.OriginalSysUserID : null,

                    CreateUser = TEST_USER_ID,
                    CreateDate = DateTime.Now,
                    Deleted = false
                };

                DbOpe_crm_contract_serviceinfo_wits_user.Instance.Insert(user);
                userIds.Add(user.Id);

                LogUtil.AddLog($"创建用户记录: {userData.AccountNumber}, 是否现有用户: {userData.IsExistingUser}, 共享次数: {user.SharePeopleNum}");
            }

            LogUtil.AddLog($"创建测试用户成功，共 {userIds.Count} 个用户");
            return userIds;
        }

        /// <summary>
        /// 创建各服务专门配置数据
        /// </summary>
        /// <returns>返回各服务的配置ID</returns>
        private async Task<ServiceConfigIds> CreateTestServiceConfigs(TestServicesData servicesData, string contractId, string witsApplId, TestMainServiceData mainServiceData)
        {
            var configIds = new ServiceConfigIds();
            // 只有当GTIS服务开通时才创建配置
            if (servicesData.Gtis != null && mainServiceData.IsGtisAudit)
            {
                var gtisService = new Db_crm_contract_serviceinfo_gtis
                {
                    Id = servicesData.Gtis.Id,
                    ContractId = contractId,
                    WitsApplId = witsApplId,
                    ProductId = "4a0ffc9a-6a44-4362-a76a-07492d0a81fa", //高级版
                    PrimaryAccountsNum = servicesData.Gtis.PrimaryAccountsNum,
                    SubAccountsNum = servicesData.Gtis.SubAccountsNum,
                    AuthorizationNum = servicesData.Gtis.AuthorizationNum,
                    ServiceCycleStart = DateTime.Parse(servicesData.Gtis.ServiceCycleStart),
                    ServiceCycleEnd = DateTime.Parse(servicesData.Gtis.ServiceCycleEnd),
                    State = (int)EnumContractServiceState.TO_BE_OPENED,
                    ShareUsageNum = servicesData.Gtis.ShareUsageNum,
                    CreateUser = TEST_USER_ID,
                    CreateDate = DateTime.Now,
                    Deleted = false,
                    IsProcessed = 0
                };

                DbOpe_crm_contract_serviceinfo_gtis.Instance.Insert(gtisService);
                configIds.GtisServiceId = gtisService.Id;
                LogUtil.AddLog($"创建GTIS服务配置成功: {gtisService.Id}");
            }

            // 只有当环球搜服务开通时才创建配置
            if (servicesData.GlobalSearch != null && mainServiceData.IsGlobalSearchAudit)
            {
                var globalSearchService = new Db_crm_contract_serviceinfo_globalsearch
                {
                    Id = servicesData.GlobalSearch.Id,
                    ContractId = contractId,
                    WitsApplId = witsApplId,
                    ProductId = "a2f16437-e95f-4c3f-8e97-a22a1bbf0ab8", //环球搜 
                    PrimaryAccountsNum = 1, // 默认1个主账号
                    SubAccountsNum = servicesData.GlobalSearch.SubAccountsNum, // 子账号数量
                    SettlementLevel = servicesData.GlobalSearch.SettlementLevel,
                    SettlementMonth = servicesData.GlobalSearch.SettlementMonth,
                    State = EnumContractServiceState.TO_BE_OPENED,
                    CreateUser = TEST_USER_ID,
                    CreateDate = DateTime.Now,
                    Deleted = false,
                    IsProcessed = (int)EnumGtisServiceIsProcess.Not
                };

                DbOpe_crm_contract_serviceinfo_globalsearch.Instance.Insert(globalSearchService);
                configIds.GlobalSearchServiceId = globalSearchService.Id;
                LogUtil.AddLog($"创建环球搜服务配置成功: {globalSearchService.Id}");
            }

            // 只有当SalesWits服务开通时才创建配置
            if (servicesData.SalesWits != null && mainServiceData.IsSalesWitsAudit)
            {
                var salesWitsService = new Db_crm_contract_serviceinfo_saleswits
                {
                    Id = servicesData.SalesWits.Id,
                    ContractId = contractId,
                    WitsApplId = witsApplId,
                    ProductId = "7f75e303-5e07-11f0-9097-c025a58cf040", // salewits 
                    AccountsNum = servicesData.SalesWits.AccountsNum,
                    ServiceCycleStart = DateTime.Parse(servicesData.SalesWits.ServiceCycleStart),
                    ServiceCycleEnd = DateTime.Parse(servicesData.SalesWits.ServiceCycleEnd),
                    CurrentGiftMonths = servicesData.SalesWits.CurrentGiftMonths,
                    CurrentGiftTokens = servicesData.SalesWits.CurrentGiftTokens,
                    CurrentGiftEmails = servicesData.SalesWits.CurrentGiftEmails,
                    RechargeAmount = servicesData.SalesWits.RechargeAmount,
                    State = EnumContractServiceState.TO_BE_OPENED,
                    CreateUser = TEST_USER_ID,
                    CreateDate = DateTime.Now,
                    Deleted = false,
                    IsProcessed = (int)EnumGtisServiceIsProcess.Not
                };

                DbOpe_crm_contract_serviceinfo_saleswits.Instance.Insert(salesWitsService);
                configIds.SalesWitsServiceId = salesWitsService.Id;
                LogUtil.AddLog($"创建SalesWits服务配置成功: {salesWitsService.Id}");
            }

            // 只有当慧思学院服务开通时才创建配置
            if (servicesData.College != null && mainServiceData.IsCollegeAudit)
            {
                var collegeService = new Db_crm_contract_serviceinfo_college
                {
                    Id = servicesData.College.Id,
                    ContractId = contractId,
                    WitsApplId = witsApplId,
                    ProductId = "e9ca8fc7-669e-48a6-ba8d-d3c7e2875007",// 慧思学院
                    PrimaryAccountsNum = servicesData.College.PrimaryAccountsNum,
                    ServiceCycleStart = DateTime.Parse(servicesData.College.ServiceCycleStart),
                    ServiceCycleEnd = DateTime.Parse(servicesData.College.ServiceCycleEnd),
                    Remark = servicesData.College.Remark,
                    State = EnumContractServiceState.TO_BE_OPENED,
                    CreateUser = TEST_USER_ID,
                    CreateDate = DateTime.Now,
                    Deleted = false,
                    IsProcessed = (int)EnumGtisServiceIsProcess.Not
                };

                DbOpe_crm_contract_serviceinfo_college.Instance.Insert(collegeService);
                configIds.CollegeServiceId = collegeService.Id;
                LogUtil.AddLog($"创建慧思学院服务配置成功: {collegeService.Id}");
            }

            return configIds;
        }

        /// <summary>
        /// 清理服务配置数据
        /// </summary>
        private void ClearServiceConfigs(string serviceId, string contractId)
        {
            // 清理GTIS配置
            var gtisServices = DbOpe_crm_contract_serviceinfo_gtis.Instance
                .GetDataList(x => x.WitsApplId == serviceId);
            foreach (var service in gtisServices)
            {
                DbOpe_crm_contract_serviceinfo_gtis.Instance.Delete(service);
            }

            // 清理环球搜配置
            var globalSearchServices = DbOpe_crm_contract_serviceinfo_globalsearch.Instance
                .GetDataList(x => x.WitsApplId == serviceId);
            foreach (var service in globalSearchServices)
            {
                DbOpe_crm_contract_serviceinfo_globalsearch.Instance.Delete(service);
            }

            // 清理SalesWits配置
            var salesWitsServices = DbOpe_crm_contract_serviceinfo_saleswits.Instance
                .GetDataList(x => x.WitsApplId == serviceId);
            foreach (var service in salesWitsServices)
            {
                DbOpe_crm_contract_serviceinfo_saleswits.Instance.Delete(service);
            }

            // 清理慧思学院配置
            var collegeServices = DbOpe_crm_contract_serviceinfo_college.Instance
                .GetDataList(x => x.WitsApplId == serviceId);
            foreach (var service in collegeServices)
            {
                DbOpe_crm_contract_serviceinfo_college.Instance.Delete(service);
            }
        }

        /// <summary>
        /// 获取客户的未开通wits服务id
        /// </summary>
        private (string,string) GetCustomerPendingServiceWitsId(string customerCode,bool isChange = false)
        {
            try
            {
                // 查找该客户的合同
                var contractOpe = new DbOpe_crm_contract();
                var contract = contractOpe.GetData(x => x.ContractNum == customerCode && x.Deleted == false && x.ContractStatus == (int)EnumContractStatus.Pass);
                var processingType = isChange ? (int)EnumProcessingType.Change : (int)EnumProcessingType.Add ;
                if (contract != null)
                {
                    // 查找合同的服务申请记录
                    var service = DbOpe_crm_contract_serviceinfo_wits.Instance.GetData(x => x.ContractId == contract.Id &&
                                                                  x.Deleted == false &&
                                                                  x.State == EnumContractServiceState.VALID && // 已开通状态
                                                                  (x.IsProcessed ==null ||x.IsProcessed == 0)
                                                                  &&x.ProcessingType == processingType); // 但未处理

                    if (service != null)
                    {
                        return (service.Id,contract.Id);
                    }
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddLog($"查找客户未开通服务异常：{ex.Message}");
                throw;
            }

            return (null,null);
        }



        /// <summary>
        /// 执行SalesWits资源下发（调用服务开通模块）
        /// </summary>
        /// <param name="contract">合同信息</param>
        /// <param name="salesWitsService">SalesWits服务配置</param>
        /// <param name="emailCount">邮件数量</param>
        /// <param name="tokenCount">Token数量（万个）</param>
        /// <param name="rechargeAmount">充值金额（元）</param>
        /// <param name="distributionType">下发类型</param>
        /// <returns>下发结果</returns>
        private async Task<SalesWitsResourceDistributionResult> ExecuteSalesWitsResourceDistribution(
            Db_crm_contract contract,
            Db_crm_contract_serviceinfo_saleswits salesWitsService,
            int emailCount,
            int tokenCount,
            decimal rechargeAmount,
            string distributionType)
        {
            var result = new SalesWitsResourceDistributionResult
            {
                Details = new SalesWitsDistributionDetails
                {
                    ContractNum = contract.ContractNum,
                    CustomerName = contract.FirstParty,
                    ActualEmailCount = emailCount,
                    ActualTokenCount = tokenCount,
                    ActualRechargeAmount = rechargeAmount
                }
            };

            try
            {
                LogUtil.AddLog($"开始执行SalesWits资源下发（调用服务开通模块），合同: {contract.ContractNum}, 邮件: {emailCount}, Token: {tokenCount}万个, 充值: {rechargeAmount}元");

                // 1. 构建ServiceOpeningParams参数
                var openingParams = BuildServiceOpeningParamsForResourceDistribution(
                    contract,
                    salesWitsService,
                    emailCount,
                    tokenCount,
                    rechargeAmount,
                    distributionType);

                if (!openingParams.IsValid)
                {
                    result.Success = false;
                    result.Message = $"构建服务开通参数失败: {openingParams.ErrorMessage}";
                    return result;
                }

                LogUtil.AddLog("ServiceOpeningParams构建成功，开始调用服务开通模块");

                // 2. 调用服务开通模块的ProcessSaleWitsService方法（定时下发使用的核心方法）
                var serviceOpeningBll = new CRM2_API.BLL.ServiceOpening.BLL_ServiceOpening();

                // 使用反射调用私有方法ProcessSaleWitsService
                var processMethod = typeof(CRM2_API.BLL.ServiceOpening.BLL_ServiceOpening)
                    .GetMethod("ProcessSaleWitsService", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                if (processMethod == null)
                {
                    result.Success = false;
                    result.Message = "未找到ProcessSaleWitsService方法";
                    return result;
                }

                // 创建模拟的GtisResult（定时下发不依赖GTIS结果）
                var gtisResult = new CRM2_API.Model.BLLModel.ServiceOpening.GtisResult { Success = true };

                // 调用ProcessSaleWitsService方法
                var invokeResult = processMethod.Invoke(serviceOpeningBll, new object[] { openingParams, gtisResult });
                if (invokeResult == null)
                {
                    result.Success = false;
                    result.Message = "ProcessSaleWitsService方法调用返回null";
                    return result;
                }
                var saleWitsResult = await (Task<CRM2_API.Model.BLLModel.ServiceOpening.SaleWitsResult>)invokeResult;

                // 3. 处理返回结果
                if (saleWitsResult.Success)
                {
                    result.Success = true;
                    result.Message = saleWitsResult.Message;
                    result.DistributionId = saleWitsResult.DistributionId;
                    result.DistributionTime = saleWitsResult.DistributionTime;
                    // 注意：SaleWitsResult没有TenantId和ResponseData字段，这些信息在内部处理
                    result.Details.ApiResponse = "通过服务开通模块执行，详细响应数据请查看系统日志";



                    LogUtil.AddLog($"SalesWits资源下发成功: {result.Message}");
                }
                else
                {
                    result.Success = false;
                    result.Message = $"SalesWits资源下发失败: {saleWitsResult.Message}";
                    LogUtil.AddErrorLog($"SalesWits资源下发失败: {saleWitsResult.Message}");
                }
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"资源下发异常: {ex.Message}";
                LogUtil.AddErrorLog($"SalesWits资源下发异常: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 构建用于资源下发的ServiceOpeningParams参数
        /// </summary>
        /// <param name="contract">合同信息</param>
        /// <param name="salesWitsService">SalesWits服务配置</param>
        /// <param name="emailCount">邮件数量</param>
        /// <param name="tokenCount">Token数量（万个）</param>
        /// <param name="rechargeAmount">充值金额（元）</param>
        /// <param name="distributionType">下发类型</param>
        /// <returns>ServiceOpeningParams对象</returns>
        private CRM2_API.Model.BLLModel.ServiceOpening.ServiceOpeningParams BuildServiceOpeningParamsForResourceDistribution(
            Db_crm_contract contract,
            Db_crm_contract_serviceinfo_saleswits salesWitsService,
            int emailCount,
            int tokenCount,
            decimal rechargeAmount,
            string distributionType)
        {
            var openingParams = new CRM2_API.Model.BLLModel.ServiceOpening.ServiceOpeningParams();

            try
            {
                LogUtil.AddLog("开始构建ServiceOpeningParams参数");

                // 1. 设置基本信息
                openingParams.Contract = contract;

                // 2. 创建模拟的主服务信息
                var mainService = new Db_crm_contract_serviceinfo_wits
                {
                    Id = Guid.NewGuid().ToString(),
                    ContractId = contract.Id,
                    HasSalesWitsApp = true,
                    IsSalesWitsAudit = true,
                    SalesWitsAccountsNum = salesWitsService.AccountsNum,
                    SalesWitsServiceCycleStart = salesWitsService.ServiceCycleStart,
                    SalesWitsServiceCycleEnd = salesWitsService.ServiceCycleEnd
                };
                openingParams.MainService = mainService;

                // 3. 设置SalesWits基础配置
                openingParams.SalesWitsBasicConfig = new CRM2_API.Model.BLLModel.ServiceOpening.SalesWitsBasicConfig
                {
                    ContractId = contract.Id,
                    HasAppPermission = true,
                    MaxAccountsNum = salesWitsService.AccountsNum ?? 0,
                    ServiceCycleStart = salesWitsService.ServiceCycleStart,
                    ServiceCycleEnd = salesWitsService.ServiceCycleEnd
                };

                // 4. 设置SalesWits特殊配置
                var salesWitsSpecialConfig = new CRM2_API.Model.BLLModel.ServiceOpening.SalesWitsSpecialConfig
                {
                    ServiceInfo = salesWitsService
                };

                // 5. 构建资源数据准备结果（模拟PrepareSaleWitsResourceData的结果）
                var resourceDataResult = new CRM2_API.Model.BLLModel.ServiceOpening.SaleWitsResourceDataResult();
                resourceDataResult.SetSuccess($"手动测试资源下发: 邮件{emailCount}封, Token{tokenCount}万个, 充值{rechargeAmount}元");
                resourceDataResult.DistributionType = CRM2_API.Model.BLLModel.Enum.EnumSaleWitsDistributionType.AnnualDistribution;
                resourceDataResult.PresetResources = new CRM2_API.Model.BLLModel.ServiceOpening.SaleWitsPresetResources
                {
                    MonthsCount = 0, // 手动测试不涉及月份
                    EmailCount = emailCount,
                    TokenCount = tokenCount,
                    RechargeAmount = rechargeAmount
                };

                salesWitsSpecialConfig.ResourceDataResult = resourceDataResult;
                openingParams.SalesWitsSpecialConfig = salesWitsSpecialConfig;

                // 6. 设置为有效状态
                openingParams.SetValid();

                LogUtil.AddLog("ServiceOpeningParams构建完成");
                return openingParams;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"构建ServiceOpeningParams异常: {ex.Message}");
                openingParams.SetError($"构建参数异常: {ex.Message}");
                return openingParams;
            }
        }



        /// <summary>
        /// 处理环球搜码写回到globalsearchuser表
        /// </summary>
        /// <param name="serviceId">服务ID</param>
        /// <param name="result">服务开通结果</param>
        private async Task ProcessGlobalSearchCodeWriteBack(string serviceId, CRM2_API.Model.BLLModel.ServiceOpening.ServiceOpeningResult result)
        {
            try
            {
                // 检查是否有环球搜服务结果且包含新创建的环球搜码
                if (result.GlobalSearchResult?.Success == true &&
                    result.GlobalSearchResult.AllCodes?.Count > 0)
                {
                    LogUtil.AddLog($"开始处理环球搜码写回，服务ID: {serviceId}");

                    // 获取wits服务信息以获取关联的申请ID
                    var witsService = DbOpe_crm_contract_serviceinfo_wits.Instance
                        .GetData(x => x.Id == serviceId);

                    if (witsService?.WitsApplId != null)
                    {
                        // 通过申请ID查找关联的环球搜服务
                        var globalSearchService = DbOpe_crm_contract_serviceinfo_globalsearch.Instance
                            .GetData(x => x.WitsApplId == witsService.WitsApplId && x.Deleted == false && x.IsHistory == false);

                        if (globalSearchService != null)
                        {
                            var globalSearchServiceId = globalSearchService.Id;
                            var allCodes = result.GlobalSearchResult.AllCodes;

                            LogUtil.AddLog($"找到关联的环球搜服务ID: {globalSearchServiceId}, 需要写回的环球搜码数量: {allCodes.Count}");

                            // 写回环球搜码到globalsearchuser表
                            foreach (var code in allCodes)
                            {
                                // 检查是否已存在该环球搜码
                                var existingUser = DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance
                                    .GetData(x => x.ContractServiceInfoGlobalSearchId == globalSearchServiceId &&
                                                 x.AccountNumber == code &&
                                                 x.Deleted == false);

                                if (existingUser == null)
                                {
                                    // 创建新的环球搜用户记录
                                    var newUser = new Db_crm_contract_serviceinfo_globalsearch_user
                                    {
                                        Id = Guid.NewGuid().ToString(),
                                        ContractServiceInfoGlobalSearchId = globalSearchServiceId,
                                        AccountNumber = code, // 环球搜码
                                        AccountType = result.GlobalSearchResult.PrimaryCode == code ?
                                            EnumContractServiceGlobalSearchAccountType.PrimaryAccount :
                                            EnumContractServiceGlobalSearchAccountType.SubAccount,
                                        OpeningStatus = EnumContractServiceGlobalSearchUserState.Normal,
                                        CreateDate = DateTime.Now,
                                        CreateUser = TEST_USER_ID,
                                        Deleted = false
                                    };

                                    DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.Insert(newUser);
                                    LogUtil.AddLog($"已写回环球搜码到globalsearchuser表: {code}");
                                }
                                else
                                {
                                    LogUtil.AddLog($"环球搜码已存在，跳过写回: {code}");
                                }
                            }

                            LogUtil.AddLog($"环球搜码写回完成，服务ID: {serviceId}");
                        }
                        else
                        {
                            LogUtil.AddLog($"未找到关联的环球搜服务，跳过环球搜码写回");
                        }
                    }
                    else
                    {
                        LogUtil.AddLog($"未找到关联的申请ID，跳过环球搜码写回");
                    }
                }
                else
                {
                    LogUtil.AddLog($"无需处理环球搜码写回：无环球搜结果或无新创建的环球搜码");
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"处理环球搜码写回异常: {ex.Message}");
                // 不抛出异常，避免影响主流程
            }
        }

        /// <summary>
        /// 更新所有关联服务表的处理状态
        /// </summary>
        /// <param name="serviceId">服务ID</param>
        private void UpdateAllServiceTablesStatus(string serviceId)
        {
            try
            {
                LogUtil.AddLog($"开始更新所有关联服务表的处理状态，服务ID: {serviceId}");

                // 获取wits服务信息
                var witsService = DbOpe_crm_contract_serviceinfo_wits.Instance
                    .GetData(x => x.Id == serviceId);

                if (witsService != null)
                {
                    var now = DateTime.Now;

                    // 1. 更新wits服务表
                    witsService.IsProcessed = 1;
                    witsService.ProcessedTime = now;
                    DbOpe_crm_contract_serviceinfo_wits.Instance.Update(witsService);
                    LogUtil.AddLog($"已更新wits服务表处理状态");

                    // 2. 通过申请ID更新关联的环球搜服务表
                    if (!string.IsNullOrEmpty(witsService.WitsApplId))
                    {
                        var globalSearchService = DbOpe_crm_contract_serviceinfo_globalsearch.Instance
                            .GetData(x => x.WitsApplId == witsService.WitsApplId && x.Deleted == false && x.IsHistory == false);

                        if (globalSearchService != null)
                        {
                            globalSearchService.IsProcessed = (int)EnumGtisServiceIsProcess.Processed;
                            globalSearchService.ProcessedTime = now;
                            DbOpe_crm_contract_serviceinfo_globalsearch.Instance.Update(globalSearchService);
                            LogUtil.AddLog($"已更新环球搜服务表处理状态: {globalSearchService.Id}");
                        }
                    }

                    // 3. 通过申请ID更新关联的GTIS服务表
                    if (!string.IsNullOrEmpty(witsService.WitsApplId))
                    {
                        var gtisService = DbOpe_crm_contract_serviceinfo_gtis.Instance
                            .GetData(x => x.WitsApplId == witsService.WitsApplId && x.Deleted == false);

                        if (gtisService != null)
                        {
                            gtisService.IsProcessed = 1;
                            gtisService.ProcessedTime = now;
                            DbOpe_crm_contract_serviceinfo_gtis.Instance.Update(gtisService);
                            LogUtil.AddLog($"已更新GTIS服务表处理状态: {gtisService.Id}");
                        }
                    }

                    // 4. 通过申请ID更新关联的慧思学院服务表
                    if (!string.IsNullOrEmpty(witsService.WitsApplId))
                    {
                        var collegeService = DbOpe_crm_contract_serviceinfo_college.Instance
                            .GetData(x => x.WitsApplId == witsService.WitsApplId && x.Deleted == false && x.IsHistory == false);

                        if (collegeService != null)
                        {
                            collegeService.IsProcessed = (int)EnumGtisServiceIsProcess.Processed;
                            collegeService.ProcessedTime = now;
                            DbOpe_crm_contract_serviceinfo_college.Instance.Update(collegeService);
                            LogUtil.AddLog($"已更新慧思学院服务表处理状态: {collegeService.Id}");
                        }
                    }

                    LogUtil.AddLog($"所有关联服务表处理状态更新完成");
                }
                else
                {
                    LogUtil.AddLog($"未找到对应的wits服务记录: {serviceId}");
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"更新服务表处理状态异常: {ex.Message}");
                // 不抛出异常，避免影响主流程
            }
        }

        #endregion
    }

    #region 数据模型

    /// <summary>
    /// 服务开通测试数据
    /// </summary>
    public class ServiceOpeningTestData
    {
        public TestContractData Contract { get; set; }
        public TestMainServiceData MainService { get; set; }
        public List<TestUserData> Users { get; set; }
        public TestServicesData Services { get; set; }
    }

    /// <summary>
    /// 测试合同数据 - 简化版
    /// </summary>
    public class TestContractData
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string CustomerCode { get; set; } // 客户编码（必填）
        public string OriginalCustomerCode { get; set; } // 被续约的客户编码（续约时必填）
        public int ContractType { get; set; } = 1; // 1-新增合同, 2-合同续约, 3-新增项目
        public string ContractName => GenerateContractName();
        public string SigningDate => DateTime.Now.ToString("yyyy-MM-dd");

        private string GenerateContractName()
        {
            var typeDesc = ContractType switch
            {
                1 => "新增合同",
                2 => "合同续约",
                3 => "新增项目",
                _ => "服务合同"
            };
            return $"{CustomerCode}-{typeDesc}-{DateTime.Now:yyyy年MM月}";
        }
    }

    /// <summary>
    /// 测试主服务数据
    /// </summary>
    public class TestMainServiceData
    {
        public string Id { get; set; }
        public string ContractId { get; set; }
        public int ProcessingType { get; set; }
        public int State { get; set; }
        public bool IsGtisAudit { get; set; }
        public bool IsGlobalSearchAudit { get; set; }
        public bool IsSalesWitsAudit { get; set; }
        public bool IsCollegeAudit { get; set; }
        public string CreateUser { get; set; }
        public string OldContractNum { get; set; } // 原合同编码（服务变更/续约时使用）
    }

    /// <summary>
    /// 测试用户数据
    /// </summary>
    public class TestUserData
    {
        public string Id { get; set; }
        public string AccountNumber { get; set; }
        public string Password { get; set; }
        public int AccountType { get; set; }
        public bool GtisPermission { get; set; }
        public bool GlobalSearchPermission { get; set; }
        public bool SalesWitsPermission { get; set; }
        public TestSalesWitsUserInfo SalesWitsUser { get; set; } // SalesWits使用者信息
        public bool CollegePermission { get; set; }
        public string CreateUser { get; set; }

        // 服务变更时保留的原有用户信息
        public string OriginalSysUserID { get; set; } // GTIS系统用户ID
        public int? SharePeopleNum { get; set; } // 共享次数
        public int? AuthorizationNum { get; set; } // 授权国家次数
        public DateTime? StartDate { get; set; } // 服务开始时间
        public DateTime? EndDate { get; set; } // 服务结束时间
        public bool IsExistingUser { get; set; } // 是否为现有用户
        public string Email { get; set; } // 邮箱
    }

    /// <summary>
    /// 测试服务配置数据
    /// </summary>
    public class TestServicesData
    {
        public TestGtisServiceData Gtis { get; set; }
        public TestGlobalSearchServiceData GlobalSearch { get; set; }
        public TestSalesWitsServiceData SalesWits { get; set; }
        public TestCollegeServiceData College { get; set; }
    }

    /// <summary>
    /// 测试GTIS服务数据
    /// </summary>
    public class TestGtisServiceData
    {
        public string Id { get; set; }
        public int PrimaryAccountsNum { get; set; }
        public int SubAccountsNum { get; set; }
        public int AuthorizationNum { get; set; }
        public int ShareUsageNum{ get{

            return PrimaryAccountsNum * 10 + SubAccountsNum * 10;
        }}
        public string ServiceCycleStart { get; set; }
        public string ServiceCycleEnd { get; set; }
        public string CreateUser { get; set; }
    }

    /// <summary>
    /// 测试环球搜服务数据
    /// </summary>
    public class TestGlobalSearchServiceData
    {
        public string Id { get; set; }
        public int PrimaryAccountsNum { get; set; } = 1; // 主账号数量，固定为1
        public int SubAccountsNum { get; set; } // 子账号数量
        public string SettlementLevel => $"{PrimaryAccountsNum}+{SubAccountsNum}"; // 结算级别格式：主账号+子账号
        public int SettlementMonth { get; set; } = 12; // 结算月份
        public decimal SettlementCount { get; set; } // 结算单位数量
        public string ServiceCycleStart { get; set; } = DateTime.Now.ToString("yyyy-MM-dd");
        public string ServiceCycleEnd { get; set; } = DateTime.Now.AddYears(1).ToString("yyyy-MM-dd");
        public string CreateUser { get; set; }

        /// <summary>
        /// 获取总账号数量
        /// </summary>
        public int GetTotalAccountsNum() => PrimaryAccountsNum + SubAccountsNum;
    }

    /// <summary>
    /// 测试SalesWits服务数据
    /// </summary>
    public class TestSalesWitsServiceData
    {
        public string Id { get; set; }
        public int AccountsNum { get; set; } // 账号数量
        public string ServiceCycleStart { get; set; } = DateTime.Now.ToString("yyyy-MM-dd"); // 服务开始时间
        public string ServiceCycleEnd { get; set; } = DateTime.Now.AddYears(1).ToString("yyyy-MM-dd"); // 服务结束时间
        public int CurrentGiftMonths { get; set; } = 0; // 本次赠送月份
        public int CurrentGiftTokens { get; set; } = 0; // 本次赠送Token数量（万个）
        public int CurrentGiftEmails { get; set; } = 0; // 本次赠送邮件数量（默认1个账号12个月 = 1*300*12 = 3600封）
        public decimal RechargeAmount { get; set; } = 0; // 充值金额（元）
        public string CreateUser { get; set; }
    }

    /// <summary>
    /// 测试慧思学院服务数据
    /// </summary>
    public class TestCollegeServiceData
    {
        public string Id { get; set; }
        public int PrimaryAccountsNum { get; set; } = 1; // 主账号数量，通常为1
        public string ServiceCycleStart { get; set; } = DateTime.Now.ToString("yyyy-MM-dd"); // 服务开始时间
        public string ServiceCycleEnd { get; set; } = DateTime.Now.AddYears(1).ToString("yyyy-MM-dd"); // 服务结束时间
        public string Remark { get; set; } // 备注信息
        public string CreateUser { get; set; }
    }

    /// <summary>
    /// 服务开通测试请求
    /// </summary>
    public class ServiceOpeningTestRequest
    {
        public string ServiceId { get; set; }
    }

    /// <summary>
    /// 获取客户信息请求
    /// </summary>
    public class GetCustomerInfoRequest
    {
        public string CustomerCode { get; set; }
    }

    /// <summary>
    /// 清理测试数据请求
    /// </summary>
    public class ClearTestDataRequest
    {
        public string CreateUser { get; set; }
    }

    /// <summary>
    /// 获取未开通服务请求
    /// </summary>
    public class GetPendingServicesRequest
    {
        public string CustomerCode { get; set; }
    }

    /// <summary>
    /// 获取未开通服务结果
    /// </summary>
    public class GetPendingServicesResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public string ServiceId { get; set; }
        public string ContractId { get; set; }
    }

    /// <summary>
    /// 查询GTIS客户信息请求
    /// </summary>
    public class GetGtisCustomerInfoRequest
    {
        public string CustomerCode { get; set; }
    }

    /// <summary>
    /// 查询GTIS客户信息结果
    /// </summary>
    public class GetGtisCustomerInfoResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public List<CRM2_API.Model.BusinessModel.BM_GtisOpe.BM_GtisOpeUserInfo> Data { get; set; }
    }

    /// <summary>
    /// 查询客户合同列表请求
    /// </summary>
    public class GetCustomerContractsRequest
    {
        public string CustomerCode { get; set; }
    }

    /// <summary>
    /// 查询客户合同列表结果
    /// </summary>
    public class GetCustomerContractsResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public Db_crm_contract Data { get; set; }
    }

    /// <summary>
    /// SalesWits使用者信息
    /// </summary>
    public class TestSalesWitsUserInfo
    {
        /// <summary>
        /// 使用者ID
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 联系人姓名
        /// </summary>
        public string LinkMan { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        public string Phone { get; set; }

        /// <summary>
        /// 邮箱地址
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// 微信昵称
        /// </summary>
        public string WxNickName { get; set; }
    }

    /// <summary>
    /// 服务配置ID集合
    /// </summary>
    public class ServiceConfigIds
    {
        public string GtisServiceId { get; set; } = string.Empty;
        public string GlobalSearchServiceId { get; set; } = string.Empty;
        public string SalesWitsServiceId { get; set; } = string.Empty;
        public string CollegeServiceId { get; set; } = string.Empty;
    }

    /// <summary>
    /// SalesWits资源下发测试请求
    /// </summary>
    public class SalesWitsResourceDistributionRequest
    {
        /// <summary>
        /// 合同ID
        /// </summary>
        public string ContractId { get; set; }

        /// <summary>
        /// 邮件数量
        /// </summary>
        public int EmailCount { get; set; } = 0;

        /// <summary>
        /// Token数量（万个）
        /// </summary>
        public int TokenCount { get; set; } = 0;

        /// <summary>
        /// 充值金额（元）
        /// </summary>
        public decimal RechargeAmount { get; set; } = 0;

        /// <summary>
        /// 下发类型
        /// </summary>
        public string DistributionType { get; set; } = "ManualTest";
    }

    /// <summary>
    /// SalesWits资源下发测试结果
    /// </summary>
    public class SalesWitsResourceDistributionResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 结果消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 下发记录ID
        /// </summary>
        public string DistributionId { get; set; }

        /// <summary>
        /// 下发时间
        /// </summary>
        public DateTime? DistributionTime { get; set; }

        /// <summary>
        /// 租户ID
        /// </summary>
        public string TenantId { get; set; }

        /// <summary>
        /// 详细信息
        /// </summary>
        public SalesWitsDistributionDetails Details { get; set; }
    }

    /// <summary>
    /// SalesWits下发详细信息
    /// </summary>
    public class SalesWitsDistributionDetails
    {
        /// <summary>
        /// 合同编号
        /// </summary>
        public string ContractNum { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary>
        public string CustomerName { get; set; }

        /// <summary>
        /// 实际下发的邮件数量
        /// </summary>
        public int ActualEmailCount { get; set; }

        /// <summary>
        /// 实际下发的Token数量（万个）
        /// </summary>
        public int ActualTokenCount { get; set; }

        /// <summary>
        /// 实际充值金额（元）
        /// </summary>
        public decimal ActualRechargeAmount { get; set; }

        /// <summary>
        /// API响应数据
        /// </summary>
        public string ApiResponse { get; set; }
    }

    /// <summary>
    /// SalesWits API调用结果
    /// </summary>
    public class SalesWitsApiResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 成功消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 下发记录ID
        /// </summary>
        public string DistributionId { get; set; }

        /// <summary>
        /// 下发时间
        /// </summary>
        public DateTime DistributionTime { get; set; }

        /// <summary>
        /// API响应数据
        /// </summary>
        public string ResponseData { get; set; }
    }

    /// <summary>
    /// SalesWits合同列表结果
    /// </summary>
    public class SalesWitsContractsResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 结果消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 合同列表数据
        /// </summary>
        public List<SalesWitsContractInfo> Data { get; set; }
    }

    /// <summary>
    /// SalesWits合同信息
    /// </summary>
    public class SalesWitsContractInfo
    {
        /// <summary>
        /// 合同ID
        /// </summary>
        public string ContractId { get; set; }

        /// <summary>
        /// 合同编号
        /// </summary>
        public string ContractNum { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary>
        public string CustomerName { get; set; }

        /// <summary>
        /// SalesWits账号数量
        /// </summary>
        public int AccountsNum { get; set; }

        /// <summary>
        /// 服务周期
        /// </summary>
        public string ServiceCycle { get; set; }
    }

    #endregion
}
