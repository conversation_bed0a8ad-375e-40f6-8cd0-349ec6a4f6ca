﻿using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using CRM2_API.BLL.Common;
using CRM2_API.Common;
using CRM2_API.Common.AppSetting;
using CRM2_API.DAL.DbModel.Gtis;
using CRM2_API.Model.BusinessModel.BM_GtisOpe;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using LgyUtil;
using Magicodes.ExporterAndImporter.Excel;

namespace CRM2_API.BLL.GtisOpe
{
    /// <summary>
    /// gtis相关操作
    /// </summary>
    public class BLL_GtisOpe : BaseBLL<BLL_GtisOpe>
    {
        #region 正式账号

        /// <summary>
        /// 获取租户id （crm下发资源接口要用）
        /// </summary>
        /// <param name="svcode">客户编码</param>
        /// <param name="sysuserid">一个账户的id，账户id和使用者id，二选一</param>
        /// <param name="phoneid">使用者id，账户id和使用者id，二选一</param>
        /// <returns></returns>
        public async Task<BM_GetAccountTenantId_Out> GetAccountTenantId(string svcode,string sysuserid = "",string phoneid="")
        {
            return await GetGtisMessage<BM_GetAccountTenantId_Out>(GtisApiRoute.GetAccountTenantId, new {  svcode,sysuserid,phoneid }.SerializeNewtonJson());
        }

        // /// <summary>
        // /// 获取租户id （crm下发资源接口要用）
        // /// </summary>
        // /// <param name="inModel"></param>
        // /// <returns></returns>
        // public async Task<BM_GetAccountTenantId_Out> GetAccountTenantId(BM_GetAccountTenantId inModel)
        // {
        //     return await GetGtisMessage<BM_GetAccountTenantId_Out>(GtisApiRoute.GetAccountTenantId, new { inModel }.SerializeNewtonJson());
        // }

        /// <summary>
        /// 获取一个账户/使用者，按月统计的次数
        /// </summary>
        /// <param name="sysuserid">一个账户的id，账户id和使用者id，二选一</param>
        /// <param name="phoneid">使用者id，账户id和使用者id，二选一</param>
        /// <returns></returns>
        public async Task<List<BM_OneUserOperateLogByMonth>> GetOneUserOpeLogStaByMonth(string sysuserid, string phoneid = null)
        {
            return await GetGtisBackUpMessage<List<BM_OneUserOperateLogByMonth>>(GtisApiRoute.OneUserOpeLogStaByMonth, new { sysuserid, phoneid }.SerializeNewtonJson());
        }

        /// <summary>
        /// 获取操作历史详情
        /// </summary>
        /// <param name="logDetail">日志详情参数</param>
        /// <returns></returns>
        public async Task<ApiTableOut<BM_UserOpeLogDetail>> GetOpeLogDetail(BM_GtisOpeGetOpeLogDetal logDetail)
        {
            var ret = await GetGtisBackUpMessage(GtisApiRoute.OpeLogDetail, new
            {
                sysuserids = logDetail.SysUserID.IsNullOrEmpty() ? null : new List<string> { logDetail.SysUserID },
                phoneids = logDetail.PhoneID.IsNullOrEmpty() ? null : new List<string> { logDetail.PhoneID },
                opeType = logDetail.OpeType,
                year = logDetail.Year,
                months = logDetail.Months,
                isDownload = false,
                pageNum = logDetail.PageNum,
                pageSize = logDetail.PageSize
            }.SerializeNewtonJson());
            return new ApiTableOut<BM_UserOpeLogDetail>()
            {
                Total = ret.count,
                Data = ret.data.DeserializeNewtonJson<List<BM_UserOpeLogDetail>>()
            };
        }

        /// <summary>
        /// 导出操作历史详情
        /// </summary>
        /// <param name="logDetail">日志详情参数</param>
        /// <returns></returns>
        public async Task<Stream> GetOpeLogDetailDownload(BM_GtisOpeGetOpeLogDetalDownload logDetail)
        {
            var ret = await GetGtisBackUpMessage(GtisApiRoute.OpeLogDetail, new
            {
                sysuserids = logDetail.SysUserIDs,
                opeType = logDetail.OpeType,
                year = logDetail.Year,
                months = logDetail.Months,
                isDownload = true
            }.SerializeNewtonJson());
            var list = ret.data.DeserializeNewtonJson<List<BM_UserOpeLogDetail>>();
            //ExcelExporter exp = new ExcelExporter();
            //var expBytes = await exp.ExportAsByteArray(list);            
            ExcelExporterNPOI exp = new ExcelExporterNPOI();
            var expBytes = exp.ExportAsByteArray(list);
            return new MemoryStream(expBytes);
        }

        /// <summary>
        /// 获取导出日志详情
        /// </summary>
        /// <param name="expDetail">导出详细参数</param>
        /// <returns></returns>
        public async Task<ApiTableOut<BM_UserExportLogDetail>> GetExportLogDetail(BM_GtisOpeGetExportLogDetal expDetail)
        {
            var ret = await GetGtisBackUpMessage(GtisApiRoute.ExportLogDetail, new
            {
                sysuserid = expDetail.SysUserID,
                phoneid = expDetail.PhoneID,
                page = expDetail.PageNum,
                pageSize = expDetail.PageSize,
                year = expDetail.Year,
                months = expDetail.Months,
            }.SerializeNewtonJson());
            return new ApiTableOut<BM_UserExportLogDetail>()
            {
                Total = ret.count,
                Data = ret.data.DeserializeNewtonJson<List<BM_UserExportLogDetail>>()
            };
        }

        public async Task<List<BM_GtisOpe_G4DbNames>> GetGtisAllSids()
        {
            return await GetGtisMessage<List<BM_GtisOpe_G4DbNames>>(GtisApiRoute.GtisAllSids, "{}");
        }

        /// <summary>
        /// 批量修改用户状态
        /// </summary>
        /// <param name="listUserState">多个用户状态</param>
        /// <returns></returns>
        public async Task ModifyUserState(List<BM_ModifyUserState> listUserState)
        {
            await GetGtisMessage(GtisApiRoute.ModifyUserState, new { listUserState }.SerializeNewtonJson());
        }

        /// <summary>
        /// 添加正式用户
        /// </summary>
        /// <param name="crmAddUser">填写的用户信息</param>
        /// <returns></returns>
        public async Task<List<BM_AddGtisUserRetModel>> AddUser(BM_AddGtisUser crmAddUser)
        {
            return await GetGtisMessage<List<BM_AddGtisUserRetModel>>(GtisApiRoute.AddUser, new { crmAddUser }.SerializeNewtonJson());
        }

        /// <summary>
        /// 批量查询正式账号的综合状态
        /// </summary>
        /// <param name="svCodes">多个客户编码</param>
        /// <returns>key:客户编码   value:状态中文描述（正常、异常、停用、过期）</returns>
        public async Task<Dictionary<string, string>> GetUserState(string[] svCodes)
        {
            return await GetGtisMessage<Dictionary<string, string>>(GtisApiRoute.UserState, new { svCodes }.SerializeNewtonJson());
        }

        /// <summary>
        /// 查询一个账号的状态和信息
        /// </summary>
        /// <param name="svCodes">客户编码</param>
        /// <returns></returns>
        public async Task<List<BM_GtisOpeUserInfo>> GetUserInfo(params string[] svCodes)
        {
            return await GetGtisMessage<List<BM_GtisOpeUserInfo>>(GtisApiRoute.UserInfo, new { svCodes }.SerializeNewtonJson());
        }

        /// <summary>
        /// 续约(只是变更的续约)或变更
        /// </summary>
        /// <param name="changeInfo">变更的信息</param>
        /// <returns>添加的账号</returns>
        public async Task<List<BM_AddGtisUserRetModel>> RenewalContact(BM_GtisOpe_RenewalContact changeInfo)
        {
            return await GetGtisMessage<List<BM_AddGtisUserRetModel>>(GtisApiRoute.RenewalContact, new { changeInfo }.SerializeNewtonJson());
        }

        /// <summary>
        /// 获取一个公司所有账户操作记录的统计次数
        /// </summary>
        /// <param name="mainSysUserId">主账号id</param>
        /// <param name="svcodes">批量查询客户编码</param>
        /// <returns></returns>
        public async Task<List<BM_AllUserOperateLog>> GetAllUserOperateLogSta(string mainSysUserId, string[] svcodes)
        {
            return await GetGtisBackUpMessage<List<BM_AllUserOperateLog>>(GtisApiRoute.AllUserOperateLogSta, new { mainSysUserId, svcodes }.SerializeNewtonJson());
        }

        /// <summary>
        /// 获取一个公司所有账户操作记录的统计次数 新
        /// </summary>
        /// <param name="svcodes"></param>
        /// <param name="days"></param>
        /// <param name="state"></param>
        /// <param name="pageNum"></param>
        /// <param name="pageSize"></param>
        /// <param name="orderStr"></param>
        /// <param name="orderType"></param>
        /// <returns></returns>
        public async Task<ApiTableOut<BM_AllUserOperateLog>> GetAllUserOperateLogStaNew(string[] svcodes, int days,int state, int pageNum, int pageSize, int orderStr, int orderType)
        {
            //return await GetGtisMessage<List<BM_AllUserOperateLog>>(GtisApiRoute.AllUserOperateLogSta_Main, new {  svcodes, days, pageNum,pageSize,orderStr,orderType }.SerializeNewtonJson());
            var ret = await GetGtisBackUpMessage(GtisApiRoute.AllUserOperateLogSta_Main, new
            {
                svcodes = svcodes,
                days = days,
                State = state,
                pageNum = pageNum,
                pageSize = pageSize,
                orderStr = orderStr,
                orderType = orderType,
            }.SerializeNewtonJson());
            return new ApiTableOut<BM_AllUserOperateLog>()
            {
                Total = ret.count,
                Data = ret.data.DeserializeNewtonJson<List<BM_AllUserOperateLog>>()
            };
        }

        /// <summary>
        /// 保存环球搜码
        /// </summary>
        /// <param name="hqsInfo">保存环球搜填写的参数</param>
        /// <returns></returns>
        public async Task<List<BM_AddGtisUserRetModel>> SaveHqsCode(BM_SaveHqsCode hqsInfo)
        {
            return await GetGtisMessage<List<BM_AddGtisUserRetModel>>(GtisApiRoute.SaveHqsCode, new { hqsInfo }.SerializeNewtonJson());
        }

        /// <summary>
        /// 环球搜续约
        /// </summary>
        /// <param name="hqsChangeInfo">环球搜续约参数</param>
        /// <returns></returns>
        public async Task<List<BM_AddGtisUserRetModel>> RenewalHqsCode(BM_RenewalHqsCode hqsChangeInfo)
        {
            return await GetGtisMessage<List<BM_AddGtisUserRetModel>>(GtisApiRoute.RenewalHqsCode, new { hqsChangeInfo }.SerializeNewtonJson());
        }

        /// <summary>
        /// 根据客户编码，获取备注信息
        /// </summary>
        /// <param name="svcode">客户编码</param>
        /// <returns></returns>
        public async Task<string> GetCompanyOtherInfo(string svcode)
        {
            var ret = await GetGtisMessage(GtisApiRoute.GetCompanyOtherInfo, new { svcode }.SerializeNewtonJson());
            return ret.data;
        }

        /// <summary>
        /// 根据客户编码，删除账号，若客户编码不存在，也不报错
        /// </summary>
        /// <param name="svCode">客户编码</param>
        /// <param name="operatorName">操作人姓名</param>
        /// <param name="changeId">crm变更业务id(会将变更之前的数据还原)</param>
        public async Task DelCompany(string svCode, string operatorName, string changeId)
        {
            await GetGtisMessage(GtisApiRoute.DelCompany, new { svCode, operatorName, changeId }.SerializeNewtonJson());
        }

        /// <summary>
        /// 正式账户ip备案
        /// </summary>
        /// <param name="record">正式账号备案需要的参数</param>
        /// <returns></returns>
        public async Task IPKeepRecord(BM_GtisOpe_IPKeepRecord record)
        {
            await GetGtisMessage(GtisApiRoute.IPKeepRecord, new { record, isDemo = false }.SerializeNewtonJson());
        }

        /// <summary>
        /// 正式账户删除ip备案
        /// </summary>
        /// <param name="delModel">删除需要的模型</param>
        /// <returns></returns>
        public async Task IPKeepRecordDel(BM_GtisOpe_IPKeepRecordDel delModel)
        {
            await GetGtisMessage(GtisApiRoute.IPKeepRecordDel, new { delModel, isDemo = false }.SerializeNewtonJson());
        }

        /// <summary>
        /// 获取最新的新增/删除手机号
        /// </summary>
        /// <returns></returns>
        public async Task<ApiTableOut<BM_GtisOpe5Phone>> GetG5Phone_New()
        {
           var ret = await GetGtisMessage(GtisApiRoute.GetG5Phone_New, "{}");
            return new ApiTableOut<BM_GtisOpe5Phone>()
            {
                Total = ret.count,
                Data = ret.data.DeserializeNewtonJson<List<BM_GtisOpe5Phone>>()
            };
        }


        /// <summary>
        /// 最新的新增/删除手机号
        /// </summary>
        /// <returns></returns>
        public async Task<ApiTableOut<BM_GtisOpe5Phone>> GetG5Phone_List(int pageNum, int pageSize)
        {
            var ret = await GetGtisMessage(GtisApiRoute.GetG5Phone_List, new
            {
                pageNum = pageNum,
                pageSize= pageSize,
            }.SerializeNewtonJson());
            return new ApiTableOut<BM_GtisOpe5Phone>()
            {
                Total = ret.count,
                Data = ret.data.DeserializeNewtonJson<List<BM_GtisOpe5Phone>>()
            };
        }

        /// <summary>
        /// 最新的新增/删除手机号
        /// </summary>
        /// <returns></returns>
        public async Task RemoveUserSid(string[] sysuserids, int[] removeSids)
        {
             await GetGtisDemoMessage(GtisApiRoute.RemoveUserSid, new
            {
                sysuserids = sysuserids,
                removeSids = removeSids,
            }.SerializeNewtonJson());
        }


        /// <summary>
        /// 查询一个账号的状态和信息
        /// </summary>
        /// <param name="sysuserid"></param>
        /// <returns></returns>
        public async Task<List<Db_sysuser_phone>> GetAccountUsersInfo(string sysuserid)
        {
            return await GetGtisMessage<List<Db_sysuser_phone>>(GtisApiRoute.GetAccountUsersInfo, new { sysuserid }.SerializeNewtonJson());
        }

        #endregion

        #region 演示账号

        /// <summary>
        /// 添加演示账号
        /// </summary>
        /// <param name="demoAddInfo">演示账号开通信息</param>
        /// <returns></returns>
        public async Task<List<BM_AddGtisUserDemoRetModel>> AddUserDemo(BM_AddGtisUserDemo demoAddInfo)
        {
            return await GetGtisDemoMessage<List<BM_AddGtisUserDemoRetModel>>(GtisApiRoute.AddUserDemo, new { demoAddInfo }.SerializeNewtonJson());
        }

        /// <summary>
        /// 获取任务id下账号的综合状态
        /// </summary>
        /// <param name="taskIDs">多个任务id</param>
        /// <param name="statePara">账号状态： 0= 全部账号，1=今日有效，2=全部有效，3=已过期</param>
        /// <returns>key:taskID   value:这个taskID下所有账号状态枚举数组(正常、异常、停用、过期)</returns>
        public async Task<Dictionary<int, List<BM_Enum_DemoState>>> GetDemoState(int[] taskIDs,int statePara)
        {
            return await GetGtisDemoMessage<Dictionary<int, List<BM_Enum_DemoState>>>(GtisApiRoute.DemoState, new { taskIDs, statePara }.SerializeNewtonJson());
        }

        /// <summary>
        /// 根据任务id，获取任务id下开通所有的账号信息和状态
        /// </summary>
        /// <param name="taskID">任务id</param>
        /// <returns></returns>
        public async Task<List<BM_GtisOpe_DemoInfo>> GetDemoInfo(int taskID)
        {
            return await GetGtisDemoMessage<List<BM_GtisOpe_DemoInfo>>(GtisApiRoute.DemoInfo, new { taskID }.SerializeNewtonJson());
        }

        /// <summary>
        /// 获取当天开通演示账号剩余个数
        /// </summary>
        /// <param name="oprID">销售id</param>
        /// <returns></returns>
        public async Task<int> GetDemoLeftOpenTimes(string oprID)
        {
            return await GetGtisDemoMessage<int>(GtisApiRoute.DemoLeftOpenTimes, new { oprID }.SerializeNewtonJson());
        }

        /// <summary>
        /// 获取演示账号所有账户操作记录的统计次数
        /// </summary>
        /// <param name="taskID">任务id</param>
        /// <param name="oprID">销售的id</param>
        /// <returns></returns>
        public async Task<List<BM_AllUserOperateLog>> GetAllDemoUserOperateLogSta(int taskID, string oprID)
        {
            return await GetGtisDemoMessage<List<BM_AllUserOperateLog>>(GtisApiRoute.AllUserOperateLogSta, new { taskID, oprID }.SerializeNewtonJson());
        }

        /// <summary>
        /// 临时账户ip备案
        /// </summary>
        /// <param name="record">临时账号备案需要的参数</param>
        /// <returns></returns>
        public async Task IPKeepRecordDemo(BM_GtisOpe_IPKeepRecordDemo record)
        {
            await GetGtisDemoMessage(GtisApiRoute.IPKeepRecord, new { record, isDemo = true }.SerializeNewtonJson());
        }

        /// <summary>
        /// 临时账户删除ip备案
        /// </summary>
        /// <param name="delModel">删除需要的模型</param>
        /// <returns></returns>
        public async Task IPKeepRecordDemoDel(BM_GtisOpe_IPKeepRecordDemoDel delModel)
        {
            await GetGtisDemoMessage(GtisApiRoute.IPKeepRecordDel, new { delModel, isDemo = true }.SerializeNewtonJson());
        }

        /// <summary>
        /// 获取临时账号的绑定微信图片
        /// </summary>
        /// <param name="id"></param>
        public async Task<string> GetDemoBindWxImg(string id)
        {
            var ret = await GetGtisDemoMessage(GtisApiRoute.GetDemoBindWxImg, new { id }.SerializeNewtonJson());
            return ret.data;
        }

        /// <summary>
        /// 批量修改用户状态(临时)
        /// </summary>
        /// <param name="listUserState">多个用户状态</param>
        /// <returns></returns>
        public async Task ModifyDemoUserState(List<BM_ModifyUserState> listUserState)
        {
            await GetGtisDemoMessage(GtisApiRoute.ModifyUserState, new { listUserState }.SerializeNewtonJson());
        }


        /// <summary>
        /// 获取一个账户/使用者，按月统计的次数
        /// </summary>
        /// <param name="sysuserid">一个账户的id，账户id和使用者id，二选一</param>
        /// <param name="phoneid">使用者id，账户id和使用者id，二选一</param>
        /// <returns></returns>
        public async Task<List<BM_OneUserOperateLogByMonth>> GetOneUserOpeLogStaByMonth_Demo(string sysuserid, string phoneid = null)
        {
            return await GetGtisDemoMessage<List<BM_OneUserOperateLogByMonth>>(GtisApiRoute.OneUserOpeLogStaByMonth, new { sysuserid, phoneid }.SerializeNewtonJson());
        }

        /// <summary>
        /// 获取操作历史详情
        /// </summary>
        /// <param name="logDetail">日志详情参数</param>
        /// <returns></returns>
        public async Task<ApiTableOut<BM_UserOpeLogDetail>> GetOpeLogDetail_Demo(BM_GtisOpeGetOpeLogDetal logDetail)
        {
            var ret = await GetGtisDemoMessage(GtisApiRoute.OpeLogDetail, new
            {
                sysuserids = logDetail.SysUserID.IsNullOrEmpty() ? null : new List<string> { logDetail.SysUserID },
                phoneids = logDetail.PhoneID.IsNullOrEmpty() ? null : new List<string> { logDetail.PhoneID },
                opeType = logDetail.OpeType,
                year = logDetail.Year,
                months = logDetail.Months,
                isDownload = false,
                pageNum = logDetail.PageNum,
                pageSize = logDetail.PageSize
            }.SerializeNewtonJson());
            return new ApiTableOut<BM_UserOpeLogDetail>()
            {
                Total = ret.count,
                Data = ret.data.DeserializeNewtonJson<List<BM_UserOpeLogDetail>>()
            };
        }

        /// <summary>
        /// 导出操作历史详情
        /// </summary>
        /// <param name="logDetail">日志详情参数</param>
        /// <returns></returns>
        public async Task<Stream> GetOpeLogDetailDownload_Demo(BM_GtisOpeGetOpeLogDetalDownload logDetail)
        {
            var ret = await GetGtisDemoMessage(GtisApiRoute.OpeLogDetail, new
            {
                sysuserids = logDetail.SysUserIDs,
                opeType = logDetail.OpeType,
                year = logDetail.Year,
                months = logDetail.Months,
                isDownload = true
            }.SerializeNewtonJson());
            var list = ret.data.DeserializeNewtonJson<List<BM_UserOpeLogDetail>>();
            //ExcelExporter exp = new ExcelExporter();
            //var expBytes = await exp.ExportAsByteArray(list);            
            ExcelExporterNPOI exp = new ExcelExporterNPOI();
            var expBytes = exp.ExportAsByteArray(list);
            return new MemoryStream(expBytes);
        }

        /// <summary>
        /// 获取导出日志详情
        /// </summary>
        /// <param name="expDetail">导出详细参数</param>
        /// <returns></returns>
        public async Task<ApiTableOut<BM_UserExportLogDetail>> GetExportLogDetail_Demo(BM_GtisOpeGetExportLogDetal expDetail)
        {
            var ret = await GetGtisDemoMessage(GtisApiRoute.ExportLogDetail, new
            {
                sysuserid = expDetail.SysUserID,
                phoneid = expDetail.PhoneID,
                page = expDetail.PageNum,
                pageSize = expDetail.PageSize,
                year = expDetail.Year,
                months = expDetail.Months,
            }.SerializeNewtonJson());
            return new ApiTableOut<BM_UserExportLogDetail>()
            {
                Total = ret.count,
                Data = ret.data.DeserializeNewtonJson<List<BM_UserExportLogDetail>>()
            };
        }

        /// <summary>
        /// 获取操作历史详情
        /// </summary>
        /// <param name="logDetail">日志详情参数</param>
        /// <returns></returns>
        public async Task<ApiTableOut<BM_GtisOpe_DemoInfoStates>> GetDemoUserQuery(BM_GtisOpeDemoInfoStates_In logDetail)
        {
            var ret = await GetGtisDemoMessage(GtisApiRoute.DemoUserQuery, new
            {
                oprids = logDetail.SysUserIDs.IsNullOrEmpty() ? null : logDetail.SysUserIDs.ToArray(),
                year = logDetail.Year,
                months = logDetail.Months,
                pageNum = logDetail.PageNumber,
                pageSize = logDetail.PageSize
            }.SerializeNewtonJson());
            return new ApiTableOut<BM_GtisOpe_DemoInfoStates>()
            {
                Total = ret.count,
                Data = ret.data.DeserializeNewtonJson<List<BM_GtisOpe_DemoInfoStates>>()
            };
        }


        /// <summary>
        /// 获取销售临时账号操作历史详情
        /// </summary>
        /// <param name="logDetail">日志详情参数</param>
        /// <returns></returns>
        public async Task<ApiTableOut<BM_UserOpeLogDetail>> GetDemoQueryOpeLogDetail(BM_GtisOpeGetOpeLogDetal logDetail)
        {
            var ret = await GetGtisDemoMessage(GtisApiRoute.OpeLogDetail_Demo, new
            {
                oprID = logDetail.SysUserID.IsNullOrEmpty() ? null : logDetail.SysUserID ,
                opeType = logDetail.OpeType,
                year = logDetail.Year,
                months = logDetail.Months,
                isDownload = false,
                pageNum = logDetail.PageNum,
                pageSize = logDetail.PageSize
            }.SerializeNewtonJson());
            return new ApiTableOut<BM_UserOpeLogDetail>()
            {
                Total = ret.count,
                Data = ret.data.DeserializeNewtonJson<List<BM_UserOpeLogDetail>>()
            };
        }
        #endregion

        #region 演示、正式账号通用：修改状态、查询日志  (240829 临时和正式区分开  这里没用了)


        #endregion

        #region 其他暂时没用的日志

        ///// <summary>
        ///// 获取一个公司所有使用者的使用统计和状态
        ///// </summary>
        ///// <param name="svcode">一个账户的id</param>
        ///// <returns></returns>
        //public async Task<List<BM_AllPhoneUserOperateLog>> GetAllPhoneUserOpeLogSta(string svcode)
        //{
        //    return await GetGtisMessage<List<BM_AllPhoneUserOperateLog>>(GtisApiRoute.AllPhoneUserOpeLogSta, new { svcode }.SerializeNewtonJson());
        //}


        ///// <summary>
        ///// 获取一个使用者，按月统计的次数
        ///// </summary>
        ///// <param name="sysuser_phone_id">一个使用者的phoneid</param>
        ///// <returns></returns>
        //public async Task<List<BM_OnePhoneUserOperateLogByMonth>> GetOnePhoneUserOpeLogStaByMonth(string sysuser_phone_id)
        //{
        //    return await GetGtisMessage<List<BM_OnePhoneUserOperateLogByMonth>>(GtisApiRoute.OnePhoneUserOpeLogStaByMonth, new { sysuser_phone_id }.SerializeNewtonJson());
        //}

        ///// <summary>
        ///// 获取导出日志详情
        ///// </summary>
        ///// <param name="expDetail">导出详细参数</param>
        ///// <returns></returns>
        //public async Task<Stream> GetExportLogDetailDownload(BM_GtisOpeGetExportLogDetalDownload expDetail)
        //{
        //    var ret = await GetGtisMessage(GtisApiRoute.ExportLogDetail, new
        //    {
        //        sysuserid = expDetail.SysUserID,
        //        phoneid = expDetail.PhoneID,
        //        year = expDetail.Year,
        //        months = expDetail.Months,
        //        isDownload = true
        //    }.SerializeNewtonJson());
        //    var list = ret.data.DeserializeNewtonJson<List<BM_UserExportLogDetail>>();
        //    ExcelExporter exp = new ExcelExporter();
        //    var expBytes = await exp.ExportAsByteArray(list);
        //    return new MemoryStream(expBytes);
        //}

        #endregion

        #region 远程调用相关

        /// <summary>
        /// g5调用路由
        /// </summary>
        enum GtisApiRoute
        {
            /// <summary>
            /// 添加用户路由
            /// </summary>
            [Description("/api/Crm2/AddUser")] AddUser,

            /// <summary>
            /// 添加演示账号路由
            /// </summary>
            [Description("/api/Crm2/AddUserDemo")] AddUserDemo,

            /// <summary>
            /// 添加环球搜路由
            /// </summary>
            [Description("/api/Crm2/SaveHqsCode")] SaveHqsCode,

            /// <summary>
            /// 环球搜续约路由
            /// </summary>
            [Description("/api/Crm2/RenewalHqsCode")]
            RenewalHqsCode,

            /// <summary>
            /// 根据客户编码，删除账号
            /// </summary>
            [Description("/api/Crm2/DelCompany")] DelCompany,

            /// <summary>
            /// 根据客户编码，删除账号
            /// </summary>
            [Description("/api/Crm2/GetCompanyOtherInfo")]
            GetCompanyOtherInfo,

            /// <summary>
            /// 修改用户状态路由
            /// </summary>
            [Description("/api/Crm2/ModifyUserState")]
            ModifyUserState,

            /// <summary>
            /// 查询账号状态，有一个有效，账号就可用
            /// </summary>
            [Description("/api/Crm2/UserState")] UserState,

            /// <summary>
            /// 查询账号状态，有一个有效，账号就可用
            /// </summary>
            [Description("/api/Crm2/GetUserInfos")] UserInfo,

            /// <summary>
            /// 获取开通演示账号剩余个数
            /// </summary>
            [Description("/api/Crm2/DemoLeftOpenTimes")]
            DemoLeftOpenTimes,

            /// <summary>
            /// 批量获取演示账号状态
            /// </summary>
            [Description("/api/Crm2/GetDemoState")]
            DemoState,

            /// <summary>
            /// 获取演示账号信息及状态
            /// </summary>
            [Description("/api/Crm2/GetDemoInfo")] DemoInfo,

            /// <summary>
            /// 一个公司所有账户的操作记录统计
            /// </summary>
            [Description("/api/Crm2/AllUserOperateLogSta")]
            AllUserOperateLogSta,

            /// <summary>
            /// 一个公司所有账户的操作记录统计
            /// </summary>
            [Description("/api/Crm2/AllUserOperateLogSta_Main")]
            AllUserOperateLogSta_Main,
            /// <summary>
            /// 获取一个账户所有使用者的使用统计和状态
            /// </summary>
            [Description("/api/Crm2/AllPhoneUserOpeLogSta")]
            AllPhoneUserOpeLogSta,

            /// <summary>
            /// 获取一个账户，按月统计的次数
            /// </summary>
            [Description("/api/Crm2/OneUserOpeLogStaByMonth")]
            OneUserOpeLogStaByMonth,

            /// <summary>
            /// 获取一个使用者，按月统计的次数
            /// </summary>
            [Description("/api/Crm2/OnePhoneUserOpeLogStaByMonth")]
            OnePhoneUserOpeLogStaByMonth,

            /// <summary>
            /// 获取操作历史详情
            /// </summary>
            [Description("/api/Crm2/OpeLogDetail")]
            OpeLogDetail,

            /// <summary>
            /// 获取导出日志详情
            /// </summary>
            [Description("/api/Crm2/ExportLogDetail")]
            ExportLogDetail,

            /// <summary>
            /// 续约(只是变更的续约)或变更
            /// </summary>
            [Description("/api/Crm2/RenewalContact")]
            RenewalContact,

            /// <summary>
            /// 正式、临时账户ip备案
            /// </summary>
            [Description("/api/Crm2/IPKeepRecord")]
            IPKeepRecord,

            /// <summary>
            /// 正式、临时账户删除ip备案
            /// </summary>
            [Description("/api/Crm2/IPKeepRecordDel")]
            IPKeepRecordDel,

            /// <summary>
            /// 获取gtis所有可用数据源
            /// </summary>
            [Description("/api/Crm2/GetGtisAllSids")]
            GtisAllSids,

            /// <summary>
            /// 获取演示账号绑定微信二维码
            /// </summary>
            [Description("/api/Crm2/GetDemoBindWxImg")]
            GetDemoBindWxImg,

            /// <summary>
            /// 获取团队临时账号统计信息
            /// </summary>
            [Description("/api/Crm2/DemoUserQuery")]
            DemoUserQuery,

            /// <summary>
            /// 获取操作历史详情
            /// </summary>
            [Description("/api/Crm2/OpeLogDetail_Demo")]
            OpeLogDetail_Demo,

            /// <summary>
            /// 获取最新的新增/删除手机号
            /// </summary>
            [Description("/api/Crm2/GetG5Phone_New")]
            GetG5Phone_New,

            /// <summary>
            /// 获取新增/删除手机号列表
            /// </summary>
            [Description("/api/Crm2/GetG5Phone_List")]
            GetG5Phone_List,

            /// <summary>
            /// 移除用户数据源
            /// </summary>
            [Description("/api/Crm2/RemoveUserSid")]
            RemoveUserSid,
            /// <summary>
            /// 查询账号的使用者
            /// </summary>
            [Description("/api/Crm2/GetAccountUsersInfo")] GetAccountUsersInfo,
            /// <summary>
            /// 获取租户id
            /// </summary>
            [Description("/api/Crm2/GetAccountTenantId")]
            GetAccountTenantId,
        }

        /// <summary>
        /// 携带身份
        /// </summary>
        Dictionary<string, string> AuthorHeader => new Dictionary<string, string> { { "Authorization", AppSettings.GtisApi.Authorization } };

        /// <summary>
        /// 发送给gtis消息
        /// </summary>
        /// <param name="route">业务路由</param>
        /// <param name="body">请求的参数</param>
        /// <returns></returns>
        async Task<BM_GtisOpe_RetModel> GetGtisMessage(GtisApiRoute route, string body)
        {
            BM_GtisOpe_RetModel ret = await NetUtil.PostAsync_ReturnModel<BM_GtisOpe_RetModel>(AppSettings.GtisApi.Url + route.GetEnumDescription(), body, AuthorHeader);
            if (ret.code != 20000)
                throw new ApiException(ret.msg);
            return ret;
        }

        /// <summary>
        /// 发送给gtis消息(备份库)
        /// </summary>
        /// <param name="route">业务路由</param>
        /// <param name="body">请求的参数</param>
        /// <returns></returns>
        async Task<BM_GtisOpe_RetModel> GetGtisBackUpMessage(GtisApiRoute route, string body)
        {
            BM_GtisOpe_RetModel ret = await NetUtil.PostAsync_ReturnModel<BM_GtisOpe_RetModel>(AppSettings.GtisApi.UrlBackUp + route.GetEnumDescription(), body, AuthorHeader);
            if (ret.code != 20000)
                throw new ApiException(ret.msg);
            return ret;
        }
        /// <summary>
        /// 发送给gtis消息(临时账号库)
        /// </summary>
        /// <param name="route">业务路由</param>
        /// <param name="body">请求的参数</param>
        /// <returns></returns>
        async Task<BM_GtisOpe_RetModel> GetGtisDemoMessage(GtisApiRoute route, string body)
        {
            BM_GtisOpe_RetModel ret = await NetUtil.PostAsync_ReturnModel<BM_GtisOpe_RetModel>(AppSettings.GtisApi.UrlDemo + route.GetEnumDescription(), body, AuthorHeader);
            if (ret.code != 20000)
                throw new ApiException(ret.msg);
            return ret;
        }

        /// <summary>
        /// 发送给gtis消息
        /// </summary>
        /// <typeparam name="T">data被反序列化的内容</typeparam>
        /// <param name="route">业务路由</param>
        /// <param name="body">请求的参数</param>
        /// <returns></returns>
        async Task<T> GetGtisMessage<T>(GtisApiRoute route, string body)
        {
            var ret = await GetGtisMessage(route, body);
            return ret.data.DeserializeNewtonJson<T>();
        }

        /// <summary>
        /// 发送给gtis消息(备份库)
        /// </summary>
        /// <typeparam name="T">data被反序列化的内容</typeparam>
        /// <param name="route">业务路由</param>
        /// <param name="body">请求的参数</param>
        /// <returns></returns>
        async Task<T> GetGtisBackUpMessage<T>(GtisApiRoute route, string body)
        {
            var ret = await GetGtisBackUpMessage(route, body);
            return ret.data.DeserializeNewtonJson<T>();
        }

        /// <summary>
        /// 发送给gtis消息(临时账号库)
        /// </summary>
        /// <typeparam name="T">data被反序列化的内容</typeparam>
        /// <param name="route">业务路由</param>
        /// <param name="body">请求的参数</param>
        /// <returns></returns>
        async Task<T> GetGtisDemoMessage<T>(GtisApiRoute route, string body)
        {
            var ret = await GetGtisDemoMessage(route, body);
            return ret.data.DeserializeNewtonJson<T>();
        }
        #endregion 远程调用路由
    }
}