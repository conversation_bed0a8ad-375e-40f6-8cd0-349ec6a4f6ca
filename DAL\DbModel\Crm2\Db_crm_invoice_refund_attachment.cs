using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    /// <summary>
    /// 发票退票附件表
    /// </summary>
    [SugarTable("crm_invoice_refund_attachment")]
    public class Db_crm_invoice_refund_attachment
    {
        /// <summary>
        /// Desc:主键ID
        /// Default:
        /// Nullable:False
        /// </summary>           
        [SugarColumn(IsPrimaryKey=true)]
        public string Id {get;set;}

        /// <summary>
        /// Desc:退票申请ID
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string RefundApplicationId {get;set;}

        /// <summary>
        /// Desc:文件名
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string FileName {get;set;}

        /// <summary>
        /// Desc:文件路径
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string FilePath {get;set;}

        /// <summary>
        /// Desc:文件扩展名
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string FileExtension {get;set;}

        /// <summary>
        /// Desc:文件大小(字节)
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? FileSize {get;set;}

        /// <summary>
        /// Desc:附件类型(1:申请附件 2:审核凭证)
        /// Default:1
        /// Nullable:True
        /// </summary>           
        public int? AttachmentType {get;set;}

        /// <summary>
        /// Desc:删除标记
        /// Default:0
        /// Nullable:True
        /// </summary>           
        public bool? Deleted {get;set;}

        /// <summary>
        /// Desc:创建人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CreateUser {get;set;}

        /// <summary>
        /// Desc:创建时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? CreateDate {get;set;}

        /// <summary>
        /// Desc:修改人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string UpdateUser {get;set;}

        /// <summary>
        /// Desc:更新时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? UpdateDate {get;set;}
    }
} 