﻿using System.ComponentModel.DataAnnotations;
using CRM2_API.BLL.Common;
using CRM2_API.Common.Filter;
using CRM2_API.Common.JWT;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.ControllersViewModel.Schedule;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SqlSugar;
using CRM2_API.Model.BLLModel.Enum;
using static CRM2_API.Model.ControllersViewModel.VM_Contract;
using static CRM2_API.Model.ControllersViewModel.VM_ContractInvoice;
using static CRM2_API.Model.ControllersViewModel.VM_Coupon;
using static CRM2_API.Model.ControllersViewModel.VM_PrivateService;
using CRM2_API.Model.BLLModel.ServiceOpening;

namespace CRM2_API.Model.ControllersViewModel
{
    public class VM_Contract
    {
        public class DownPaymentInfo_In
        {
            //转移BLL [Required(ErrorMessage = "是否分期不可为空")]
            public int? IsStage { get; set; }

            //转移BLL [Required(ErrorMessage = "是否到账不可为空")]
            public int? IsReceipt { get; set; }

            //转移BLL [Required(ErrorMessage = "款项类型不可为空")]
            public int? PaymentType { get; set; }

            //[Required(ErrorMessage = "计划到账日期不可为空")]
            public DateTime? PlannedArrivalDate { get; set; }

            //[Required(ErrorMessage = "预计到账金额不可为空")]
            public decimal? PlannedArrivalAmount { get; set; }

            //转移BLL [Required(ErrorMessage = "支付公司不可为空")]
            public Guid? PaymentCompany { get; set; }

            //转移BLL [Required(ErrorMessage = "收款公司不可为空")]
            public Guid? CollectingCompany { get; set; }

            //转移BLL [Required(ErrorMessage = "币种不可为空")]
            public int? Currency { get; set; }

            //[Required(ErrorMessage = "到账金额不可为空")]
            public decimal? ArrivalAmount { get; set; }

            //[Required(ErrorMessage = "到账日期不可为空")]
            public DateTime? ArrivalDate { get; set; }

            //转移BLL [Required(ErrorMessage = "支付方式不可为空")]
            public int? PaymentMethod { get; set; }

            //[Required(ErrorMessage = "是否分期不可为空")]
            //public int IsStage { get; set; } 作废

            //[Required(ErrorMessage = "银行支付金额不可为空")]
            public decimal? BankPaymentAmount { get; set; }

            //[Required(ErrorMessage = "现金支付金额不可为空")]
            public decimal? CashPaymentAmount { get; set; }

            //public IFormFileCollection PaymentVoucher { get; set; }

            //[Required(ErrorMessage = "现金来源备注不可为空")]
            public string CashSourceRemarks { get; set; }

            //[Required(ErrorMessage = "是否代付款不可为空")]
            public bool? IsBehalfPayment { get; set; }

            public string BehalfPaymentName { get; set; }

            /// <summary>
            /// 联系人
            /// </summary>           
            public string PaymentContacts { get; set; }

            /// <summary>
            /// 职务
            /// </summary>           
            public string PaymentJob { get; set; }

            /// <summary>
            /// 联系方式
            /// </summary>           
            public string PaymentContactWay { get; set; }

            /// <summary>
            /// 电子邮箱
            /// </summary>           
            public string PaymentEmail { get; set; }

            /// <summary>
            /// 固定电话
            /// </summary>           
            public string PaymentTelephone { get; set; }

            /// <summary>
            /// 传真号码
            /// </summary>           
            public string PaymentFax { get; set; }

            /// <summary>
            /// 邮编
            /// </summary>           
            public string PaymentPostalCode { get; set; }

            /// <summary>
            /// 详细地址-国家
            /// </summary>           
            public int? PaymentCountry { get; set; }

            /// <summary>
            /// 详细地址-省
            /// </summary>           
            public int? PaymentProvince { get; set; }

            /// <summary>
            /// 详细地址-市
            /// </summary>           
            public int? PaymentCity { get; set; }

            /// <summary>
            /// 详细地址-详细地址
            /// </summary>           
            public string PaymentAddress { get; set; }
        }

        public class DownContractBase_In
        {
            //转移BLL [Required(ErrorMessage = "客户表id不可为空")]
            [Required(ErrorMessage = "客户表id不可为空")]
            public Guid CustomerId { get; set; }

            //[Required(ErrorMessage = "客户编码（合同编码）不可为空")]
            //public string ContractNum { get; set; }

            //转移BLL [Required(ErrorMessage = "合同类型不可为空")]
            [Required(ErrorMessage = "合同类型不可为空")]
            public int? ContractMethod { get; set; }

            //[Required(ErrorMessage = "合同号不可为空")]  电子合同后台生成
            public string ContractNo { get; set; }

            //[Required(ErrorMessage = "纸质合同id不可为空")] 电子合同后台生成
            public string ContractPaperEntityId { get; set; }

            //[Required(ErrorMessage = "电子合同id不可为空")] 电子合同后台生成
            public string ContractElectronicEntityId { get; set; }

            //转移BLL [Required(ErrorMessage = "合同名称不可为空")]
            [Required(ErrorMessage = "合同名称不可为空")]
            public string ContractName { get; set; }

            //[Required(ErrorMessage = "出单人不可为空")]
            public Guid Issuer { get; set; }

            //转移BLL [Required(ErrorMessage = "签约日期不可为空")]
            [Required(ErrorMessage = "签约日期不可为空")]
            public DateTime? SigningDate { get; set; }

            //转移BLL [Required(ErrorMessage = "甲方公司不可为空")]
            [Required(ErrorMessage = "甲方公司不可为空")]
            public Guid FirstParty { get; set; }

            //转移BLL [Required(ErrorMessage = "乙方公司不可为空")]
            [Required(ErrorMessage = "乙方公司不可为空")]
            public Guid? SecondParty { get; set; }

            //转移BLL [Required(ErrorMessage = "合同金额不可为空")]
            //[Required(ErrorMessage = "合同金额不可为空")]
            public decimal? ContractAmount { get; set; }

            //转移BLL [Required(ErrorMessage = "外币合同金额不可为空")]
            //[Required(ErrorMessage = "外币合同金额不可为空")]
            public decimal? FCContractAmount { get; set; }

            //转移BLL [Required(ErrorMessage = "币种不可为空")]
            [Required(ErrorMessage = "币种不可为空")]
            public int? Currency { get; set; }

            //转移BLL [Required(ErrorMessage = "签约类型不可为空")]
            [Required(ErrorMessage = "签约类型不可为空")]
            public int? ContractType { get; set; }

            //转移BLL [Required(ErrorMessage = "联系信息不可为空")]
            [Required(ErrorMessage = "联系信息不可为空")]
            public int? ContactInformation { get; set; }

            //转移BLL [Required(ErrorMessage = "联系人不可为空")]
            [Required(ErrorMessage = "联系人不可为空")]
            public string Contacts { get; set; }

            //转移BLL [Required(ErrorMessage = "职务不可为空")]
            [Required(ErrorMessage = "职务不可为空")]
            public string Job { get; set; }

            //转移BLL [Required(ErrorMessage = "联系方式不可为空")]
            [Required(ErrorMessage = "联系方式不可为空")]
            public string ContactWay { get; set; }

            //转移BLL [Required(ErrorMessage = "电子邮箱不可为空")]
            [Required(ErrorMessage = "电子邮箱不可为空")]
            public string Email { get; set; }

            //转移BLL [Required(ErrorMessage = "固定电话不可为空")]
            //[Required(ErrorMessage = "固定电话不可为空")]
            public string Telephone { get; set; }

            //转移BLL [Required(ErrorMessage = "传真号码不可为空")]
            //[Required(ErrorMessage = "传真号码不可为空")]
            public string Fax { get; set; }

            //转移BLL [Required(ErrorMessage = "邮编不可为空")]
            //[Required(ErrorMessage = "邮编不可为空")]
            public string PostalCode { get; set; }

            //转移BLL [Required(ErrorMessage = "详细地址-国家不可为空")]
            [Required(ErrorMessage = "详细地址-国家不可为空")]
            public int? Country { get; set; }

            //[Required(ErrorMessage = "详细地址-省不可为空")]
            public int? Province { get; set; }

            //[Required(ErrorMessage = "详细地址-市不可为空")]
            public int? City { get; set; }

            //转移BLL [Required(ErrorMessage = "详细地址-详细地址不可为空")]
            [Required(ErrorMessage = "详细地址-详细地址不可为空")]
            public string Address { get; set; }

            ////[Required(ErrorMessage = "未盖章合同不可为空")]
            //public IFormFileCollection ContractFile { get; set; }

            [Required(ErrorMessage = "是否自定义合同说明")]
            public int IsCustomContractDescription { get; set; }

            //[Required(ErrorMessage = "合同说明不可为空")]
            public string ContractDescription { get; set; }

            //[Required(ErrorMessage = "合同说明模板Id不可为空")]
            public string ContractTemplateId { get; set; }

            [Required(ErrorMessage = "合同状态不可为空")]
            public EnumContractStatus ContractStatus { get; set; }

            //转移BLL [Required(ErrorMessage = "是否特殊不可为空")]
            [Required(ErrorMessage = "是否特殊不可为空")]
            public int? IsSpecial { get; set; }

            //[Required(ErrorMessage = "特殊说明不可为空")]
            public string SpecialDescription { get; set; }

            //public IFormFileCollection ContractSpecialAttachFile { get; set; }

            [Required(ErrorMessage = "是否境外客户不可为空")]
            public bool IsOverseasCustomer { get; set; }

            [Required(ErrorMessage = "是否申请超级子账号不可为空")]
            public bool IsApplySuperSubAccount { get; set; }

            [Required(ErrorMessage = "销售国家不可为空")]
            public int SalesCountry { get; set; }

            public string Remark { get; set; }

            public string SigningAgentName { get; set; }

            public string SigningAgentPhone { get; set; }

            public string SigningAgentEmail { get; set; }

            public string SigningAgentFax { get; set; }

            public string CompanyAddressId { get; set; }

            //[Required(ErrorMessage = "续约客户编码不可为空")]
            public string RenewalContractNum { get; set; }

            /// <summary>
            /// 关系说明
            /// </summary>
            //[Required(ErrorMessage = "关系说明不可为空")]
            public string RelationshipDescription { get; set; }

            /// <summary>
            /// 关系说明文件
            /// </summary>
            //[Required(ErrorMessage = "关系说明文件不可为空")]
            //public IFormFileCollection RelationshipFileInfo { get; set; }
        }


        public class Contract_In
        {
            //转移BLL [Required(ErrorMessage = "客户表id不可为空")]
            [Required(ErrorMessage = "客户表id不可为空")]
            public Guid CustomerId { get; set; }

            //[Required(ErrorMessage = "客户编码（合同编码）不可为空")]
            //public string ContractNum { get; set; }

            //转移BLL [Required(ErrorMessage = "合同类型不可为空")]
            [Required(ErrorMessage = "合同类型不可为空")]
            public int? ContractMethod { get; set; }

            //[Required(ErrorMessage = "合同号不可为空")]  电子合同后台生成
            public string ContractNo { get; set; }

            //[Required(ErrorMessage = "纸质合同id不可为空")] 电子合同后台生成
            public string ContractPaperEntityId { get; set; }

            //[Required(ErrorMessage = "电子合同id不可为空")] 电子合同后台生成
            public string ContractElectronicEntityId { get; set; }

            //转移BLL [Required(ErrorMessage = "合同名称不可为空")]
            [Required(ErrorMessage = "合同名称不可为空")]
            public string ContractName { get; set; }

            //[Required(ErrorMessage = "出单人不可为空")]
            public Guid Issuer { get; set; }

            //转移BLL [Required(ErrorMessage = "签约日期不可为空")]
            [Required(ErrorMessage = "签约日期不可为空")]
            public DateTime? SigningDate { get; set; }

            //转移BLL [Required(ErrorMessage = "甲方公司不可为空")]
            [Required(ErrorMessage = "甲方公司不可为空")]
            public Guid FirstParty { get; set; }

            //转移BLL [Required(ErrorMessage = "乙方公司不可为空")]
            [Required(ErrorMessage = "乙方公司不可为空")]
            public Guid? SecondParty { get; set; }

            //转移BLL [Required(ErrorMessage = "合同金额不可为空")]
            //[Required(ErrorMessage = "合同金额不可为空")]
            public decimal? ContractAmount { get; set; }

            //转移BLL [Required(ErrorMessage = "外币合同金额不可为空")]
            //[Required(ErrorMessage = "外币合同金额不可为空")]
            public decimal? FCContractAmount { get; set; }

            //转移BLL [Required(ErrorMessage = "币种不可为空")]
            [Required(ErrorMessage = "币种不可为空")]
            public int? Currency { get; set; }

            //转移BLL [Required(ErrorMessage = "签约类型不可为空")]
            [Required(ErrorMessage = "签约类型不可为空")]
            public int? ContractType { get; set; }

            //转移BLL [Required(ErrorMessage = "联系信息不可为空")]
            [Required(ErrorMessage = "联系信息不可为空")]
            public int? ContactInformation { get; set; }

            //转移BLL [Required(ErrorMessage = "联系人不可为空")]
            [Required(ErrorMessage = "联系人不可为空")]
            public string Contacts { get; set; }

            //转移BLL [Required(ErrorMessage = "职务不可为空")]
            [Required(ErrorMessage = "职务不可为空")]
            public string Job { get; set; }

            //转移BLL [Required(ErrorMessage = "联系方式不可为空")]
            [Required(ErrorMessage = "联系方式不可为空")]
            public string ContactWay { get; set; }

            //转移BLL [Required(ErrorMessage = "电子邮箱不可为空")]
            [Required(ErrorMessage = "电子邮箱不可为空")]
            public string Email { get; set; }

            //转移BLL [Required(ErrorMessage = "固定电话不可为空")]
            //[Required(ErrorMessage = "固定电话不可为空")]
            public string Telephone { get; set; }

            //转移BLL [Required(ErrorMessage = "传真号码不可为空")]
            //[Required(ErrorMessage = "传真号码不可为空")]
            public string Fax { get; set; }

            //转移BLL [Required(ErrorMessage = "邮编不可为空")]
            //[Required(ErrorMessage = "邮编不可为空")]
            public string PostalCode { get; set; }

            //转移BLL [Required(ErrorMessage = "详细地址-国家不可为空")]
            [Required(ErrorMessage = "详细地址-国家不可为空")]
            public int? Country { get; set; }

            //[Required(ErrorMessage = "详细地址-省不可为空")]
            public int? Province { get; set; }

            //[Required(ErrorMessage = "详细地址-市不可为空")]
            public int? City { get; set; }

            //转移BLL [Required(ErrorMessage = "详细地址-详细地址不可为空")]
            [Required(ErrorMessage = "详细地址-详细地址不可为空")]
            public string Address { get; set; }

            //[Required(ErrorMessage = "未盖章合同不可为空")]
            public IFormFileCollection ContractFile { get; set; }

            [Required(ErrorMessage = "是否自定义合同说明")]
            public int IsCustomContractDescription { get; set; }

            //[Required(ErrorMessage = "合同说明不可为空")]
            public string ContractDescription { get; set; }

            //[Required(ErrorMessage = "合同说明模板Id不可为空")]
            public string ContractTemplateId { get; set; }

            [Required(ErrorMessage = "合同状态不可为空")]
            public EnumContractStatus ContractStatus { get; set; }

            //转移BLL [Required(ErrorMessage = "是否特殊不可为空")]
            [Required(ErrorMessage = "是否特殊不可为空")]
            public int? IsSpecial { get; set; }

            //[Required(ErrorMessage = "特殊说明不可为空")]
            public string SpecialDescription { get; set; }

            public IFormFileCollection ContractSpecialAttachFile { get; set; }

            [Required(ErrorMessage = "是否境外客户不可为空")]
            public bool IsOverseasCustomer { get; set; }

            [Required(ErrorMessage = "是否申请超级子账号不可为空")]
            public bool IsApplySuperSubAccount { get; set; }

            [Required(ErrorMessage = "销售国家不可为空")]
            public int SalesCountry { get; set; }

            public string Remark { get; set; }

            public string SigningAgentName { get; set; }

            public string SigningAgentPhone { get; set; }

            public string SigningAgentEmail { get; set; }

            public string SigningAgentFax { get; set; }

            public string CompanyAddressId { get; set; }

            //[Required(ErrorMessage = "续约客户编码不可为空")]
            public string RenewalContractNum { get; set; }

            /// <summary>
            /// 关系说明
            /// </summary>
            //[Required(ErrorMessage = "关系说明不可为空")]
            public string RelationshipDescription { get; set; }

            /// <summary>
            /// 关系说明文件
            /// </summary>
            //[Required(ErrorMessage = "关系说明文件不可为空")]
            public IFormFileCollection RelationshipFileInfo { get; set; }

            [Required(ErrorMessage = "是否合并合同")]
            public bool IsMerged { get; set; }

            public string? ParentContractId { get; set; }
        }

        public class PaymentInfo_In
        {
            //转移BLL [Required(ErrorMessage = "是否分期不可为空")]
            public int? IsStage { get; set; }

            //转移BLL [Required(ErrorMessage = "是否到账不可为空")]
            public int? IsReceipt { get; set; }

            //转移BLL [Required(ErrorMessage = "款项类型不可为空")]
            public int? PaymentType { get; set; }

            //[Required(ErrorMessage = "计划到账日期不可为空")]
            public DateTime? PlannedArrivalDate { get; set; }

            //[Required(ErrorMessage = "预计到账金额不可为空")]
            public decimal? PlannedArrivalAmount { get; set; }

            //转移BLL [Required(ErrorMessage = "支付公司不可为空")]
            public Guid? PaymentCompany { get; set; }

            //转移BLL [Required(ErrorMessage = "收款公司不可为空")]
            public Guid? CollectingCompany { get; set; }

            //转移BLL [Required(ErrorMessage = "币种不可为空")]
            public int? Currency { get; set; }

            //[Required(ErrorMessage = "到账金额不可为空")]
            public decimal? ArrivalAmount { get; set; }

            //[Required(ErrorMessage = "到账日期不可为空")]
            public DateTime? ArrivalDate { get; set; }

            //转移BLL [Required(ErrorMessage = "支付方式不可为空")]
            public int? PaymentMethod { get; set; }

            //[Required(ErrorMessage = "是否分期不可为空")]
            //public int IsStage { get; set; } 作废

            //[Required(ErrorMessage = "银行支付金额不可为空")]
            public decimal? BankPaymentAmount { get; set; }

            //[Required(ErrorMessage = "现金支付金额不可为空")]
            public decimal? CashPaymentAmount { get; set; }

            public IFormFileCollection PaymentVoucher { get; set; }

            //[Required(ErrorMessage = "现金来源备注不可为空")]
            public string CashSourceRemarks { get; set; }

            //[Required(ErrorMessage = "是否代付款不可为空")]
            public bool? IsBehalfPayment { get; set; }

            public string BehalfPaymentName { get; set; }

            /// <summary>
            /// 联系人
            /// </summary>           
            public string PaymentContacts { get; set; }

            /// <summary>
            /// 职务
            /// </summary>           
            public string PaymentJob { get; set; }

            /// <summary>
            /// 联系方式
            /// </summary>           
            public string PaymentContactWay { get; set; }

            /// <summary>
            /// 电子邮箱
            /// </summary>           
            public string PaymentEmail { get; set; }

            /// <summary>
            /// 固定电话
            /// </summary>           
            public string PaymentTelephone { get; set; }

            /// <summary>
            /// 传真号码
            /// </summary>           
            public string PaymentFax { get; set; }

            /// <summary>
            /// 邮编
            /// </summary>           
            public string PaymentPostalCode { get; set; }

            /// <summary>
            /// 详细地址-国家
            /// </summary>           
            public int? PaymentCountry { get; set; }

            /// <summary>
            /// 详细地址-省
            /// </summary>           
            public int? PaymentProvince { get; set; }

            /// <summary>
            /// 详细地址-市
            /// </summary>           
            public int? PaymentCity { get; set; }

            /// <summary>
            /// 详细地址-详细地址
            /// </summary>           
            public string PaymentAddress { get; set; }
        }

        [ModelBinder(BinderType = typeof(MetadataValueModelBinder))]
        public class ProductInfo_DownloadStyle_In
        {
            [Required(ErrorMessage = "产品表id不可为空")]
            public Guid ProductId { get; set; }

            [Required(ErrorMessage = "产品类型不可为空")]
            public EnumProductType ProductType { get; set; }

            [Required(ErrorMessage = "产品价格不可为空")]
            public decimal ContractProductinfoPrice { get; set; }

            [Required(ErrorMessage = "产品价格不可为空")]
            public decimal ContractProductinfoPriceTotal { get; set; }

            [Required(ErrorMessage = "产品表(组合产品的上级节点)id不可为空")]
            public Guid ParentProductId { get; set; }

            public decimal ParentContractProductinfoPrice { get; set; }

            public decimal ParentContractProductinfoPriceTotal { get; set; }

            public Guid? ParentId { get; set; }

            public string? ProductNum { get; set; }
        }

        [ModelBinder(BinderType = typeof(MetadataValueModelBinder))]
        public class ProductInfo_In
        {
            [Required(ErrorMessage = "产品表id不可为空")]
            public Guid ProductId { get; set; }

            [Required(ErrorMessage = "产品类型不可为空")]
            public EnumProductType ProductType { get; set; }

            //[Required(ErrorMessage = "产品价格Id不可为空")]
            public Guid? ProductPriceId { get; set; } = Guid.Empty;

            [Required(ErrorMessage = "产品价格不可为空")]
            public decimal ContractProductinfoPrice { get; set; }

            public Guid? SubAccountsProductPriceId { get; set; } = Guid.Empty;

            //[Required(ErrorMessage = "产品价格不可为空")]
            public decimal? SubAccountsProductPrice { get; set; } = 0;

            //[Required(ErrorMessage = "产品价格不可为空")]
            public decimal ContractProductinfoPriceTotal { get; set; } = 0;

            public decimal ContractProductinfoPriceMinimum { get; set; }

            public decimal ContractProductinfoPriceMinimumTotal { get; set; }

            //[Required(ErrorMessage = "开通月数不可为空")]
            public int? OpeningMonths { get; set; }

            //[Required(ErrorMessage = "首次开通月数不可为空")]
            public int? FirstOpeningMonths { get; set; }

            //[Required(ErrorMessage = "开通年数不可为空")]
            public int? OpeningYears { get; set; }

            //[Required(ErrorMessage = "国家不可为空")]
            public string Countrys { get; set; }

            //[Required(ErrorMessage = "主账号个数不可为空")]
            public int? PrimaryAccountsNum { get; set; }

            //[Required(ErrorMessage = "子账号个数不可为空")]
            public int? SubAccountsNum { get; set; }

            //[Required(ErrorMessage = "编码个数不可为空")]
            public int? CodesNum { get; set; }

            //[Required(ErrorMessage = "套数不可为空")]
            public int? SetNum { get; set; }

            //[Required(ErrorMessage = "期数不可为空")]
            public int? PeriodsNum { get; set; }

            //[Required(ErrorMessage = "子账号授权国家次数不可为空")]
            public int? AuthorizationNum { get; set; }

            [Required(ErrorMessage = "产品表(组合产品的上级节点)id不可为空")]
            public Guid ParentProductId { get; set; }

            public Guid? ParentProductPriceId { get; set; }

            public decimal ParentContractProductinfoPrice { get; set; }

            public decimal ParentContractProductinfoPriceTotal { get; set; }

            public Guid? ParentId { get; set; }

            public string? ProductNum { get; set; }

            public int? PriceInfo { get; set; }
        }

        [ModelBinder(BinderType = typeof(MetadataValueModelBinder))]
        public class ContractTemplate_In
        {
            [Required(ErrorMessage = "合同说明Id不可为空")]
            public Guid ContractTemplateId { get; set; }

            [Required(ErrorMessage = "合同说明不可为空")]
            public string ContractDescription { get; set; }
        }

        [ModelBinder(BinderType = typeof(MetadataValueModelBinder))]
        public class ContractTermTemplate_In : AddContractTermTemplate
        {
            public string Term_Template_Id { get; set; }
        }

        [ModelBinder(BinderType = typeof(MetadataValueModelBinder))]
        public class AddContractTermTemplate_In : ContractTermTemplate_In
        {
        }

        [ModelBinder(BinderType = typeof(MetadataValueModelBinder))]
        public class UpdateContractTermTemplate_In : ContractTermTemplate_In
        {
            /// <summary>
            /// 主键
            /// </summary>
            //[Required(ErrorMessage = "合同说明主键不可为空")]
            public string Id { get; set; }

        }

        public class ContractTermTemplateList
        {
            public string Id { get; set; }

            public string Term_Template_Id { get; set; }

            public string Term_Contract_Id { get; set; }

            public int? Sort_Index { get; set; }

            public int? Term_Status { get; set; }

            public int? Term_Template_Status { get; set; }

            public string Content { get; set; }

            /// <summary>
            /// 层级：公司、个人
            /// </summary>           
            public int? Term_Template_Level { get; set; }

            /// <summary>
            /// 适用合同年限：1年合同、2年合同、3年合同、不限
            /// </summary>           
            public int? Applicable_Years { get; set; }

            /// <summary>
            /// 适用大陆/非大陆地区客户，可以不限
            /// </summary>           
            public int? Applicable_Areas { get; set; }

            /// <summary>
            /// 指定客户：只能用于某个客户的条款，可以不限
            /// </summary>           
            public string Specified_Customers { get; set; }

            /// <summary>
            /// 审核流程：自动审核、总部客服、营销+总部
            /// </summary>           
            public int? Audit_Flow_Type { get; set; }

            /// <summary>
            /// 使用周期：一次性/永久（永久也有起始时间）
            /// </summary>           
            public bool? One_Time { get; set; }

            /// <summary>
            /// 模板来源合同：可以为空，空表示是单独建立的合同条款模板，否则这里存储的是这条模板建立时所关联的合同
            /// </summary>           
            public string Source_Contract_Id { get; set; }

            /// <summary>
            /// 营销审核：无需审核，待审核，拒绝，通过
            /// </summary>           
            public int? Marketing_Audit_Status { get; set; }

            /// <summary>
            /// 北京审核：无需审核，待审核，拒绝，通过
            /// </summary>           
            public int? Beijing_Audit_Status { get; set; }

            /// <summary>
            ///审核进程：待确认，中队，大队，战队，北京初审，北京复核
            /// </summary>
            public int? Term_Audit_Process { get; set; }

            /// <summary>
            /// 审核组织类型 中队，大队，战队,空表示无需团队审核
            /// </summary>
            public int? Term_Audit_OrgType { get; set; }

            /// <summary>
            /// 关联的最后一条合同条款模板审核记录(Crm_ContractTerms_Audits)
            /// </summary>           
            public string Last_Audit_Id { get; set; }

            /// <summary>
            /// 适用产品规则描述
            /// </summary>           
            public string ProductsRuleDescription { get; set; }

            /// <summary>
            /// Group的排序序号
            /// </summary>           
            public int? Group_Sort_Index { get; set; }

            /// <summary>
            /// 备注
            /// </summary>           
            public string Remark { get; set; }

            /// <summary>
            /// 开始时间
            /// </summary>           
            public DateTime? StartDate { get; set; }

            /// <summary>
            /// 结束时间
            /// </summary>           
            public DateTime? EndDate { get; set; }

            /// <summary>
            /// 审核人
            /// </summary>           
            public string AuditUser { get; set; }

            /// <summary>
            /// 审核时间
            /// </summary>           
            public DateTime? AuditDate { get; set; }

        }

        public class AddContract_In : Contract_In
        {
            //转移BLL [Required(ErrorMessage = "支付信息不可为空")]
            public AddPaymentInfo_In PaymentInfo { get; set; }

            //转移BLL [Required(ErrorMessage = "产品信息不可为空")]
            public List<AddProductInfo_In> ProductInfo { get; set; }

            //转移BLL [Required(ErrorMessage = "合同说明不可为空")]
            public List<AddContractTemplate_In> ContractTemplate { get; set; }

            public List<AddContractTermTemplate_In> ContractTermTemplate { get; set; }

            public List<ContractModifyContractTemplateAddRow_In> ModifyContractTemplateAddRow { get; set; }

            public List<AddProductInfo_DownloadStyle_In> ProductInfoDownloadStyle { get; set; }
        }

        public class DownContract_In : DownContractBase_In
        {
            //转移BLL [Required(ErrorMessage = "支付信息不可为空")]
            public DownPaymentInfo_In PaymentInfo { get; set; }

            //转移BLL [Required(ErrorMessage = "产品信息不可为空")]
            public List<AddProductInfo_In> ProductInfo { get; set; }

            //转移BLL [Required(ErrorMessage = "合同说明不可为空")]
            public List<AddContractTemplate_In> ContractTemplate { get; set; }

            public List<AddContractTermTemplate_In> ContractTermTemplate { get; set; }

            public List<ContractModifyContractTemplateAddRow_In> ModifyContractTemplateAddRow { get; set; }

            public List<AddProductInfo_DownloadStyle_In> ProductInfoDownloadStyle { get; set; }

            public ContractIsstage_Out ContractIsstage { get; set; }

            public string? parentContractId { get; set; } = null;
        }

        public class CopyContract_In : Contract_In
        {
            public string HistoryContractId { get; set; }

            public CopyPaymentInfo_In PaymentInfo { get; set; }

            public List<AddProductInfo_In> ProductInfo { get; set; }

            public List<AddContractTemplate_In> ContractTemplate { get; set; }

            public List<AddContractTermTemplate_In> ContractTermTemplate { get; set; }

            public List<BM_FileInfo> ContractFileInfo { get; set; }

            public List<BM_FileInfo> ContractSpecialAttachFileInfo { get; set; }

            public List<BM_FileInfo> RelationshipFileInfoInfo { get; set; }

            public List<ContractModifyContractTemplateAddRow_In> ModifyContractTemplateAddRow { get; set; }

            public List<AddProductInfo_DownloadStyle_In> ProductInfoDownloadStyle { get; set; }
        }

        public class AddPaymentInfo_In : PaymentInfo_In
        { }

        [ModelBinder(BinderType = typeof(MetadataValueModelBinder))]
        public class AddProductInfo_DownloadStyle_In : ProductInfo_DownloadStyle_In
        {
            public List<AddProductInfo_DownloadStyle_In> ChildNode { get; set; }
        }

        [ModelBinder(BinderType = typeof(MetadataValueModelBinder))]
        public class AddProductInfo_In : ProductInfo_In, IContractTermProductInfo
        {
            public List<AddProductInfo_In> ChildNode { get; set; }
        }

        public class AddProductInfoOrder_In : AddProductInfo_In
        {
            public string Id { get; set; }
        }

        [ModelBinder(BinderType = typeof(MetadataValueModelBinder))]
        public class AddContractTemplate_In : ContractTemplate_In
        { }

        public class UpdateContract_In : Contract_In
        {
            /// <summary>
            /// 主键
            /// </summary>
            [Required(ErrorMessage = "合同主键不可为空")]
            public string Id { get; set; }

            //转移BLL [Required(ErrorMessage = "支付信息不可为空")]
            public UpdatePaymentInfo_In PaymentInfo { get; set; }

            //转移BLL [Required(ErrorMessage = "产品信息不可为空")]
            public List<UpdateProductInfo_In> ProductInfo { get; set; }

            //转移BLL [Required(ErrorMessage = "合同说明不可为空")]
            public List<UpdateContractTemplate_In> ContractTemplate { get; set; }

            public List<UpdateContractTermTemplate_In> ContractTermTemplate { get; set; }

            public List<BM_FileInfo> ContractFileInfo { get; set; }

            public List<BM_FileInfo> ContractSpecialAttachFileInfo { get; set; }

            public List<BM_FileInfo> RelationshipFileInfoInfo { get; set; }

            public List<ContractModifyContractTemplateAddRow_In> ModifyContractTemplateAddRow { get; set; }

            public List<UpdateProductInfo_DownloadStyle_In> ProductInfoDownloadStyle { get; set; }
        }

        public class UpdatePaymentInfo_In : PaymentInfo_In
        {
            /// <summary>
            /// 主键
            /// </summary>
            //[Required(ErrorMessage = "支付信息主键不可为空")]
            public string Id { get; set; }

            public List<BM_FileInfo> PaymentVoucherInfo { get; set; }

        }

        public class CopyPaymentInfo_In : PaymentInfo_In
        {
            public List<BM_FileInfo> PaymentVoucherInfo { get; set; }

        }

        [ModelBinder(BinderType = typeof(MetadataValueModelBinder))]
        public class UpdateProductInfo_DownloadStyle_In : ProductInfo_DownloadStyle_In
        {
            /// <summary>
            /// 主键
            /// </summary>
            //[Required(ErrorMessage = "合同产品信息主键不可为空")]
            public string Id { get; set; }

            //[Required(ErrorMessage = "合同信息主键不可为空")]
            public string ContractId { get; set; }

            public List<UpdateProductInfo_DownloadStyle_In> ChildNode { get; set; }
        }

        [ModelBinder(BinderType = typeof(MetadataValueModelBinder))]
        public class UpdateProductInfo_In : ProductInfo_In
        {
            /// <summary>
            /// 主键
            /// </summary>
            //[Required(ErrorMessage = "合同产品信息主键不可为空")]
            public string Id { get; set; }

            //[Required(ErrorMessage = "合同信息主键不可为空")]
            public string ContractId { get; set; }

            public List<UpdateProductInfo_In> ChildNode { get; set; }
        }

        [ModelBinder(BinderType = typeof(MetadataValueModelBinder))]
        public class UpdateContractTemplate_In : ContractTemplate_In
        {
            /// <summary>
            /// 主键
            /// </summary>
            //[Required(ErrorMessage = "合同说明主键不可为空")]
            public string Id { get; set; }

            //[Required(ErrorMessage = "合同信息主键不可为空")]
            public string ContractId { get; set; }

        }

        public class AuditContractSuperSubAccount_In
        {
            [Required(ErrorMessage = "合同审核表主键id")]
            public Guid Id { get; set; }

            /// <summary>
            /// 审核反馈意见
            /// </summary>
            public string Feedback { get; set; }

            [Required(ErrorMessage = "状态不可为空")]
            public int State { get; set; }

        }

        public class AuditContract_In
        {
            [Required(ErrorMessage = "合同初审核表主键id")]
            public Guid Id { get; set; }

            /// <summary>
            /// 审核反馈意见
            /// </summary>
            public string Feedback { get; set; }

            /// <summary>
            /// 审核备注
            /// </summary>
            public string AuditRemark { get; set; }

            [Required(ErrorMessage = "状态不可为空")]
            public int State { get; set; }

            [Required(ErrorMessage = "乙方公司不可为空")]
            public Guid? SecondParty { get; set; }

            [Required(ErrorMessage = "是否境外客户不可为空")]
            public bool IsOverseasCustomer { get; set; }

            [Required(ErrorMessage = "是否私密客户不可为空")]
            public bool IsSecret { get; set; }

            public List<ProductInfoAudit> ProductInfoAudit { get; set; }

            public string CompanyAddressId { get; set; }

            public string RenewalContractNum { get; set; }

            /// <summary>
            /// 合同条款审核结果
            /// </summary>
            public List<AuditSingleContractTerm> ContractTermAuditList { get; set; }
        }

        public class DivisionSubmitAuditContract_In : TeamAuditContract_In
        {
            //[Required(ErrorMessage = "战队条款审核表主键id")]
            //public Guid Id { get; set; }

            ///// <summary>
            ///// 审核反馈意见
            ///// </summary>
            //public string Feedback { get; set; }

            ///// <summary>
            ///// 审核备注
            ///// </summary>
            //public string AuditRemark { get; set; }

            //[Required(ErrorMessage = "状态不可为空")]
            //public int State { get; set; }

            //public List<AuditSingleContractTerm_Team> ContractTermAuditList { get; set; }
        }

        public class BrigadeSubmitAuditContract_In : TeamAuditContract_In
        {
            //[Required(ErrorMessage = "大队条款审核表主键id")]
            //public Guid Id { get; set; }

            ///// <summary>
            ///// 审核反馈意见
            ///// </summary>
            //public string Feedback { get; set; }

            ///// <summary>
            ///// 审核备注
            ///// </summary>
            //public string AuditRemark { get; set; }

            //[Required(ErrorMessage = "状态不可为空")]
            //public int State { get; set; }

            //public List<AuditSingleContractTerm_Team> ContractTermAuditList { get; set; }
        }

        public class RegimentSubmitAuditContract_In : TeamAuditContract_In
        {
            //[Required(ErrorMessage = "中队条款审核表主键id")]
            //public Guid Id { get; set; }

            ///// <summary>
            ///// 审核反馈意见
            ///// </summary>
            //public string Feedback { get; set; }

            ///// <summary>
            ///// 审核备注
            ///// </summary>
            //public string AuditRemark { get; set; }

            //[Required(ErrorMessage = "状态不可为空")]
            //public int State { get; set; }

            //public List<AuditSingleContractTerm_Team> ContractTermAuditList { get; set; }
        }

        public class DivisionAuditContract_In : TeamAuditContract_In
        {
            //[Required(ErrorMessage = "战队条款审核表主键id")]
            //public Guid Id { get; set; }

            ///// <summary>
            ///// 审核反馈意见
            ///// </summary>
            //public string Feedback { get; set; }

            ///// <summary>
            ///// 审核备注
            ///// </summary>
            //public string AuditRemark { get; set; }

            //[Required(ErrorMessage = "状态不可为空")]
            //public int State { get; set; }

            //public List<AuditSingleContractTerm_Team> ContractTermAuditList { get; set; }
        }

        public class BrigadeAuditContract_In : TeamAuditContract_In
        {
            //[Required(ErrorMessage = "大队条款审核表主键id")]
            //public Guid Id { get; set; }

            ///// <summary>
            ///// 审核反馈意见
            ///// </summary>
            //public string Feedback { get; set; }

            ///// <summary>
            ///// 审核备注
            ///// </summary>
            //public string AuditRemark { get; set; }

            //[Required(ErrorMessage = "状态不可为空")]
            //public int State { get; set; }

            //public List<AuditSingleContractTerm_Team> ContractTermAuditList { get; set; }
        }

        public class RegimentAuditContract_In : TeamAuditContract_In
        {
            //[Required(ErrorMessage = "中队条款审核表主键id")]
            //public Guid Id { get; set; }

            ///// <summary>
            ///// 审核反馈意见
            ///// </summary>
            //public string Feedback { get; set; }

            ///// <summary>
            ///// 审核备注
            ///// </summary>
            //public string AuditRemark { get; set; }

            //[Required(ErrorMessage = "状态不可为空")]
            //public int State { get; set; }

            //public List<AuditSingleContractTerm_Team> ContractTermAuditList { get; set; }
        }

        public class TeamAuditContract_In
        {
            [Required(ErrorMessage = "团队条款审核表主键id")]
            public Guid Id { get; set; }

            /// <summary>
            /// 审核反馈意见
            /// </summary>
            public string Feedback { get; set; }

            /// <summary>
            /// 审核备注
            /// </summary>
            public string AuditRemark { get; set; }

            [Required(ErrorMessage = "状态不可为空")]
            public int State { get; set; }

            public List<AuditSingleContractTerm_Team> ContractTermAuditList { get; set; }

            public int Type { get; set; }
        }

        public class ReviewAuditContract_In
        {
            [Required(ErrorMessage = "合同审核表主键id")]
            public Guid Id { get; set; }

            /// <summary>
            /// 审核反馈意见
            /// </summary>
            public string Feedback { get; set; }

            /// <summary>
            /// 审核备注
            /// </summary>
            public string AuditRemark { get; set; }

            [Required(ErrorMessage = "状态不可为空")]
            public int State { get; set; }

            public List<ReviewSingleContractTerm> ContractTermAuditList { get; set; }
        }

        public class ProductInfoAudit : AddProductInfo_In
        {
            [Required(ErrorMessage = "合同产品信息表id不可为空")]
            public Guid ContractProductInfoId { get; set; }

            [Required(ErrorMessage = "合同表id不可为空")]
            public Guid ContractId { get; set; }

            public List<ProductInfoAudit> ChildNode { get; set; }

            /// <summary>
            /// 产品价格
            /// </summary>           
            public string ProductPrice { get; set; }

            /// <summary>
            /// 产品价格(组合产品的上级节点)
            /// </summary>           
            public string ParentProductPrice { get; set; }
        }

        public class AuditContractStampreViewAudit_In
        {
            [Required(ErrorMessage = "合同盖章审核表主键id")]
            public Guid Id { get; set; }

            /// <summary>
            /// 状态
            /// </summary>           
            public int? Status { get; set; }

            /// <summary>
            /// 审核反馈意见
            /// </summary>           
            public string Feedback { get; set; }

        }

        public class AuditContractOtherStampreViewAudit_In
        {
            [Required(ErrorMessage = "合同其他盖章审核表主键id")]
            public Guid Id { get; set; }

            /// <summary>
            /// 状态
            /// </summary>           
            public int? Status { get; set; }

            /// <summary>
            /// 审核反馈意见
            /// </summary>           
            public string Feedback { get; set; }

        }


        public class ContractBasicInfo_Out
        {
            public string Id { get; set; }

            /// <summary>
            /// 客户编号
            /// </summary>
            public string CustomerNum { get; set; }

            /// <summary>
            /// 客户名称
            /// </summary>
            public string CustomerName { get; set; }

            /// <summary>
            /// 客户表id
            /// </summary>           
            public string CustomerId { get; set; }
            /// <summary>
            /// 真实客户表id  
            /// </summary>           
            public string ExactCustomerId { get; set; }

            /// <summary>
            /// 客户编码（合同编码）
            /// </summary>           
            public string ContractNum { get; set; }

            /// <summary>
            /// 合同类型
            /// </summary>           
            public string ContractMethodName
            {
                get
                {
                    List<Db_sys_dictionary> dictionary = Dictionary.ContractMethod.Where(e => e.Value == ContractMethod.ToString()).ToList();
                    if (dictionary.Count > 0)
                    {
                        return dictionary.First().Name;
                    }
                    else
                    {
                        return "";
                    }
                    //return Dictionary.ContractMethod.First(e => e.Value == ContractMethod.ToString()).Name;
                }
            }

            /// <summary>
            /// 合同类型
            /// </summary>           
            public int ContractMethod { get; set; }

            /// <summary>
            /// 合同号
            /// </summary>           
            public string ContractNo { get; set; }

            /// <summary>
            /// 合同号
            /// </summary>
            public string ContractPaperEntityId { get; set; }

            /// <summary>
            /// 合同名称
            /// </summary>           
            public string ContractName { get; set; }

            /// <summary>
            /// 出单人名称
            /// </summary>
            private string _IssuerName;
            public string IssuerName
            {
                get
                {
                    string OrgName = "";
                    if (OrgDivisionName.IsNotNullOrEmpty())
                    {
                        OrgName = OrgName + OrgDivisionName;
                    }
                    if (OrgBrigadeName.IsNotNullOrEmpty())
                    {
                        if (OrgName != "")
                        {
                            OrgName = OrgName + "/";
                        }
                        OrgName = OrgName + OrgBrigadeName;
                    }
                    if (OrgRegimentName.IsNotNullOrEmpty())
                    {
                        if (OrgName != "")
                        {
                            OrgName = OrgName + "/";
                        }
                        OrgName = OrgName + OrgRegimentName;
                    }
                    if (OrgName != "")
                    {
                        OrgName = "(" + OrgName + ")";
                    }
                    return _IssuerName + OrgName;
                }
                set { _IssuerName = value; }
            }

            /// <summary>
            /// 出单人名称
            /// </summary>
            public string IssuerNameFull
            {
                get
                {
                    return IssuerName;
                }
            }

            /// <summary>
            /// 战队
            /// </summary>           
            public string OrgDivisionName { get; set; }

            /// <summary>
            /// 大队
            /// </summary>           
            public string OrgBrigadeName { get; set; }

            /// <summary>
            /// 中队
            /// </summary>           
            public string OrgRegimentName { get; set; }

            /// <summary>
            /// 出单人
            /// </summary>           
            public string Issuer { get; set; }

            /// <summary>
            /// 签约日期
            /// </summary>           
            private string _SigningDate;
            public string SigningDate
            {
                //get; set;
                get
                {
                    return _SigningDate == null ? "" : Convert.ToDateTime(_SigningDate).ToString("yyyy-MM-dd");
                }
                set
                {
                    _SigningDate = value;
                }
            }

            /// <summary>
            /// 甲方公司
            /// </summary>  
            public string FirstPartyName { get; set; }

            /// <summary>
            /// 甲方公司
            /// </summary>           
            public string FirstParty { get; set; }

            /// <summary>
            /// 乙方公司
            /// </summary>
            public string SecondPartyName { get; set; }

            /// <summary>
            /// 乙方公司
            /// </summary>           
            public string SecondParty { get; set; }

            /// <summary>
            /// 合同金额
            /// </summary>           
            public decimal ContractAmount { get; set; }

            /// <summary>
            /// 外币合同金额
            /// </summary> 
            public decimal FCContractAmount { get; set; }

            /// <summary>
            /// 币种
            /// </summary>           
            public string CurrencyName
            {
                get
                {
                    List<Db_sys_dictionary> dictionary = Dictionary.Currency.Where(e => e.Value == Currency.ToString()).ToList();
                    if (dictionary.Count > 0)
                    {
                        return dictionary.First().Name;
                    }
                    else
                    {
                        return "";
                    }
                    //return Dictionary.Currency.First(e => e.Value == Currency.ToString()).Name;
                }
            }

            /// <summary>
            /// 币种
            /// </summary>           
            public int Currency { get; set; }

            /// <summary>
            /// 签约类型
            /// </summary>           
            public string ContractTypeName
            {
                get
                {
                    List<Db_sys_dictionary> dictionary = Dictionary.ContractType.Where(e => e.Value == ContractType.ToString()).ToList();
                    if (dictionary.Count > 0)
                    {
                        return dictionary.First().Name;
                    }
                    else
                    {
                        return "";
                    }
                    //return Dictionary.ContractType.First(e => e.Value == ContractType.ToString()).Name;
                }
            }

            /// <summary>
            /// 签约类型
            /// </summary>           
            public int ContractType { get; set; }

            /// <summary>
            /// 联系信息
            /// </summary>           
            public string ContactInformationName
            {
                get
                {
                    List<Db_sys_dictionary> dictionary = Dictionary.ContactInformation.Where(e => e.Value == ContactInformation.ToString()).ToList();
                    if (dictionary.Count > 0)
                    {
                        return dictionary.First().Name;
                    }
                    else
                    {
                        return "";
                    }
                    //return Dictionary.ContactInformation.First(e => e.Value == ContactInformation.ToString()).Name;
                }
            }

            /// <summary>
            /// 联系信息
            /// </summary>           
            public int ContactInformation { get; set; }

            /// <summary>
            /// 联系人
            /// </summary>           
            public string Contacts { get; set; }

            /// <summary>
            /// 职务
            /// </summary>           
            public string Job { get; set; }

            /// <summary>
            /// 联系方式
            /// </summary>           
            public string ContactWay { get; set; }

            /// <summary>
            /// 电子邮箱
            /// </summary>           
            public string Email { get; set; }

            /// <summary>
            /// 固定电话
            /// </summary>           
            public string Telephone { get; set; }

            /// <summary>
            /// 传真号码
            /// </summary>           
            public string Fax { get; set; }

            /// <summary>
            /// 邮编
            /// </summary>           
            public string PostalCode { get; set; }

            /// <summary>
            /// 详细地址-国家
            /// </summary>           
            public string CountryName { get; set; }

            /// <summary>
            /// 详细地址-国家
            /// </summary>           
            public int Country { get; set; }

            /// <summary>
            /// 详细地址-省
            /// </summary>           
            public string ProvinceName { get; set; }

            /// <summary>
            /// 详细地址-省
            /// </summary>           
            public int Province { get; set; }

            /// <summary>
            /// 详细地址-市
            /// </summary>           
            public string CityName { get; set; }

            /// <summary>
            /// 详细地址-市
            /// </summary>           
            public int City { get; set; }

            /// <summary>
            /// 详细地址-详细地址
            /// </summary>           
            public string Address { get; set; }

            /// <summary>
            /// 是否自定义合同说明
            /// </summary>           
            public int IsCustomContractDescription { get; set; }

            /// <summary>
            /// 合同说明
            /// </summary>           
            public string ContractDescription { get; set; }

            /// <summary>
            /// 合同说明模板Id
            /// </summary>           
            public string ContractTemplateId { get; set; }

            /// <summary>
            /// 合同状态
            /// </summary>           
            public string ContractStatusName
            {
                get
                {
                    List<Db_sys_dictionary> dictionary = Dictionary.ContractStatus.Where(e => e.Value == ContractStatus.ToString()).ToList();
                    if (dictionary.Count > 0)
                    {
                        return dictionary.First().Name;
                    }
                    else
                    {
                        return "";
                    }
                    //return Dictionary.ContractStatus.First(e => e.Value == ContractStatus.ToString()).Name;
                }
            }

            /// <summary>
            /// 审核类型
            /// </summary>           
            public int AuditType { get; set; }

            /// <summary>
            /// 合同状态
            /// </summary>             
            private int _ContractStatus;
            public int ContractStatus
            {
                get
                {
                    if (AuditType == EnumAuditType.AutomaticReview.ToInt() && _ContractStatus == EnumContractStatus.Pass.ToInt())
                    {
                        return EnumContractStatus.AutoPass.ToInt();
                    }
                    else
                    {
                        return _ContractStatus;
                    }
                }
                set { _ContractStatus = value; }
            }

            /// <summary>
            /// 使用状态
            /// </summary>           
            public string UsageStatusName { get; set; }

            /// <summary>
            /// 使用状态
            /// </summary>           
            public int UsageStatus { get; set; }

            /// <summary>
            /// 回收状态
            /// </summary>           
            public string RecoveryStatusName
            {
                get
                {
                    List<Db_sys_dictionary> dictionary = Dictionary.RecoveryStatus.Where(e => e.Value == RecoveryStatus.ToString()).ToList();
                    if (dictionary.Count > 0)
                    {
                        return dictionary.First().Name;
                    }
                    else
                    {
                        return "";
                    }
                    //return Dictionary.RecoveryStatus.First(e => e.Value == RecoveryStatus.ToString()).Name;
                }
            }

            /// <summary>
            /// 回收状态
            /// </summary>           
            public int RecoveryStatus { get; set; }

            /// <summary>
            /// 回收时间
            /// </summary>           
            public DateTime? RecoveryTime { get; set; }

            /// <summary>
            /// 回收备注
            /// </summary>           
            public string RecyclingRemarks { get; set; }

            /// <summary>
            /// 是否私密
            /// </summary>           
            public bool IsSecret { get; set; }

            ///// <summary>
            ///// 保护截止日
            ///// </summary>           
            //public DateTime? ProtectionDeadline { get; set; }

            /// <summary>
            /// 创建时间
            /// </summary>
            private string _ProtectionDeadline;
            public string ProtectionDeadline
            {
                //get; set;
                get
                {
                    return StringUtil.IsNullOrEmpty(_ProtectionDeadline) ? "" : Convert.ToDateTime(_ProtectionDeadline).ToString("yyyy-MM-dd");
                }
                set
                {
                    _ProtectionDeadline = value;
                }
            }

            /// <summary>
            /// 是否特殊
            /// </summary>           
            public int IsSpecial { get; set; }

            /// <summary>
            /// 特殊说明
            /// </summary>  
            public string SpecialDescription { get; set; }

            /// <summary>
            /// 是否催单
            /// </summary>
            public int IsUrgeRegistration { get; set; }

            /// <summary>
            /// 服务情况
            /// </summary>  
            public string ProductServiceInfoStatusName
            {
                get
                {
                    List<Db_sys_dictionary> dictionary = Dictionary.ServiceStatus.Where(e => e.Value == ProductServiceInfoStatus.ToString()).ToList();
                    if (dictionary.Count > 0)
                    {
                        return dictionary.First().Name;
                    }
                    else
                    {
                        return "";
                    }
                    //return Dictionary.ServiceStatus.First(e => e.Value == ProductServiceInfoStatus.ToString()).Name;
                }
            }

            /// <summary>
            /// 服务情况
            /// </summary>  
            public int ProductServiceInfoStatus { get; set; }

            /// <summary>
            /// 创建时间
            /// </summary>
            private string _CreateDate;
            public string CreateDate
            {
                //get; set;
                get { return Convert.ToDateTime(_CreateDate).ToString("yyyy-MM-dd HH:mm"); }
                set { _CreateDate = value; }
            }

            /// <summary>
            /// 账号终止日
            /// </summary>
            public string AccountTerminationDate { get; set; }

            /// <summary>
            /// 企业类型
            /// </summary>
            public int CreditType { get; set; }

            /// <summary>
            /// 是否境外客户
            /// </summary>           
            public bool IsOverseasCustomer { get; set; }

            /// <summary>
            /// 是否申请超级子账号
            /// </summary>           
            public bool IsApplySuperSubAccount { get; set; }

            /// <summary>
            /// 未盖章合同
            /// </summary>
            public List<BM_FileInfo> ContractFile { get; set; }

            /// <summary>
            /// 合同特殊附件
            /// </summary>
            public List<BM_FileInfo> ContractSpecialAttachFile { get; set; }

            /// <summary>
            /// 电子合同附件
            /// </summary>
            public List<BM_FileInfo> ElectronicContractAttachFile { get; set; }

            /// <summary>
            /// 邓白氏合同原件附件
            /// </summary>
            public List<BM_FileInfo> DBOriginalContractAttachFile { get; set; }

            /// <summary>
            /// 已盖章合同附件
            /// </summary>
            public List<BM_FileInfo> SealedContractAttachFile { get; set; }

            /// <summary>
            /// 其他合同附件
            /// </summary>
            public List<BM_FileInfo> OtherContractAttachFile { get; set; }

            /// <summary>
            /// 合同说明列表
            /// </summary>
            public List<ContractTemplateList> ContractTemplateList { get; set; }

            /// <summary>
            /// 合同条款列表
            /// </summary>
            public List<ContractTermTemplateList> ContractTermTemplate { get; set; }

            /// <summary>
            /// 跟踪阶段
            /// </summary>           
            public int TrackingStage { get; set; }
            /// <summary>
            /// 跟踪阶段
            /// </summary>           
            public string TrackingStageNname
            {
                get
                {
                    return ((EnumTrackingStage)TrackingStage).GetEnumDescription();
                }
            }

            /// <summary>
            /// 开票状态
            /// </summary>
            public string IsInvoiceName
            {
                get
                {
                    return IsInvoice == null ? "" : ((EnumContractInvoiceDisplayStatus)IsInvoice).GetEnumDescription().ToString();
                }
            }

            /// <summary>
            /// 开票状态
            /// </summary>
            public int? IsInvoice { get; set; }

            /// <summary>
            /// 销售国家
            /// </summary>           
            public int SalesCountry { get; set; }

            /// <summary>
            /// 销售国家
            /// </summary>           
            public string SalesCountryName { get; set; }

            /// <summary>
            /// 备注
            /// </summary>
            public string Remark { get; set; }

            /// <summary>
            /// 签约合同代理人
            /// </summary>
            public string SigningAgentName { get; set; }

            /// <summary>
            /// 签约合同代理人电话
            /// </summary>
            public string SigningAgentPhone { get; set; }

            /// <summary>
            /// 签约合同代理人Email
            /// </summary>
            public string SigningAgentEmail { get; set; }

            /// <summary>
            /// 签约合同代理人传真
            /// </summary>
            public string SigningAgentFax { get; set; }

            /// <summary>
            /// 盖章审核状态
            /// </summary>      
            public int StampReviewStatus { get; set; }

            /// <summary>
            /// 盖章审核
            /// </summary>           
            public string StampReviewStatusName
            {
                get
                {
                    return Dictionary.StampReviewStatus.First(e => e.Value == StampReviewStatus.ToString()).Name;
                }
            }

            /// <summary>
            /// 合同模板类型
            /// </summary>
            public int TemplateType { get; set; }

            /// <summary>
            /// 中文模版币种
            /// </summary>
            public string ChineseTemplateCurrency { get; set; }

            /// <summary>
            /// 英文模板币种
            /// </summary>
            public string EnglishTemplateCurrency { get; set; }

            /// <summary>
            /// 是否中英文版
            /// </summary>
            public int IsBothChineseAndEnglish { get; set; }

            /// <summary>
            /// 地址
            /// </summary> 
            public string CompanyAddressId { get; set; }

            /// <summary>
            /// 地址
            /// </summary>           
            public string CompanyAddressAddress { get; set; }

            /// <summary>
            /// 邮编
            /// </summary>           
            public string CompanyAddressPostalCode { get; set; }

            /// <summary>
            /// 地址
            /// </summary> 
            public string CompanyAddressName { get; set; }

            /// <summary>
            /// 续约客户编码
            /// </summary> 
            public string RenewalContractNum { get; set; }

            /// <summary>
            /// 甲方子公司
            /// </summary>
            public List<string> FirstPartySubCompany { get; set; }

            /// <summary>
            /// 甲方关联公司
            /// </summary>
            public List<string> FirstPartyRelated { get; set; }

            /// <summary>
            /// 公司合并表id
            /// </summary>
            public string MergeCompanyAuditId { get; set; }

            /// <summary>
            /// 公司合并附件
            /// </summary>
            public List<BM_FileInfo> MergeCompanyAttachFile { get; set; }

            /// <summary>
            /// 公司合并关系说明
            /// </summary>
            public string RelationshipDescription { get; set; }
            /// <summary>
            /// 被合并公司名称
            /// </summary>
            public string MergeCompanyName { get; set; }
            /// <summary>
            /// 合并是否有效
            /// </summary>
            public string MergeRelationStateName { get; set; }
            /// <summary>
            /// 合并是否有效
            /// </summary>
            public int? MergeRelationState { get; set; }

            public List<ContractTemplateAddRow_Out> ModifyContractTemplateAddRow { get; set; }

            public string RegimentAuditId { get; set; }

            public string BrigadeAuditId { get; set; }

            public string DivisionAuditId { get; set; }

            public bool IsMerged { get; set; }

            /// <summary>
            /// 客户的当前Gtis状态
            /// </summary>
            public EnumCustomerGtisState? CustomerGtisState { get; set; }
            /// <summary>
            /// 客户的当前Gtis状态
            /// </summary>
            public string CustomerGtisStateName { get { return CustomerGtisState == null ? "" : CustomerGtisState.GetEnumDescription().ToString(); } }

            /// <summary>
            /// 上级合同ID
            /// </summary>
            public string ParentContractId { get; set; }

            public List<ParentContractFile> ParentContractFileList { get; set; }
        }

        public class ParentContractFile
        {
            /// <summary>
            /// 合同ID
            /// </summary>
            public string ContractId { get; set; }

            /// <summary>
            /// 合同类型
            /// </summary>
            public int ContractType { get; set; }
            /// <summary>
            /// 未盖章合同
            /// </summary>
            public List<BM_FileInfo> ContractFile { get; set; }

            /// <summary>
            /// 合同特殊附件
            /// </summary>
            public List<BM_FileInfo> ContractSpecialAttachFile { get; set; }

            /// <summary>
            /// 电子合同附件
            /// </summary>
            public List<BM_FileInfo> ElectronicContractAttachFile { get; set; }

            /// <summary>
            /// 邓白氏合同原件附件
            /// </summary>
            public List<BM_FileInfo> DBOriginalContractAttachFile { get; set; }

            /// <summary>
            /// 已盖章合同附件
            /// </summary>
            public List<BM_FileInfo> SealedContractAttachFile { get; set; }

            /// <summary>
            /// 盖章合同附件集合
            /// </summary>
            public List<BM_FileInfo> SignContractAttachFile { get; set; }

        }

        public class ContractTemplateList
        {
            /// <summary>
            /// 主键
            /// </summary>
            public string Id { get; set; }

            /// <summary>
            /// 合同表id
            /// </summary>           
            public string ContractId { get; set; }

            /// <summary>
            /// 合同说明Id
            /// </summary>           
            public string ContractTemplateId { get; set; }

            /// <summary>
            /// 合同说明
            /// </summary>           
            public string ContractDescription { get; set; }
        }

        public class ContractLoadInfo_Out
        {
            public string Id { get; set; }

            /// <summary>
            /// 客户名称
            /// </summary>
            public string CustomerName { get; set; }

            /// <summary>
            /// 客户表id
            /// </summary>           
            public string CustomerId { get; set; }

            /// <summary>
            /// 客户编码（合同编码）
            /// </summary>           
            public string ContractNum { get; set; }

            /// <summary>
            /// 合同类型
            /// </summary>           
            public string ContractMethodName
            {
                get
                {
                    return Dictionary.ContractMethod.First(e => e.Value == ContractMethod.ToString()).Name;
                }
            }

            /// <summary>
            /// 合同类型
            /// </summary>           
            public int ContractMethod { get; set; }

            /// <summary>
            /// 合同号
            /// </summary>           
            public string ContractNo { get; set; }

            /// <summary>
            /// 合同号
            /// </summary>
            public string ContractPaperEntityId { get; set; }

            /// <summary>
            /// 合同名称
            /// </summary>           
            public string ContractName { get; set; }

            /// <summary>
            /// 出单人名称
            /// </summary>
            public string IssuerName { get; set; }

            /// <summary>
            /// 出单人
            /// </summary>           
            public string Issuer { get; set; }

            /// <summary>
            /// 签约日期
            /// </summary>           
            public DateTime SigningDate { get; set; }

            /// <summary>
            /// 甲方公司
            /// </summary>  
            public string FirstPartyName { get; set; }

            /// <summary>
            /// 甲方公司
            /// </summary>           
            public string FirstParty { get; set; }

            /// <summary>
            /// 乙方公司
            /// </summary>
            public string SecondPartyName { get; set; }

            /// <summary>
            /// 乙方公司
            /// </summary>           
            public string SecondParty { get; set; }

            /// <summary>
            /// 合同金额
            /// </summary>           
            public decimal ContractAmount { get; set; }

            /// <summary>
            /// 外币合同金额
            /// </summary> 
            public decimal FCContractAmount { get; set; }

            /// <summary>
            /// 币种
            /// </summary>           
            public string CurrencyName
            {
                get
                {
                    return Dictionary.Currency.First(e => e.Value == Currency.ToString()).Name;
                }
            }

            /// <summary>
            /// 币种
            /// </summary>           
            public int Currency { get; set; }

            /// <summary>
            /// 签约类型
            /// </summary>           
            public string ContractTypeName
            {
                get
                {
                    return Dictionary.ContractType.First(e => e.Value == ContractType.ToString()).Name;
                }
            }

            /// <summary>
            /// 签约类型
            /// </summary>           
            public int ContractType { get; set; }

            /// <summary>
            /// 联系信息
            /// </summary>           
            public string ContactInformationName
            {
                get
                {
                    return Dictionary.ContactInformation.First(e => e.Value == ContactInformation.ToString()).Name;
                }
            }

            /// <summary>
            /// 联系信息
            /// </summary>           
            public int ContactInformation { get; set; }

            /// <summary>
            /// 联系人
            /// </summary>           
            public string Contacts { get; set; }

            /// <summary>
            /// 职务
            /// </summary>           
            public string Job { get; set; }

            /// <summary>
            /// 联系方式
            /// </summary>           
            public string ContactWay { get; set; }

            /// <summary>
            /// 电子邮箱
            /// </summary>           
            public string Email { get; set; }

            /// <summary>
            /// 固定电话
            /// </summary>           
            public string Telephone { get; set; }

            /// <summary>
            /// 传真号码
            /// </summary>           
            public string Fax { get; set; }

            /// <summary>
            /// 邮编
            /// </summary>           
            public string PostalCode { get; set; }

            /// <summary>
            /// 详细地址-国家
            /// </summary>           
            public string CountryName { get; set; }

            /// <summary>
            /// 详细地址-国家
            /// </summary>           
            public int Country { get; set; }

            /// <summary>
            /// 详细地址-省
            /// </summary>           
            public string ProvinceName { get; set; }

            /// <summary>
            /// 详细地址-省
            /// </summary>           
            public int Province { get; set; }

            /// <summary>
            /// 详细地址-市
            /// </summary>           
            public string CityName { get; set; }

            /// <summary>
            /// 详细地址-市
            /// </summary>           
            public int City { get; set; }

            /// <summary>
            /// 详细地址-详细地址
            /// </summary>           
            public string Address { get; set; }

            /// <summary>
            /// 是否自定义合同说明
            /// </summary>           
            public int IsCustomContractDescription { get; set; }

            /// <summary>
            /// 合同说明
            /// </summary>           
            public string ContractDescription { get; set; }

            /// <summary>
            /// 合同说明模板Id
            /// </summary>           
            public string ContractTemplateId { get; set; }

            /// <summary>
            /// 合同状态
            /// </summary>           
            public string ContractStatusName
            {
                get
                {
                    return Dictionary.ContractStatus.First(e => e.Value == ContractStatus.ToString()).Name;
                }
            }

            /// <summary>
            /// 审核类型
            /// </summary>           
            public int AuditType { get; set; }

            /// <summary>
            /// 合同状态
            /// </summary>             
            private int _ContractStatus;
            public int ContractStatus
            {
                get
                {
                    if (AuditType == EnumAuditType.AutomaticReview.ToInt() && _ContractStatus == EnumContractStatus.Pass.ToInt())
                    {
                        return EnumContractStatus.AutoPass.ToInt();
                    }
                    else
                    {
                        return _ContractStatus;
                    }
                }
                set { _ContractStatus = value; }
            }

            /// <summary>
            /// 使用状态
            /// </summary>           
            public string UsageStatusName { get; set; }

            /// <summary>
            /// 使用状态
            /// </summary>           
            public int UsageStatus { get; set; }

            /// <summary>
            /// 回收状态
            /// </summary>           
            public string RecoveryStatusName
            {
                get
                {
                    return Dictionary.RecoveryStatus.First(e => e.Value == RecoveryStatus.ToString()).Name;
                }
            }

            /// <summary>
            /// 回收状态
            /// </summary>           
            public int RecoveryStatus { get; set; }

            /// <summary>
            /// 回收时间
            /// </summary>           
            public DateTime RecoveryTime { get; set; }

            /// <summary>
            /// 回收备注
            /// </summary>           
            public string RecyclingRemarks { get; set; }

            /// <summary>
            /// 是否私密
            /// </summary>           
            public bool IsSecret { get; set; }

            /// <summary>
            /// 保护截止日
            /// </summary>           
            public DateTime ProtectionDeadline { get; set; }

            /// <summary>
            /// 是否特殊
            /// </summary>           
            public int IsSpecial { get; set; }

            /// <summary>
            /// 特殊说明
            /// </summary>           
            public string SpecialDescription { get; set; }

            /// <summary>
            /// 是否催单
            /// </summary>
            public int IsUrgeRegistration { get; set; }

            /// <summary>
            /// 签约合同代理人
            /// </summary>
            public string SigningAgentName { get; set; }

            /// <summary>
            /// 签约合同代理人电话
            /// </summary>
            public string SigningAgentPhone { get; set; }

            /// <summary>
            /// 签约合同代理人Email
            /// </summary>
            public string SigningAgentEmail { get; set; }

            /// <summary>
            /// 签约合同代理人传真
            /// </summary>
            public string SigningAgentFax { get; set; }

            /// <summary>
            /// 盖章审核状态
            /// </summary>      
            public int StampReviewStatus { get; set; }

            public bool IsOverseasCustomer { get; set; }

            /// <summary>
            /// 公司地址
            /// </summary>           
            public string CompanyAddressId { get; set; }

            /// <summary>
            /// 地址
            /// </summary>           
            public string CompanyAddressAddress { get; set; }

            /// <summary>
            /// 邮编
            /// </summary>           
            public string CompanyAddressPostalCode { get; set; }

            /// <summary>
            /// 续约客户编码
            /// </summary> 
            public string RenewalContractNum { get; set; }
        }

        public class ContractAuditInfo_Out
        {
            public string Id { get; set; }

            /// <summary>
            /// 合同表id
            /// </summary>           
            public string ContractId { get; set; }

            /// <summary>
            /// 申请人id
            /// </summary>           
            public string ApplicantId { get; set; }

            /// <summary>
            /// 申请人id
            /// </summary>           
            public string ApplicantName { get; set; }

            /// <summary>
            /// 申请时间
            /// </summary>           
            public DateTime? ApplicantDate { get; set; }

            /// <summary>
            /// 审核反馈意见
            /// </summary>           
            public string Feedback { get; set; }

            /// <summary>
            /// 状态
            /// </summary>           
            public int? State { get; set; }

            /// <summary>
            /// 是否确认（同意审核人的修改、拒绝审核人的修改）
            /// </summary>           
            public int? IsConfirm { get; set; }

            /// <summary>
            /// 审核人id
            /// </summary>           
            public string ReviewerId { get; set; }

            /// <summary>
            /// 审核人id
            /// </summary>           
            public string ReviewerName { get; set; }

            /// <summary>
            /// 审核时间
            /// </summary>           
            public DateTime? ReviewerDate { get; set; }

            /// <summary>
            /// 审核类型
            /// </summary>           
            public int? AuditType { get; set; }

            /// <summary>
            /// 确认人id
            /// </summary>           
            public string ConfirmId { get; set; }

            /// <summary>
            /// 确认人id
            /// </summary>           
            public string ConfirmName { get; set; }

            /// <summary>
            /// 确认时间
            /// </summary>           
            public DateTime? ConfirmDate { get; set; }

            /// <summary>
            /// 审核备注
            /// </summary>           
            public string AuditRemark { get; set; }
        }

        public class ContractInitialAuditInfo_Out
        {
            public string Id { get; set; }

            /// <summary>
            /// 合同表id
            /// </summary>           
            public string ContractId { get; set; }

            /// <summary>
            /// 申请人id
            /// </summary>           
            public string ApplicantId { get; set; }

            /// <summary>
            /// 申请人id
            /// </summary>           
            public string ApplicantName { get; set; }

            /// <summary>
            /// 申请时间
            /// </summary>           
            public DateTime? ApplicantDate { get; set; }

            /// <summary>
            /// 状态
            /// </summary>           
            public int? Status { get; set; }

            /// <summary>
            /// 审核人id
            /// </summary>           
            public string ReviewerId { get; set; }

            /// <summary>
            /// 审核人id
            /// </summary>           
            public string ReviewerName { get; set; }

            /// <summary>
            /// 审核时间
            /// </summary>           
            public DateTime? ReviewerDate { get; set; }

            /// <summary>
            /// 审核反馈意见
            /// </summary>           
            public string Feedback { get; set; }

            /// <summary>
            /// 审核类型
            /// </summary>           
            public int? AuditType { get; set; }

            /// <summary>
            /// 审核备注
            /// </summary>           
            public string AuditRemark { get; set; }

            /// <summary>
            /// 续约客户编码
            /// </summary> 
            public string RenewalContractNum { get; set; }

            /// <summary>
            /// 公司地址
            /// </summary>
            public string CompanyAddressId { get; set; }
        }

        public class ContractDivisionAuditInfo_Out
        {
            public string Id { get; set; }

            /// <summary>
            /// 合同表id
            /// </summary>           
            public string ContractId { get; set; }

            /// <summary>
            /// 申请人id
            /// </summary>           
            public string ApplicantId { get; set; }

            /// <summary>
            /// 申请人id
            /// </summary>           
            public string ApplicantName { get; set; }

            /// <summary>
            /// 申请时间
            /// </summary>           
            public DateTime? ApplicantDate { get; set; }

            /// <summary>
            /// 状态
            /// </summary>           
            public int? Status { get; set; }

            /// <summary>
            /// 审核人id
            /// </summary>           
            public string ReviewerId { get; set; }

            /// <summary>
            /// 审核人id
            /// </summary>           
            public string ReviewerName { get; set; }

            /// <summary>
            /// 审核时间
            /// </summary>           
            public DateTime? ReviewerDate { get; set; }

            /// <summary>
            /// 审核反馈意见
            /// </summary>           
            public string Feedback { get; set; }

            /// <summary>
            /// 审核备注
            /// </summary>           
            public string AuditRemark { get; set; }
        }

        public class ContractBrigadeAuditInfo_Out
        {
            public string Id { get; set; }

            /// <summary>
            /// 合同表id
            /// </summary>           
            public string ContractId { get; set; }

            /// <summary>
            /// 申请人id
            /// </summary>           
            public string ApplicantId { get; set; }

            /// <summary>
            /// 申请人id
            /// </summary>           
            public string ApplicantName { get; set; }

            /// <summary>
            /// 申请时间
            /// </summary>           
            public DateTime? ApplicantDate { get; set; }

            /// <summary>
            /// 状态
            /// </summary>           
            public int? Status { get; set; }

            /// <summary>
            /// 审核人id
            /// </summary>           
            public string ReviewerId { get; set; }

            /// <summary>
            /// 审核人id
            /// </summary>           
            public string ReviewerName { get; set; }

            /// <summary>
            /// 审核时间
            /// </summary>           
            public DateTime? ReviewerDate { get; set; }

            /// <summary>
            /// 审核反馈意见
            /// </summary>           
            public string Feedback { get; set; }

            /// <summary>
            /// 审核备注
            /// </summary>           
            public string AuditRemark { get; set; }
        }

        public class ContractRegimentAuditInfo_Out
        {
            public string Id { get; set; }

            /// <summary>
            /// 合同表id
            /// </summary>           
            public string ContractId { get; set; }

            /// <summary>
            /// 申请人id
            /// </summary>           
            public string ApplicantId { get; set; }

            /// <summary>
            /// 申请人id
            /// </summary>           
            public string ApplicantName { get; set; }

            /// <summary>
            /// 申请时间
            /// </summary>           
            public DateTime? ApplicantDate { get; set; }

            /// <summary>
            /// 状态
            /// </summary>           
            public int? Status { get; set; }

            /// <summary>
            /// 审核人id
            /// </summary>           
            public string ReviewerId { get; set; }

            /// <summary>
            /// 审核人id
            /// </summary>           
            public string ReviewerName { get; set; }

            /// <summary>
            /// 审核时间
            /// </summary>           
            public DateTime? ReviewerDate { get; set; }

            /// <summary>
            /// 审核反馈意见
            /// </summary>           
            public string Feedback { get; set; }

            /// <summary>
            /// 审核备注
            /// </summary>           
            public string AuditRemark { get; set; }
        }

        public class SearchContractList_In : ApiTableIn
        {
            /// <summary>
            /// 合同名称
            /// </summary>   
            public string ContractName { get; set; }
            /// <summary>
            /// 甲方公司
            /// </summary>  
            public string FirstPartyName { get; set; }
            /// <summary>
            /// 支付信息
            /// </summary>
            public string PaymentCompanyName { get; set; }
            /// <summary>
            /// 客户编码（合同编码）
            /// </summary>          
            public string ContractNum { get; set; }
            /// <summary>
            /// 签约产品
            /// </summary>          
            public List<string> Product { get; set; }
            /// <summary>
            /// 出单人名称
            /// </summary>
            public List<string> IssuerName { get; set; }
            /// <summary>
            /// 到账情况
            /// </summary>   
            public int? IsReceipt { get; set; }
            /// <summary>
            /// 支付方式
            /// </summary>  
            public int? PaymentMethod { get; set; }
            /// <summary>
            /// 收款公司
            /// </summary> 
            public string CollectingCompany { get; set; }
            /// <summary>
            /// 签约类型
            /// </summary>           
            public int? ContractType { get; set; }
            /// <summary>
            /// 合同状态
            /// </summary>           
            public List<int?> ContractStatus { get; set; }
            /// <summary>
            /// 币种
            /// </summary>           
            public int? Currency { get; set; }
            /// <summary>
            /// 合同类型
            /// </summary>           
            public int? ContractMethod { get; set; }
            /// <summary>
            /// 服务情况
            /// </summary>  
            public int? ProductServiceInfoStatus { get; set; }
            /// <summary>
            /// 回收状态
            /// </summary>           
            public int? RecoveryStatus { get; set; }
            /// <summary>
            /// 签约日期
            /// </summary> 
            public DateTime? SigningDateStart { get; set; }
            /// <summary>
            /// 签约日期
            /// </summary> 
            public DateTime? SigningDateEnd { get; set; }
            /// <summary>
            /// 创建时间
            /// </summary> 
            public DateTime? CreateDateStart { get; set; }
            /// <summary>
            /// 创建时间
            /// </summary> 
            public DateTime? CreateDateEnd { get; set; }
            /// <summary>
            /// 审核备注
            /// </summary>   
            public string AuditRemark { get; set; }
            /// <summary>
            /// 快捷搜索选项
            /// </summary>
            public EnumQueryListType enumQueryListType { get; set; }
            /// <summary>
            /// 是否代付款
            /// </summary>           
            public bool? IsBehalfPayment { get; set; }
            public string IsBehalfPaymentCompanyName { get; set; }
            /// <summary>
            /// 首页跳转-类型
            /// </summary>
            public EnumIndexRedirectType EnumIndexRedirectType { get; set; }
            /// <summary>
            /// 首页跳转-时间
            /// </summary>
            public EnumStatisticsDateSpanSelect EnumStatisticsDateSpanSelect { get; set; }
            /// <summary>
            /// 首页跳转-特定年份
            /// </summary>
            public int SelectYear { get; set; }
            /// <summary>
            /// 销售国家
            /// </summary>           
            public int? SalesCountry { get; set; }
            /// <summary>
            /// 详细地址-国家
            /// </summary>           
            public int? Country { get; set; }
            /// <summary>
            /// 详细地址-省
            /// </summary>           
            public int? Province { get; set; }
            /// <summary>
            /// 详细地址-市
            /// </summary>           
            public int? City { get; set; }
            /// <summary>
            /// 详细地址-详细地址
            /// </summary>           
            public string Address { get; set; }
            public string CustomerId { get; set; }

            public string? Id { get; set; }

            /// <summary>
            /// 是否私密-查询条件
            /// 2025年6月5日 增加
            /// </summary>
            public bool? IsSecret { get; set; }
        }

        public class SearchContractStampreViewList_In : ApiTableIn
        {
            /// <summary>
            /// 盖章审核
            /// </summary>
            public int StampReviewStatus { get; set; }

            /// <summary>
            /// 合同名称
            /// </summary>   
            public string ContractName { get; set; }

            /// <summary>
            /// 甲方公司
            /// </summary>  
            public string FirstPartyName { get; set; }

            /// <summary>
            /// 支付信息
            /// </summary>
            public string PaymentCompanyName { get; set; }

            /// <summary>
            /// 客户编码（合同编码）
            /// </summary>          
            public string ContractNum { get; set; }

            /// <summary>
            /// 签约产品
            /// </summary>          
            public List<string> Product { get; set; }

            /// <summary>
            /// 出单人名称
            /// </summary>
            public List<string> IssuerName { get; set; }

            /// <summary>
            /// 到账情况
            /// </summary>   
            public int? IsReceipt { get; set; }

            /// <summary>
            /// 支付方式
            /// </summary>  
            public int? PaymentMethod { get; set; }

            /// <summary>
            /// 收款公司
            /// </summary> 
            public string CollectingCompany { get; set; }

            /// <summary>
            /// 签约类型
            /// </summary>           
            public int? ContractType { get; set; }

            /// <summary>
            /// 合同状态
            /// </summary>           
            public int? ContractStatus { get; set; }

            /// <summary>
            /// 币种
            /// </summary>           
            public int? Currency { get; set; }

            /// <summary>
            /// 合同类型
            /// </summary>           
            public int? ContractMethod { get; set; }

            /// <summary>
            /// 服务情况
            /// </summary>  
            public int? ProductServiceInfoStatus { get; set; }

            /// <summary>
            /// 回收状态
            /// </summary>           
            public int? RecoveryStatus { get; set; }

            /// <summary>
            /// 签约日期
            /// </summary> 
            public DateTime? SigningDateStart { get; set; }

            /// <summary>
            /// 签约日期
            /// </summary> 
            public DateTime? SigningDateEnd { get; set; }

            /// <summary>
            /// 创建时间
            /// </summary> 
            public DateTime? CreateDateStart { get; set; }

            /// <summary>
            /// 创建时间
            /// </summary> 
            public DateTime? CreateDateEnd { get; set; }

            /// <summary>
            /// 快捷搜索选项
            /// </summary>
            public EnumQueryListType enumQueryListType { get; set; }

            /// <summary>
            /// 到账
            /// </summary> 
            public DateTime? ArrivalDateStart { get; set; }

            /// <summary>
            /// 归属月份
            /// </summary> 
            public DateTime? ArrivalDateEnd { get; set; }

            /// <summary>
            /// 是否结汇
            /// </summary>           
            public int? IsSettlementExchange { get; set; }

            /// <summary>
            /// 是否有客户编码
            /// </summary>
            public bool? IsHaveContractNum { get; set; }

            /// <summary>
            /// 是否代付款
            /// </summary>           
            public bool? IsBehalfPayment { get; set; }


            public string IsBehalfPaymentCompanyName { get; set; }
        }

        public class SearchContractOtherStampreViewList_In : ApiTableIn
        {
            /// <summary>
            /// 其他盖章审核
            /// </summary>
            public int OtherStampReviewStatus { get; set; }

            /// <summary>
            /// 合同名称
            /// </summary>   
            public string ContractName { get; set; }

            /// <summary>
            /// 甲方公司
            /// </summary>  
            public string FirstPartyName { get; set; }

            /// <summary>
            /// 支付信息
            /// </summary>
            public string PaymentCompanyName { get; set; }

            /// <summary>
            /// 客户编码（合同编码）
            /// </summary>          
            public string ContractNum { get; set; }

            /// <summary>
            /// 签约产品
            /// </summary>          
            public List<string> Product { get; set; }

            /// <summary>
            /// 出单人名称
            /// </summary>
            public List<string> IssuerName { get; set; }

            /// <summary>
            /// 到账情况
            /// </summary>   
            public int? IsReceipt { get; set; }

            /// <summary>
            /// 支付方式
            /// </summary>  
            public int? PaymentMethod { get; set; }

            /// <summary>
            /// 收款公司
            /// </summary> 
            public string CollectingCompany { get; set; }

            /// <summary>
            /// 签约类型
            /// </summary>           
            public int? ContractType { get; set; }

            /// <summary>
            /// 合同状态
            /// </summary>           
            public int? ContractStatus { get; set; }

            /// <summary>
            /// 币种
            /// </summary>           
            public int? Currency { get; set; }

            /// <summary>
            /// 合同类型
            /// </summary>           
            public int? ContractMethod { get; set; }

            /// <summary>
            /// 服务情况
            /// </summary>  
            public int? ProductServiceInfoStatus { get; set; }

            /// <summary>
            /// 回收状态
            /// </summary>           
            public int? RecoveryStatus { get; set; }

            /// <summary>
            /// 签约日期
            /// </summary> 
            public DateTime? SigningDateStart { get; set; }

            /// <summary>
            /// 签约日期
            /// </summary> 
            public DateTime? SigningDateEnd { get; set; }

            /// <summary>
            /// 创建时间
            /// </summary> 
            public DateTime? CreateDateStart { get; set; }

            /// <summary>
            /// 创建时间
            /// </summary> 
            public DateTime? CreateDateEnd { get; set; }

            /// <summary>
            /// 快捷搜索选项
            /// </summary>
            public EnumQueryListType enumQueryListType { get; set; }

            /// <summary>
            /// 到账
            /// </summary> 
            public DateTime? ArrivalDateStart { get; set; }

            /// <summary>
            /// 归属月份
            /// </summary> 
            public DateTime? ArrivalDateEnd { get; set; }

            /// <summary>
            /// 是否结汇
            /// </summary>           
            public int? IsSettlementExchange { get; set; }

            /// <summary>
            /// 是否有客户编码
            /// </summary>
            public bool? IsHaveContractNum { get; set; }

            /// <summary>
            /// 是否代付款
            /// </summary>           
            public bool? IsBehalfPayment { get; set; }


            public string IsBehalfPaymentCompanyName { get; set; }
        }

        public class SearchContractList_Out
        {
            /// <summary>
            /// 主键
            /// </summary>
            public string Id { get; set; }

            /// <summary>
            /// 客户编码
            /// </summary>
            public string ContractNum { get; set; }

            /// <summary>
            /// 合同号
            /// </summary>
            public string ContractNo { get; set; }

            /// <summary>
            /// 合同名称
            /// </summary>
            public string ContractName { get; set; }

            /// <summary>
            /// 签约公司
            /// </summary>
            public string FirstParty { get; set; }

            /// <summary>
            /// 签约公司
            /// </summary>
            public string FirstPartyName { get; set; }

            /// <summary>
            /// 签约类型
            /// </summary>           
            public string ContractTypeName
            {
                get
                {
                    List<Db_sys_dictionary> dictionary = Dictionary.ContractType.Where(e => e.Value == ContractType.ToString()).ToList();
                    if (dictionary.Count > 0)
                    {
                        return dictionary.First().Name;
                    }
                    else
                    {
                        return "";
                    }
                    //return Dictionary.ContractType.First(e => e.Value == ContractType.ToString()).Name;
                }
            }

            /// <summary>
            /// 签约类型
            /// </summary>           
            public int ContractType { get; set; }

            /// <summary>
            /// 合同类型
            /// </summary>           
            public string ContractMethodName
            {
                get
                {
                    List<Db_sys_dictionary> dictionary = Dictionary.ContractMethod.Where(e => e.Value == ContractMethod.ToString()).ToList();
                    if (dictionary.Count > 0)
                    {
                        return dictionary.First().Name;
                    }
                    else
                    {
                        return "";
                    }
                    //return Dictionary.ContractMethod.First(e => e.Value == ContractMethod.ToString()).Name;
                }
            }

            /// <summary>
            /// 合同类型
            /// </summary>           
            public int ContractMethod { get; set; }

            /// <summary>
            /// 审核类型
            /// </summary>           
            public int AuditType { get; set; }

            /// <summary>
            /// 合同状态
            /// </summary>           
            public string ContractStatusName
            {
                get
                {
                    List<Db_sys_dictionary> dictionary = Dictionary.ContractStatus.Where(e => e.Value == ContractStatus.ToString()).ToList();
                    if (dictionary.Count > 0)
                    {
                        return dictionary.First().Name;
                    }
                    else
                    {
                        return "";
                    }
                    //return Dictionary.ContractStatus.First(e => e.Value == ContractStatus.ToString()).Name;
                }
            }

            /// <summary>
            /// 合同状态
            /// </summary>           
            public string ContractStatusMarketingName
            {
                get
                {
                    int Status = ContractStatus;

                    switch (Status)
                    {
                        case (int)EnumContractStatus.InitialAuditPassSubmitOrgRegimentAudit:
                        case (int)EnumContractStatus.InitialAuditPassSubmitOrgBrigadeAudit:
                        case (int)EnumContractStatus.InitialAuditPassSubmitOrgDivisionAudit:
                        //case (int)EnumContractStatus.OrgRegimentAuditPass:
                        case (int)EnumContractStatus.OrgRegimentAuditSubmitOrgBrigadeAudit:
                        case (int)EnumContractStatus.OrgRegimentAuditSubmitOrgDivisionAudit:
                        //case (int)EnumContractStatus.OrgBrigadeAuditPass:
                        case (int)EnumContractStatus.OrgBrigadeAuditSubmitOrgDivisionAudit:
                        //case (int)EnumContractStatus.OrgDivisionAuditPass:
                        case (int)EnumContractStatus.SubmitOrgRegimentAudit:
                        case (int)EnumContractStatus.SubmitOrgBrigadeAudit:
                        case (int)EnumContractStatus.SubmitOrgDivisionAudit:
                        //case (int)EnumContractStatus.SubmitOrgRegimentAuditPass:
                        case (int)EnumContractStatus.SubmitOrgRegimentAuditSubmitOrgBrigadeAudit:
                        case (int)EnumContractStatus.SubmitOrgRegimentAuditSubmitOrgDivisionAudit:
                        //case (int)EnumContractStatus.SubmitOrgBrigadeAuditPass:
                        case (int)EnumContractStatus.SubmitOrgBrigadeAuditSubmitOrgDivisionAudit:
                            //case (int)EnumContractStatus.SubmitOrgDivisionAuditPass:
                            {
                                Status = 200;
                                break;
                            }
                        case (int)EnumContractStatus.OrgRegimentAuditRefuse:
                        case (int)EnumContractStatus.OrgBrigadeAuditRefuse:
                        case (int)EnumContractStatus.OrgDivisionAuditRefuse:
                        case (int)EnumContractStatus.SubmitOrgRegimentAuditRefuse:
                        case (int)EnumContractStatus.SubmitOrgBrigadeAuditRefuse:
                        case (int)EnumContractStatus.SubmitOrgDivisionAuditRefuse:
                            {
                                Status = 13;
                                break;
                            }
                        case (int)EnumContractStatus.OrgRegimentAuditPass:
                        case (int)EnumContractStatus.OrgBrigadeAuditPass:
                        case (int)EnumContractStatus.OrgDivisionAuditPass:
                            {
                                Status = 12;
                                break;
                            }
                        case (int)EnumContractStatus.SubmitOrgRegimentAuditPass:
                        case (int)EnumContractStatus.SubmitOrgBrigadeAuditPass:
                        case (int)EnumContractStatus.SubmitOrgDivisionAuditPass:
                            {
                                Status = 1;
                                break;
                            }
                        default:
                            {
                                Status = ContractStatus;
                                break;
                            }
                    }
                    List<Db_sys_dictionary> dictionary = Dictionary.ContractStatusMarketing.Where(e => e.Value == Status.ToString()).ToList();
                    if (dictionary.Count > 0)
                    {
                        return dictionary.First().Name;
                    }
                    else
                    {
                        return "";
                    }
                    //return Dictionary.ContractStatus.First(e => e.Value == ContractStatus.ToString()).Name;
                }
            }

            /// <summary>
            /// 合同状态
            /// </summary>    
            private int _ContractStatus;
            public int ContractStatus
            {
                get
                {
                    if (AuditType == EnumAuditType.AutomaticReview.ToInt() && _ContractStatus == EnumContractStatus.Pass.ToInt())
                    {
                        return EnumContractStatus.AutoPass.ToInt();
                    }
                    else
                    {
                        return _ContractStatus;
                    }
                }
                set { _ContractStatus = value; }
            }

            /// <summary>
            /// 签约日期
            /// </summary>
            private string _SigningDate;
            public string SigningDate
            {
                get { return Convert.ToDateTime(_SigningDate).ToString("yyyy-MM-dd"); }
                set { _SigningDate = value; }
            }

            /// <summary>
            /// 币种
            /// </summary>           
            public string CurrencyName
            {
                get
                {
                    List<Db_sys_dictionary> dictionary = Dictionary.Currency.Where(e => e.Value == Currency.ToString()).ToList();
                    if (dictionary.Count > 0)
                    {
                        return dictionary.First().Name;
                    }
                    else
                    {
                        return "";
                    }
                    //return Dictionary.Currency.First(e => e.Value == Currency.ToString()).Name;
                }
            }

            /// <summary>
            /// 币种
            /// </summary>           
            public int Currency { get; set; }

            /// <summary>
            /// 合同金额
            /// </summary>
            public decimal ContractAmount { get; set; }

            /// <summary>
            /// 外币合同金额
            /// </summary>
            public decimal FCContractAmount { get; set; }

            /// <summary>
            /// 外币合同金额
            /// </summary>           
            public decimal? ContractAmountConvert { get; set; }

            ///// <summary>
            ///// 签约产品
            ///// </summary>
            //public string ProductName { get; set; }

            /// <summary>
            /// 服务情况
            /// </summary>  
            public string ProductServiceInfoStatusName
            {
                get
                {
                    List<Db_sys_dictionary> dictionary = Dictionary.ServiceStatus.Where(e => e.Value == ProductServiceInfoStatus.ToString()).ToList();
                    if (dictionary.Count > 0)
                    {
                        return dictionary.First().Name;
                    }
                    else
                    {
                        return "";
                    }
                    //return Dictionary.ServiceStatus.First(e => e.Value == ProductServiceInfoStatus.ToString()).Name;
                }
            }

            /// <summary>
            /// 服务情况
            /// </summary>  
            public int ProductServiceInfoStatus { get; set; }

            /// <summary>
            /// 到账情况
            /// </summary>
            public string IsReceiptName
            {
                get
                {
                    List<Db_sys_dictionary> dictionary = Dictionary.IsReceipt.Where(e => e.Value == IsReceipt.ToString()).ToList();
                    if (dictionary.Count > 0)
                    {
                        return dictionary.First().Name;
                    }
                    else
                    {
                        return "";
                    }
                    //return Dictionary.IsReceipt.First(e => e.Value == IsReceipt.ToString()).Name;
                }
            }

            /// <summary>
            /// 到账情况
            /// </summary>
            public int IsReceipt { get; set; }

            /// <summary>
            /// 开票状态
            /// </summary>
            public string IsInvoiceName
            {
                get
                {
                    return IsInvoice == null ? "" : ((EnumContractInvoiceDisplayStatus)IsInvoice).GetEnumDescription().ToString();
                }
            }

            /// <summary>
            /// 开票状态
            /// </summary>
            public int? IsInvoice { get; set; }

            /// <summary>
            /// 回收状态
            /// </summary>           
            public string RecoveryStatusName
            {
                get
                {
                    List<Db_sys_dictionary> dictionary = Dictionary.RecoveryStatus.Where(e => e.Value == RecoveryStatus.ToString()).ToList();
                    if (dictionary.Count > 0)
                    {
                        return dictionary.First().Name;
                    }
                    else
                    {
                        return "";
                    }
                    //return Dictionary.RecoveryStatus.First(e => e.Value == RecoveryStatus.ToString()).Name;
                }
            }

            /// <summary>
            /// 回收状态
            /// </summary>           
            public int RecoveryStatus { get; set; }


            /// <summary>
            /// 使用状态
            /// </summary>           
            public string UsageStatusName
            {
                get
                {
                    if (UsageStatus == BLLModel.Enum.EnumUsageStatus.UnUse.ToInt())
                    {
                        return "";
                    }
                    else
                    {
                        return Dictionary.UsageStatus.First(e => e.Value == UsageStatus.ToString()).Name + "\r\n" + RecoveryTime.Value.ToString("yyyy-MM-dd");
                    }
                }
            }

            /// <summary>
            /// 使用状态
            /// </summary>           
            public int UsageStatus { get; set; }

            /// <summary>
            /// 回收时间
            /// </summary>
            public DateTime? RecoveryTime { get; set; }

            /// <summary>
            /// 出单人
            /// </summary>
            private string _IssuerName;
            public string IssuerName
            {
                get
                {
                    string OrgName = "";
                    if (OrgDivisionName.IsNotNullOrEmpty())
                    {
                        OrgName = OrgName + OrgDivisionName;
                    }
                    if (OrgBrigadeName.IsNotNullOrEmpty())
                    {
                        if (OrgName != "")
                        {
                            OrgName = OrgName + "/";
                        }
                        OrgName = OrgName + OrgBrigadeName;
                    }
                    if (OrgRegimentName.IsNotNullOrEmpty())
                    {
                        if (OrgName != "")
                        {
                            OrgName = OrgName + "/";
                        }
                        OrgName = OrgName + OrgRegimentName;
                    }
                    if (OrgName != "")
                    {
                        OrgName = "(" + OrgName + ")";
                    }
                    return _IssuerName + OrgName;
                }
                set { _IssuerName = value; }
            }

            /// <summary>
            /// 战队
            /// </summary>           
            public string OrgDivisionName { get; set; }

            /// <summary>
            /// 大队
            /// </summary>           
            public string OrgBrigadeName { get; set; }

            /// <summary>
            /// 中队
            /// </summary>           
            public string OrgRegimentName { get; set; }

            /// <summary>
            /// 创建时间
            /// </summary>
            private string _CreateDate;
            public string CreateDate
            {
                //get; set;
                get { return Convert.ToDateTime(_CreateDate).ToString("yyyy-MM-dd HH:mm"); }
                set { _CreateDate = value; }
            }

            /// <summary>
            /// 是否催单
            /// </summary>
            public int IsUrgeRegistration { get; set; }

            /// <summary>
            /// 合同初审审核id
            /// </summary>
            public string ContractInitialAuditId { get; set; }

            /// <summary>
            /// 合同审核id
            /// </summary>
            public string ContractAuditId { get; set; }

            /// <summary>
            /// 是否提交合同变更申请
            /// </summary>
            public bool IsContractChangeInReview { get; set; }

            /// <summary>
            /// 合同模板类型
            /// </summary>
            public int TemplateType { get; set; }

            /// <summary>
            /// 中文模版币种
            /// </summary>
            public string ChineseTemplateCurrency { get; set; }

            /// <summary>
            /// 英文模板币种
            /// </summary>
            public string EnglishTemplateCurrency { get; set; }

            /// <summary>
            /// 是否中英文版
            /// </summary>
            public int IsBothChineseAndEnglish { get; set; }

            /// <summary>
            /// 合同盖章审核id
            /// </summary>
            public string StampreViewAuditId { get; set; }

            /// <summary>
            /// 合同盖章审核状态
            /// </summary>
            public int StampreViewStatus { get; set; }

            /// <summary>
            /// 合同盖章审核状态名称
            /// </summary>
            public string StampreViewStatusName
            {
                get
                {
                    List<Db_sys_dictionary> dictionary = Dictionary.StampReviewStatus.Where(e => e.Value == StampreViewStatus.ToString()).ToList();
                    if (dictionary.Count > 0)
                    {
                        return dictionary.First().Name;
                    }
                    else
                    {
                        return "";
                    }
                }
            }

            /// <summary>
            /// 合同盖章审核状态名称
            /// </summary>
            public bool StampreViewStatusRefuse { get; set; }

            /// <summary>
            /// 合同是否保密
            /// </summary>
            public bool IsSecret { get; set; }

            /// <summary>
            /// 是否代付款
            /// </summary>           
            public bool? IsBehalfPayment { get; set; }

            public string IsBehalfPaymentName
            {
                get
                {

                    if (IsBehalfPayment != null)
                    {
                        return IsBehalfPayment.Value ? "是" : "否";
                    }
                    else
                    {
                        return "";
                    }
                }
            }

            public string IsBehalfPaymentCompanyName { get; set; }

            public string PaymentCompanyName { get; set; }

            public int GrantCouponNum { get; set; }
            /// <summary>
            /// 收款公司
            /// </summary>
            public string CollectingCompany { get; set; }

            /// <summary>
            /// 收款公司
            /// </summary>
            public string CollectingCompanyName { get; set; }
        }

        public class SearchContractStampreViewList_Out : SearchContractList_Out
        {
            public string Issuer { get; set; }
            /// <summary>
            /// 盖章审核
            /// </summary>
            public int StampReviewStatus { get; set; }

            /// <summary>
            /// 盖章审核
            /// </summary>           
            public string StampReviewStatusName
            {
                get
                {

                    return Dictionary.StampReviewStatus.First(e => e.Value == StampReviewStatus.ToString()).Name;

                }
            }

            /// <summary>
            /// 到账
            /// </summary> 
            public string ArrivalDate { get; set; }

            /// <summary>
            /// Desc:是否结汇
            /// Default:
            /// Nullable:True
            /// </summary>           
            public int? IsSettlementExchange { get; set; }

            /// <summary>
            /// Desc:是否结汇
            /// Default:
            /// Nullable:True
            /// </summary>           
            public string IsSettlementExchangeName
            {
                get
                {
                    List<Db_sys_dictionary> dictionary = Dictionary.IsSettlementExchange.Where(e => e.Value == IsSettlementExchange.ToString()).ToList();
                    if (dictionary.Count > 0)
                    {
                        return dictionary.First().Name;
                    }
                    else
                    {
                        return "";
                    }
                }
            }
        }

        public class SearchContractOtherStampreViewList_Out : SearchContractList_Out
        {
            public string Issuer { get; set; }
            /// <summary>
            /// 盖章审核
            /// </summary>
            public int OtherStampReviewStatus { get; set; }

            /// <summary>
            /// 盖章审核
            /// </summary>           
            public string OtherStampReviewStatusName
            {
                get
                {

                    return Dictionary.StampReviewStatus.First(e => e.Value == OtherStampReviewStatus.ToString()).Name;

                }
            }

            /// <summary>
            /// 到账
            /// </summary> 
            public string ArrivalDate { get; set; }

            /// <summary>
            /// Desc:是否结汇
            /// Default:
            /// Nullable:True
            /// </summary>           
            public int? IsSettlementExchange { get; set; }

            /// <summary>
            /// Desc:是否结汇
            /// Default:
            /// Nullable:True
            /// </summary>           
            public string IsSettlementExchangeName
            {
                get
                {
                    List<Db_sys_dictionary> dictionary = Dictionary.IsSettlementExchange.Where(e => e.Value == IsSettlementExchange.ToString()).ToList();
                    if (dictionary.Count > 0)
                    {
                        return dictionary.First().Name;
                    }
                    else
                    {
                        return "";
                    }
                }
            }
        }

        public class ContractStampreViewList_Out
        {
            /// <summary>
            /// 主键
            /// </summary>
            public string Id { get; set; }

            /// <summary>
            /// 合同表id
            /// </summary>           
            public string ContractId { get; set; }

            /// <summary>
            /// 申请人id
            /// </summary>           
            public string ApplicantId { get; set; }

            /// <summary>
            /// 申请人名称
            /// </summary>           
            public string ApplicantName { get; set; }

            /// <summary>
            /// 申请时间
            /// </summary>           
            public DateTime? ApplicantDate { get; set; }

            /// <summary>
            /// 状态
            /// </summary>           
            public int? Status { get; set; }

            /// <summary>
            /// 盖章审核
            /// </summary>           
            public string StatusName
            {
                get
                {
                    List<Db_sys_dictionary> dictionary = Dictionary.StampReviewStatus.Where(e => e.Value == Status.ToString()).ToList();
                    if (dictionary.Count > 0)
                    {
                        return dictionary.First().Name;
                    }
                    else
                    {
                        return "";
                    }
                }
            }

            /// <summary>
            /// 审核人id
            /// </summary>           
            public string ReviewerId { get; set; }

            /// <summary>
            /// 审核人名称
            /// </summary>           
            public string ReviewerName { get; set; }

            /// <summary>
            /// 审核时间
            /// </summary>           
            public DateTime? ReviewerDate { get; set; }

            /// <summary>
            /// 审核反馈意见
            /// </summary>           
            public string Feedback { get; set; }

        }

        public class ContractOtherStampreViewList_Out
        {
            /// <summary>
            /// 主键
            /// </summary>
            public string Id { get; set; }

            /// <summary>
            /// 合同表id
            /// </summary>           
            public string ContractId { get; set; }

            /// <summary>
            /// 申请人id
            /// </summary>           
            public string ApplicantId { get; set; }

            /// <summary>
            /// 申请人名称
            /// </summary>           
            public string ApplicantName { get; set; }

            /// <summary>
            /// 申请时间
            /// </summary>           
            public DateTime? ApplicantDate { get; set; }

            /// <summary>
            /// 状态
            /// </summary>           
            public int? Status { get; set; }

            /// <summary>
            /// 盖章审核
            /// </summary>           
            public string StatusName
            {
                get
                {
                    List<Db_sys_dictionary> dictionary = Dictionary.StampReviewStatus.Where(e => e.Value == Status.ToString()).ToList();
                    if (dictionary.Count > 0)
                    {
                        return dictionary.First().Name;
                    }
                    else
                    {
                        return "";
                    }
                }
            }

            /// <summary>
            /// 审核人id
            /// </summary>           
            public string ReviewerId { get; set; }

            /// <summary>
            /// 审核人名称
            /// </summary>           
            public string ReviewerName { get; set; }

            /// <summary>
            /// 审核时间
            /// </summary>           
            public DateTime? ReviewerDate { get; set; }

            /// <summary>
            /// 审核反馈意见
            /// </summary>           
            public string Feedback { get; set; }

        }

        public class RecoveryContract_In
        {
            /// <summary>
            /// 主键
            /// </summary>
            [Required(ErrorMessage = "合同主键不可为空")]
            public string Id { get; set; }

            [Required(ErrorMessage = "回收状态不可为空")]
            public int RecoveryStatus { get; set; }

            [Required(ErrorMessage = "回收时间不可为空")]
            public DateTime? RecoveryTime { get; set; }

            public string RecyclingRemarks { get; set; }
        }

        public class ConfirmContract_In
        {
            /// <summary>
            /// 主键
            /// </summary>
            [Required(ErrorMessage = "合同主键不可为空")]
            public string Id { get; set; }

            [Required(ErrorMessage = "确认状态不可为空")]
            public int IsConfirm { get; set; }
        }


        public class AddContractProductServiceInfoGtisAppl_In
        {
            [Required(ErrorMessage = "合同产品信息表id不可为空")]
            public string ContractProductInfoId { get; set; }

            [Required(ErrorMessage = "主账号数量不可为空")]
            public int PrimaryAccountsNum { get; set; }

            [Required(ErrorMessage = "子账号数量不可为空")]
            public int SubAccountsNum { get; set; }

            [Required(ErrorMessage = "每个账户共享人数不可为空")]
            public int SharePeopleNum { get; set; }

            //[Required(ErrorMessage = "共享使用总数不可为空")]
            public int? ShareUsageNum { get; set; }

            //[Required(ErrorMessage = "子账号授权国家次数不可为空")]
            public int? AuthorizationNum { get; set; }

            [Required(ErrorMessage = "服务周期-开始时间不可为空")]
            public DateTime? ServiceCycleStart { get; set; }

            [Required(ErrorMessage = "服务周期-结束时间不可为空")]
            public DateTime? ServiceCycleEnd { get; set; }

            //[Required(ErrorMessage = "常驻国家不可为空")]
            //public List<GtisApplResidentCountry>? GtisApplResidentCountry { get; set; }

            //[Required(ErrorMessage = "常驻城市不可为空")]
            //public List<GtisApplResidentCity>? GtisApplResidentCity { get; set; }

            [Required(ErrorMessage = "零售国家不可为空")]
            public int RetailCountry { get; set; }

            //[Required(ErrorMessage = "备注不可为空")]
            public string Remark { get; set; }

            [Required(ErrorMessage = "环球慧思申请_开通国家不可为空")]
            public List<GtisApplCountry> GtisApplCountry { get; set; }

            [Required(ErrorMessage = "服务月份不可为空")]
            public int? ServiceMonth { get; set; }
            /// <summary>
            /// （续约）甲方公司
            /// </summary>
            public string RenewFirstParty { get; set; }
            /// <summary>
            /// (续约)客户编码
            /// </summary>
            public string RenewContractNum { get; set; }
            /// <summary>
            /// 优惠类型
            /// </summary>
            public int DiscountType { get; set; }
            /// <summary>
            /// 选中的优惠券Id列表
            /// </summary>
            public List<string>? CounponDetailIdList { get; set; }
            /// <summary>
            /// 优惠后服务月份
            /// </summary>
            public int? ServiceMonthAfterDiscount { get; set; }
            /// <summary>
            /// 优惠后服务开始时间
            /// </summary>
            public DateTime? ServiceCycleStartAfterDiscount { get; set; }
            /// <summary>
            /// 优惠后服务结束时间
            /// </summary>
            public DateTime? ServiceCycleEndAfterDiscount { get; set; }
            /// <summary>
            /// 延长的总服务天数
            /// </summary>
            public int? PerlongServiceDays { get; set; }
            /// <summary>
            /// 赠送的个人服务天数
            /// </summary>
            public int? PrivateServiceDays { get; set; }
            /// <summary>
            /// 需自费的个人服务天数
            /// </summary>
            public int? OverServiceDays { get; set; }
            /// <summary>
            /// 加急处理状态
            /// </summary>
            public bool IsUrgent { get; set; }
            /// <summary>
            /// 禁用下载/导出权限
            /// </summary>
            public bool ForbidSearchExport { get; set; }
            /// <summary>
            /// 是否开通定制报告
            /// </summary>
            public bool WordRptPermissions { get; set; }
            /// <summary>
            /// 定制报告每年总次数
            /// </summary>
            public int? WordRptMaxTimes { get; set; }
            /// <summary>
            /// 环球搜账号数量(主+子)
            /// </summary>
            public int? GlobalSearchAccountCount { get; set; }
        }


        public class GtisApplResidentCountry
        {
            [Required(ErrorMessage = "常驻国家id不可为空")]
            public int ResidentCountry { get; set; }
        }

        public class GtisApplResidentCity
        {
            [Required(ErrorMessage = "常驻城市id不可为空")]
            public int ResidentCity { get; set; }
        }

        public class GtisApplCountry
        {
            [Required(ErrorMessage = "国家id不可为空")]
            public int Sid { get; set; }
        }

        public class ContractProductServiceInfoGtisAppl_Out
        {
            public string Id { get; set; }

            public string ContractProductInfoId { get; set; }

            public int PrimaryAccountsNum { get; set; }

            public int SubAccountsNum { get; set; }

            public int SharePeopleNum { get; set; }

            public int ShareUsageNum { get; set; }

            public int AuthorizationNum { get; set; }

            public DateTime? ServiceCycleStart { get; set; }

            public DateTime? ServiceCycleEnd { get; set; }

            public List<GtisApplResidentCountry_Out> GtisApplResidentCountry { get; set; }

            public List<GtisApplResidentCity_Out> GtisApplResidentCity { get; set; }

            public int RetailCountry { get; set; }

            public string Remark { get; set; }

            public List<GtisApplCountry_Out> GtisApplCountry { get; set; }

            /// <summary>
            /// Desc:服务月份
            /// Default:
            /// Nullable:True
            public int? ServiceMonth { get; set; }
        }

        public class GtisApplResidentCountry_Out
        {
            public string Id { get; set; }

            public int ResidentCountry { get; set; }
        }

        public class GtisApplResidentCity_Out
        {
            public string Id { get; set; }

            public int ResidentCity { get; set; }
        }

        public class GtisApplCountry_Out
        {
            public string Id { get; set; }

            public int Sid { get; set; }
        }

        public class AddContractProductServiceInfoDBAppl_In
        {
            [Required(ErrorMessage = "合同产品信息表id不可为空")]
            public string ContractProductInfoId { get; set; }

            [Required(ErrorMessage = "服务周期-开始时间不可为空")]
            public DateTime? ServiceCycleStart { get; set; }

            [Required(ErrorMessage = "服务周期-结束时间不可为空")]
            public DateTime? ServiceCycleEnd { get; set; }

            //[Required(ErrorMessage = "备注不可为空")]
            public string Remark { get; set; }

            /* 此处改为审核人填写 2024-6-13修改
            [Required(ErrorMessage = "DBHOOOVERSContractNo不可为空")]
            public string DBHOOOVERSContractNo { get; set; }*/

            [Required(ErrorMessage = "CustomerCompanyName不可为空")]
            public string CustomerCompanyName { get; set; }

            [Required(ErrorMessage = "CustomerContactName不可为空")]
            public string CustomerContactName { get; set; }

            [Required(ErrorMessage = "CustomerContactEmailAddress不可为空")]
            public string CustomerContactEmailAddress { get; set; }

            [Required(ErrorMessage = "UsersNo不可为空")]

            public int? UsersNo { get; set; }

            [Required(ErrorMessage = "SubscriptionPeriod不可为空")]
            public string SubscriptionPeriod { get; set; }

            [Required(ErrorMessage = "服务月份不可为空")]
            public int? ServiceMonth { get; set; }

            /// <summary>
            /// SITEID
            /// </summary>
            public List<string> SITEIDs { get; set; }

            //public List<AddContractProductServiceInfoDBAppl_In_Sub> SubInfoList { get; set; }
            /// <summary>
            /// 是否加急处理
            /// </summary>
            public bool IsUrgent { get; set; }
        }

        public class AddContractProductServiceInfoDBAppl_In_Sub
        {
            [Required(ErrorMessage = "CustomerCompanyName不可为空")]
            public string CustomerCompanyName { get; set; }

            [Required(ErrorMessage = "CustomerContactName不可为空")]
            public string CustomerContactName { get; set; }

            [Required(ErrorMessage = "CustomerContactEmailAddress不可为空")]
            public string CustomerContactEmailAddress { get; set; }

            [Required(ErrorMessage = "UsersNo不可为空")]

            public int? UsersNo { get; set; }

            public string SITEID { get; set; }
        }

        public class ContractProductServiceInfoDBAppl_Out
        {
            public string Id { get; set; }

            public string ContractProductInfoId { get; set; }

            public DateTime? ServiceCycleStart { get; set; }

            public DateTime? ServiceCycleEnd { get; set; }

            //[Required(ErrorMessage = "备注不可为空")]
            public string Remark { get; set; }

            public string DBHOOOVERSContractNo { get; set; }

            public string CustomerCompanyName { get; set; }

            public string CustomerContactName { get; set; }

            public string CustomerContactEmailAddress { get; set; }

            public int? UsersNo { get; set; }

            public string SubscriptionPeriod { get; set; }

            /// <summary>
            /// Desc:服务月份
            /// Default:
            /// Nullable:True
            public int? ServiceMonth { get; set; }
        }

        public class AddContractProductServiceInfoCollegeAppl_In
        {
            [Required(ErrorMessage = "合同产品信息表id不可为空")]
            public string ContractProductInfoId { get; set; }

            //[Required(ErrorMessage = "主账号数量不可为空")]
            public int? PrimaryAccountsNum { get; set; }

            [Required(ErrorMessage = "服务周期-开始时间不可为空")]
            public DateTime? ServiceCycleStart { get; set; }

            [Required(ErrorMessage = "服务周期-结束时间不可为空")]
            public DateTime? ServiceCycleEnd { get; set; }

            //[Required(ErrorMessage = "备注不可为空")]
            public string Remark { get; set; }

            [Required(ErrorMessage = "慧思学院申请联系人不可为空")]
            public List<CollegeApplContacts> CollegeApplContacts { get; set; }

            [Required(ErrorMessage = "服务月份不可为空")]
            public int? ServiceMonth { get; set; }
            /// <summary>
            /// 是否加急处理
            /// </summary>
            public bool IsUrgent { get; set; }
        }

        public class CollegeApplContacts
        {
            [Required(ErrorMessage = "姓名不可为空")]
            public string Name { get; set; }

            [Required(ErrorMessage = "手机号不可为空")]
            public string Phone { get; set; }
        }

        public class ContractProductServiceInfoCollegeAppl_Out
        {
            public string Id { get; set; }

            public string ContractProductInfoId { get; set; }

            public int PrimaryAccountsNum { get; set; }

            public DateTime? ServiceCycleStart { get; set; }

            public DateTime? ServiceCycleEnd { get; set; }

            public string Remark { get; set; }

            public List<CollegeApplContacts_Out> CollegeApplContacts { get; set; }

            /// <summary>
            /// Desc:服务月份
            /// Default:
            /// Nullable:True
            public int? ServiceMonth { get; set; }
        }

        public class CollegeApplContacts_Out
        {
            public string Id { get; set; }

            public string Name { get; set; }

            public string Phone { get; set; }
        }

        public class AddContractProductServiceInfoGlobalSearchAppl_In
        {
            [Required(ErrorMessage = "合同产品信息表id不可为空")]
            public string ContractProductInfoId { get; set; }

            [Required(ErrorMessage = "主账号数量不可为空")]
            public int PrimaryAccountsNum { get; set; }

            [Required(ErrorMessage = "子账号数量不可为空")]
            public int SubAccountsNum { get; set; }

            [Required(ErrorMessage = "服务周期-开始时间不可为空")]
            public DateTime? ServiceCycleStart { get; set; }

            [Required(ErrorMessage = "服务周期-结束时间不可为空")]
            public DateTime? ServiceCycleEnd { get; set; }

            //[Required(ErrorMessage = "备注不可为空")]
            public string Remark { get; set; }

            [Required(ErrorMessage = "服务月份不可为空")]
            public int? ServiceMonth { get; set; }
            /// <summary>
            /// 被续约的甲方公司
            /// </summary>
            public string OldFirstParty { get; set; }
            /// <summary>
            /// 被续约的客户编码
            /// </summary>
            public string RenewContractNum { get; set; }
            /// <summary>
            /// 是否加急处理
            /// </summary>
            public bool IsUrgent { get; set; }
        }

        public class ContractProductServiceInfoGlobalSearchAppl_Out
        {
            public string Id { get; set; }

            public string ContractProductInfoId { get; set; }

            public int PrimaryAccountsNum { get; set; }

            public int SubAccountsNum { get; set; }

            public DateTime? ServiceCycleStart { get; set; }

            public DateTime? ServiceCycleEnd { get; set; }

            public string Remark { get; set; }

            /// <summary>
            /// Desc:服务月份
            /// Default:
            /// Nullable:True
            public int? ServiceMonth { get; set; }
        }

        public class AddContractProductServiceInfoOtherDataAppl_In
        {
            [Required(ErrorMessage = "合同产品信息表id不可为空")]
            public string ContractProductInfoId { get; set; }

            [Required(ErrorMessage = "服务周期-开始时间不可为空")]
            public DateTime? ServiceCycleStart { get; set; }

            [Required(ErrorMessage = "服务周期-结束时间不可为空")]
            public DateTime? ServiceCycleEnd { get; set; }

            //[Required(ErrorMessage = "备注不可为空")]
            public string Remark { get; set; }

            [Required(ErrorMessage = "编码数量不可为空")]
            public int CodesNum { get; set; }

            [Required(ErrorMessage = "其他数据申请海关编码不可为空")]
            public List<OtherDataApplHsCode> OtherDataApplHsCodes { get; set; }

            [Required(ErrorMessage = "服务月份不可为空")]
            public int? ServiceMonth { get; set; }
            /// <summary>
            /// 是否加急处理
            /// </summary>
            public bool IsUrgent { get; set; }
        }

        public class OtherDataApplHsCode
        {
            [Required(ErrorMessage = "国家ID不可为空")]
            public int SID { get; set; }

            [Required(ErrorMessage = "国家不可为空")]
            public string CountryName { get; set; }

            [Required(ErrorMessage = "进出口类型不可为空")]
            public int IAEType { get; set; }

            [Required(ErrorMessage = "海关编码不可为空")]
            public string HsCode { get; set; }

            [Required(ErrorMessage = "时段开始不可为空")]
            public string TimePeriodStart { get; set; }

            [Required(ErrorMessage = "时段结束不可为空")]
            public string TimePeriodEnd { get; set; }
        }

        public class ContractProductServiceInfoOtherDataAppl_Out
        {
            public string Id { get; set; }

            public string ContractProductInfoId { get; set; }

            public DateTime? ServiceCycleStart { get; set; }

            public DateTime? ServiceCycleEnd { get; set; }

            public string Remark { get; set; }

            public int CodesNum { get; set; }

            public List<OtherDataApplHsCode_Out> OtherDataApplHsCodes { get; set; }

            /// <summary>
            /// Desc:服务月份
            /// Default:
            /// Nullable:True
            public int? ServiceMonth { get; set; }
        }

        public class OtherDataApplHsCode_Out
        {
            public string Id { get; set; }

            public int SID { get; set; }

            public string CountryName { get; set; }

            public int IAEType { get; set; }

            public string HsCode { get; set; }

            public string TimePeriodStart { get; set; }

            public string TimePeriodEnd { get; set; }
        }

        public class AddContractProductServiceInfoProjectAppl_In
        {
            [Required(ErrorMessage = "合同产品信息表id不可为空")]
            public string ContractProductInfoId { get; set; }

            [Required(ErrorMessage = "服务周期-开始时间不可为空")]
            public DateTime? ServiceCycleStart { get; set; }

            [Required(ErrorMessage = "服务周期-结束时间不可为空")]
            public DateTime? ServiceCycleEnd { get; set; }

            //[Required(ErrorMessage = "备注不可为空")]
            public string Remark { get; set; }

            [Required(ErrorMessage = "套数不可为空")]
            public int CaseNum { get; set; }

            [Required(ErrorMessage = "期数不可为空")]
            public int PeriodsNum { get; set; }

            [Required(ErrorMessage = "费用类型不可为空")]
            public int ExpenseType { get; set; }

            [Required(ErrorMessage = "自费金额不可为空")]
            public decimal SelfExpenseAmount { get; set; }

            [Required(ErrorMessage = "收件人不可为空")]
            public string Addressee { get; set; }

            [Required(ErrorMessage = "联系方式不可为空")]
            public string ContactInformation { get; set; }

            [Required(ErrorMessage = "国家不可为空")]
            public int Country { get; set; }

            [Required(ErrorMessage = "省不可为空")]
            public int? Province { get; set; }

            [Required(ErrorMessage = "市不可为空")]
            public int? City { get; set; }

            [Required(ErrorMessage = "详细地址不可为空")]
            public string DetailedAddress { get; set; }

            //[Required(ErrorMessage = "服务月份不可为空")]
            public int? ServiceMonth { get; set; }
        }

        public class ContractProductServiceInfoProjectAppl_Out
        {
            public string Id { get; set; }

            public string ContractProductInfoId { get; set; }

            public DateTime? ServiceCycleStart { get; set; }

            public DateTime? ServiceCycleEnd { get; set; }

            public string Remark { get; set; }

            public int CaseNum { get; set; }

            public int PeriodsNum { get; set; }

            public int ExpenseType { get; set; }

            public decimal SelfExpenseAmount { get; set; }

            public string Addressee { get; set; }

            public string ContactInformation { get; set; }

            public int Country { get; set; }

            public int Province { get; set; }

            public int City { get; set; }

            public string DetailedAddress { get; set; }

            /// <summary>
            /// Desc:服务月份
            /// Default:
            /// Nullable:True
            public int? ServiceMonth { get; set; }
        }

        public class ContractPaymentInfo_Out
        {
            public string Id { get; set; }

            public string ContractId { get; set; }

            /// <summary>
            /// 是否分期
            /// </summary>           
            public int IsStage { get; set; }

            /// <summary>
            /// 是否到账
            /// </summary>
            public int IsReceipt { get; set; }

            /// <summary>
            /// 是否到账
            /// </summary>
            public string IsReceiptName
            {
                get
                {
                    List<Db_sys_dictionary> dictionary = Dictionary.IsReceipt.Where(e => e.Value == IsReceipt.ToString()).ToList();
                    if (dictionary.Count > 0)
                    {
                        return dictionary.First().Name;
                    }
                    else
                    {
                        return "";
                    }
                    //return Dictionary.IsReceipt.First(e => e.Value == IsReceipt.ToString()).Name;
                }
            }

            /// <summary>
            /// 款项类型
            /// </summary>
            public int? PaymentType { get; set; }

            /// <summary>
            /// 款项类型
            /// </summary>
            public string PaymentTypeName
            {
                get
                {
                    List<Db_sys_dictionary> dictionary = Dictionary.PaymentType.Where(e => e.Value == PaymentType.ToString()).ToList();
                    if (dictionary.Count > 0)
                    {
                        return dictionary.First().Name;
                    }
                    else
                    {
                        return "";
                    }
                    //return Dictionary.PaymentType.First(e => e.Value == PaymentType.ToString()).Name;
                }
            }

            /// <summary>
            /// 计划到账日期
            /// </summary>
            private string _PlannedArrivalDate;
            public string PlannedArrivalDate
            {
                get
                {
                    return _PlannedArrivalDate == null ? _PlannedArrivalDate : Convert.ToDateTime(_PlannedArrivalDate).ToString("yyyy-MM-dd");
                }
                set { _PlannedArrivalDate = value; }
            }

            /// <summary>
            /// 预计到账金额
            /// </summary>
            public decimal PlannedArrivalAmount { get; set; }

            /// <summary>
            /// 支付公司
            /// </summary>
            public string PaymentCompany { get; set; }

            /// <summary>
            /// 支付公司
            /// </summary>
            public string PaymentCompanyName { get; set; }

            /// <summary>
            /// 收款公司
            /// </summary>
            public string CollectingCompany { get; set; }

            /// <summary>
            /// 收款公司
            /// </summary>
            public string CollectingCompanyName { get; set; }

            /// <summary>
            /// 币种
            /// </summary>
            public int? Currency { get; set; }

            /// <summary>
            /// 币种
            /// </summary>
            public string CurrencyName
            {
                get
                {
                    List<Db_sys_dictionary> dictionary = Dictionary.Currency.Where(e => e.Value == Currency.ToString()).ToList();
                    if (dictionary.Count > 0)
                    {
                        return dictionary.First().Name;
                    }
                    else
                    {
                        return "";
                    }
                    //return Dictionary.Currency.First(e => e.Value == Currency.ToString()).Name;
                }
            }

            /// <summary>
            /// 到账金额
            /// </summary>
            public decimal ArrivalAmount { get; set; }

            /// <summary>
            /// 到账日期
            /// </summary>
            private string _ArrivalDate;
            public string ArrivalDate
            {
                get
                {
                    return _ArrivalDate == null ? _ArrivalDate : Convert.ToDateTime(_ArrivalDate).ToString("yyyy-MM-dd");
                }
                set { _ArrivalDate = value; }
            }

            /// <summary>
            /// 支付方式
            /// </summary>
            public int? PaymentMethod { get; set; }

            /// <summary>
            /// 支付方式
            /// </summary>
            public string PaymentMethodName
            {
                get
                {
                    List<Db_sys_dictionary> dictionary = Dictionary.PaymentMethod.Where(e => e.Value == PaymentMethod.ToString()).ToList();
                    if (dictionary.Count > 0)
                    {
                        return dictionary.First().Name;
                    }
                    else
                    {
                        return "";
                    }
                    //return Dictionary.PaymentMethod.First(e => e.Value == PaymentMethod.ToString()).Name;
                }
            }

            /// <summary>
            /// 银行支付金额
            /// </summary>
            public decimal BankPaymentAmount { get; set; }

            /// <summary>
            /// 现金支付金额
            /// </summary>
            public decimal CashPaymentAmount { get; set; }

            /// <summary>
            /// 支付凭证
            /// </summary>
            public List<BM_FileInfo> PaymentVoucher { get; set; }

            /// <summary>
            /// 创建人
            /// </summary>
            public string CreateUser { get; set; }

            /// <summary>
            /// 创建时间
            /// </summary>
            public DateTime? CreateDate { get; set; }

            private decimal? _RemainingAmountReceived;
            /// <summary>
            /// 剩余到账金额
            /// </summary>
            public decimal? RemainingAmountReceived
            {
                get
                {
                    return _RemainingAmountReceived == null ? 0 : _RemainingAmountReceived.Value;
                }
                set
                {
                    _RemainingAmountReceived = value;
                }
            }

            /// <summary>
            /// 已到账金额
            /// </summary>
            public decimal? ReceivedAmount { get; set; }

            public string CashSourceRemarks { get; set; }

            public bool? IsBehalfPayment { get; set; }

            public string BehalfPaymentName { get; set; }

            /// <summary>
            /// 联系人
            /// </summary>           
            public string PaymentContacts { get; set; }

            /// <summary>
            /// 职务
            /// </summary>           
            public string PaymentJob { get; set; }

            /// <summary>
            /// 联系方式
            /// </summary>           
            public string PaymentContactWay { get; set; }

            /// <summary>
            /// 电子邮箱
            /// </summary>           
            public string PaymentEmail { get; set; }

            /// <summary>
            /// 固定电话
            /// </summary>           
            public string PaymentTelephone { get; set; }

            /// <summary>
            /// 传真号码
            /// </summary>           
            public string PaymentFax { get; set; }

            /// <summary>
            /// 邮编
            /// </summary>           
            public string PaymentPostalCode { get; set; }

            /// <summary>
            /// 详细地址-国家
            /// </summary>           
            public int? PaymentCountry { get; set; }

            /// <summary>
            /// 详细地址-省
            /// </summary>           
            public int? PaymentProvince { get; set; }

            /// <summary>
            /// 详细地址-市
            /// </summary>           
            public int? PaymentCity { get; set; }

            /// <summary>
            /// 详细地址-详细地址
            /// </summary>           
            public string PaymentAddress { get; set; }
        }

        public class ProductInfo_Out
        {
            public string Id { get; set; }
            /// <summary>
            /// 合同表id
            /// </summary>
            public string ContractId { get; set; }
            /// <summary>
            /// 产品表id
            /// </summary>
            public string ProductId { get; set; }
            /// <summary>
            /// 产品名称
            /// </summary>
            public string ProductName { get; set; }
            /// <summary>
            /// 产品英文名称
            /// </summary>
            public string ProductNameEN { get; set; }
            /// <summary>
            /// 产品描述
            /// </summary>
            public string ProductDescription { get; set; }
            /// <summary>
            /// 产品英文描述
            /// </summary>
            public string ProductDescriptionEn { get; set; }
            /// <summary>
            /// 产品编号
            /// 2025年6月16日 修改
            /// </summary>
            public string ProductNum { get; set; }
            /// <summary>
            /// 币种
            /// </summary>
            public int Currency { get; set; }
            /// <summary>
            /// 币种
            /// </summary>
            public string CurrencyName
            {
                get
                {
                    List<Db_sys_dictionary> dictionary = Dictionary.Currency.Where(e => e.Value == Currency.ToString()).ToList();
                    if (dictionary.Count > 0)
                    {
                        return dictionary.First().Name;
                    }
                    else
                    {
                        return "";
                    }
                    //return Dictionary.Currency.First(e => e.Value == Currency.ToString()).Name;
                }
            }
            /// <summary>
            /// 售价
            /// </summary>
            public decimal Price { get; set; }
            /// <summary>
            /// 计费参数
            /// </summary>
            public int ChargingParameters { get; set; }
            /// <summary>
            /// 计费参数
            /// </summary>
            public string ChargingParametersName
            {
                get
                {
                    List<Db_sys_dictionary> dictionary = Dictionary.ChargingParameters.Where(e => e.Value == ChargingParameters.ToString()).ToList();
                    if (dictionary.Count > 0)
                    {
                        return dictionary.First().Name;
                    }
                    else
                    {
                        return "";
                    }
                    //return Dictionary.ChargingParameters.First(e => e.Value == ChargingParameters.ToString()).Name;
                }
            }
            /// <summary>
            /// 币种价格周期
            /// </summary>
            public string CurrencyPrice
            {
                get
                {
                    return Price.ToString("N2") + " " + Currency.ToEnum<EnumCurrencyLite>().GetEnumDescription() + " " + ChargingParametersName.Replace("每", "/");
                }
            }
            /// <summary>
            /// 产品类型
            /// </summary>
            public int ProductType { get; set; }
            /// <summary>
            /// 开通月数
            /// </summary>
            public int? OpeningMonths { get; set; }
            /// <summary>
            /// 首次开通月数
            /// </summary>
            public int? FirstOpeningMonths { get; set; }
            /// <summary>
            /// 开通年数
            /// </summary>
            public int? OpeningYears { get; set; }
            /// <summary>
            /// 国家
            /// </summary>
            public string Countrys { get; set; }
            /// <summary>
            /// 主账号个数
            /// </summary>
            public int? PrimaryAccountsNum { get; set; }
            /// <summary>
            /// 子账号个数
            /// </summary>
            public int? SubAccountsNum { get; set; }
            /// <summary>
            /// 编码个数
            /// </summary>
            public int? CodesNum { get; set; }
            /// <summary>
            /// 套数
            /// </summary>
            public int? SetNum { get; set; }
            /// <summary>
            /// 期数
            /// </summary>
            public int? PeriodsNum { get; set; }
            /// <summary>
            /// 超级子账号数
            /// </summary>           
            public int? SuperSubAccountNum { get; set; }
            /// <summary>
            /// 定制报告下载次数
            /// </summary>           
            public int? CustomizedReportDownloadsNum { get; set; }
            /// <summary>
            /// 创建人
            /// </summary>
            public string CreateUser { get; set; }
            /// <summary>
            /// 创建时间
            /// </summary>
            public DateTime? CreateDate { get; set; }
            /// <summary>
            /// 申请id
            /// </summary>
            public string ProductServiceInfoApplId { get; set; }
            /// <summary>
            /// 申请状态
            /// </summary>
            public int? ProductServiceInfoApplState { get; set; }
            /// <summary>
            /// 申请状态
            /// </summary>
            public string ProductServiceInfoApplStateName
            {
                get
                {
                    return ProductServiceInfoApplState == null ? null : Dictionary.ProcessStatus.First(e => e.Value == ProductServiceInfoApplState.ToString()).Name;
                }
            }
            /// <summary>
            /// 申请类型
            /// </summary>
            public int? ProductServiceInfoApplProcessingType { get; set; }
            /// <summary>
            /// 申请类型
            /// </summary>
            public string ProductServiceInfoApplProcessingTypeName
            {
                get
                {
                    return ProductServiceInfoApplProcessingType == null ? null : Dictionary.ProcessingType.First(e => e.Value == ProductServiceInfoApplProcessingType.ToString()).Name;
                }
            }
            /// <summary>
            /// 服务状态
            /// </summary>
            public int? ServiceState { get; set; }
            /// <summary>
            /// 服务状态
            /// </summary>
            public string ServiceStateName
            {
                get
                {
                    return ServiceState == null ? null : ((EnumContractServiceState)ServiceState).GetEnumDescription();
                }
            }
            /// <summary>
            /// 服务周期
            /// </summary>
            public int? ServiceCycle { get; set; }
            /// <summary>
            /// 周期月份最短
            /// </summary>           
            public int? ServiceCycleStart { get; set; }
            /// <summary>
            /// 周期月份最长
            /// </summary>           
            public int? ServiceCycleEnd { get; set; }
            /// <summary>
            /// 产品价格Id
            /// </summary>           
            public string ProductPriceId { get; set; }
            /// <summary>
            /// 产品价格
            /// </summary>           
            public string ProductPrice { get; set; }
            /// <summary>
            /// 子账号价格id
            /// </summary>
            public string SubAccountsProductPriceId { get; set; }
            /// <summary>
            /// 子账号价格
            /// </summary>
            public decimal? SubAccountsProductPrice { get; set; }
            /// <summary>
            /// 价格
            /// </summary>           
            public decimal ContractProductinfoPrice { get; set; }
            /// <summary>
            /// 价格
            /// </summary>           
            public decimal ContractProductinfoPriceTotal { get; set; }
            /// <summary>
            /// 开通月数
            /// </summary>
            public DateTime? ProtectionDeadline
            {
                get
                {
                    if (ProductType == 7)
                    {
                        DateTime result = DateTime.Now.AddMonths(PeriodsNum.Value);
                        return new DateTime(result.Year, result.Month, DateTime.DaysInMonth(result.Year, result.Month));
                    }
                    else if (ProductType == 9)
                    {
                        return null;
                    }
                    else if (ProductType == 1 && ProductId == Guid.Empty.ToString())
                    {
                        return null;
                    }
                    else
                    {
                        return DateTime.Now.AddMonths(OpeningMonths.Value);
                    }
                }
            }
            /// <summary>
            /// 产品表id(组合产品的上级节点)
            /// </summary>           
            public string ParentProductId { get; set; }
            /// <summary>
            /// 产品名称(组合产品的上级节点)
            /// </summary>           
            public string ParentProductName { get; set; }
            /// <summary>
            /// 产品价格Id(组合产品的上级节点)
            /// </summary>           
            public string ParentProductPriceId { get; set; }
            /// <summary>
            /// 产品价格(组合产品的上级节点)
            /// </summary>           
            public string ParentProductPrice { get; set; }
            /// <summary>
            /// 价格(组合产品的上级节点)
            /// </summary>           
            public decimal ParentContractProductinfoPrice { get; set; }
            /// <summary>
            /// 价格(组合产品的上级节点)
            /// </summary>           
            public decimal ParentContractProductinfoPriceTotal { get; set; }
            /// <summary>
            /// 套餐产品的内容
            /// </summary>
            public List<ProductInfo_Out> ChildNode { get; set; }
            /// <summary>
            /// 备注
            /// </summary>
            public string Remark { get; set; }
            /// <summary>
            /// 产品简介
            /// </summary>
            public string BriefIntroduction { get; set; }
            /// <summary>
            /// 组合产品简介
            /// </summary>
            public string ParentBriefIntroduction { get; set; }
            /// <summary>
            /// 国内国外
            /// </summary>
            public int? NotForSale { get; set; }
            /// <summary>
            /// 服务开始时间
            /// </summary>           
            public DateTime? ServiceStartDate { get; set; }
            /// <summary>
            /// 服务结束时间
            /// </summary>           
            public DateTime? ServiceEndDate { get; set; }
            /// <summary>
            /// 开通周期
            /// </summary>
            public string OpenServiceCycle { get; set; }
            /// <summary>
            /// 备注
            /// </summary>
            public string ServiceRemark { get; set; }
            /// <summary>
            /// 优惠券列表
            /// </summary>
            public List<CounponDetail> CouponList { get; set; }
        }

        public class ProductInfoDownloadStyle_Out
        {
            public string Id { get; set; }

            /// <summary>
            /// 合同表id
            /// </summary>
            public string ContractId { get; set; }

            /// <summary>
            /// 产品表id
            /// </summary>
            public string ProductId { get; set; }

            /// <summary>
            /// 产品名称
            /// </summary>
            public string ProductName { get; set; }

            /// <summary>
            /// 产品英文名称
            /// </summary>
            public string ProductNameEN { get; set; }

            /// <summary>
            /// 产品描述
            /// </summary>
            public string ProductDescription { get; set; }

            /// <summary>
            /// 产品英文描述
            /// </summary>
            public string ProductDescriptionEn { get; set; }

            /// <summary>
            /// 产品编号
            /// 2025年6月16日 修改
            /// </summary>
            public string ProductNum { get; set; }

            ///// <summary>
            ///// 售价
            ///// </summary>
            //public decimal Price { get; set; }

            /// <summary>
            /// 产品类型
            /// </summary>
            public int ProductType { get; set; }

            /// <summary>
            /// 产品价格
            /// </summary>           
            public string ProductPrice { get; set; }

            /// <summary>
            /// 价格
            /// </summary>           
            public decimal ContractProductinfoPrice { get; set; }

            /// <summary>
            /// 价格
            /// </summary>           
            public decimal ContractProductinfoPriceTotal { get; set; }

            /// <summary>
            /// 产品表id(组合产品的上级节点)
            /// </summary>           
            public string ParentProductId { get; set; }

            /// <summary>
            /// 产品名称(组合产品的上级节点)
            /// </summary>           
            public string ParentProductName { get; set; }

            /// <summary>
            /// 产品价格(组合产品的上级节点)
            /// </summary>           
            public string ParentProductPrice { get; set; }

            /// <summary>
            /// 价格(组合产品的上级节点)
            /// </summary>           
            public decimal ParentContractProductinfoPrice { get; set; }

            /// <summary>
            /// 价格(组合产品的上级节点)
            /// </summary>           
            public decimal ParentContractProductinfoPriceTotal { get; set; }

            public List<ProductInfoDownloadStyle_Out> ChildNode { get; set; }

            /// <summary>
            /// 产品简介
            /// </summary>
            public string BriefIntroduction { get; set; }

            /// <summary>
            /// 组合产品简介
            /// </summary>
            public string ParentBriefIntroduction { get; set; }

            /// <summary>
            /// 国内国外
            /// </summary>
            public int? NotForSale { get; set; }

            /// <summary>
            /// 人民币标准价格
            /// </summary>           
            public decimal? CNYTypical { get; set; }

            /// <summary>
            /// 美元标准价格
            /// </summary>           
            public decimal? USDTypical { get; set; }

            /// <summary>
            /// 欧元标准价格
            /// </summary>           
            public decimal? EURTypical { get; set; }
        }


        public class ProductInfoDownloadStyleAndPrice_Out : ProductInfoDownloadStyle_Out
        {
        }

        public class ProductInfoAndPrice_Out : ProductInfo_Out
        {
            /// <summary>
            /// 价格
            /// </summary>
            public List<ProductPrice> ProductPriceList { get; set; }

            /// <summary>
            /// 国家名称
            /// </summary>
            public string CountrysName { get; set; }
        }

        public class ProductServiceInfo_Out : ProductInfo_Out
        {
            /// <summary>
            /// Desc:
            /// Default:
            /// Nullable:True
            /// </summary>           
            public string ProductServiceInfo_CustomerCompanyName { get; set; }

            /// <summary>
            /// Desc:
            /// Default:
            /// Nullable:True
            /// </summary>           
            public int? ProductServiceInfo_UsersNo { get; set; }

            /// <summary>
            /// Desc:
            /// Default:
            /// Nullable:True
            /// </summary>           
            public string ProductServiceInfo_SubscriptionPeriod { get; set; }

            /// <summary>
            /// Desc:
            /// Default:
            /// Nullable:True
            /// </summary>           
            public string ProductServiceInfo_Countrys { get; set; }

            /// <summary>
            /// Desc:
            /// Default:
            /// Nullable:True
            /// </summary>           
            public string ProductServiceInfo_PrimaryAccountsNum { get; set; }

            /// <summary>
            /// Desc:
            /// Default:
            /// Nullable:True
            /// </summary>           
            public string ProductServiceInfo_SubAccountsNum { get; set; }

            /// <summary>
            /// Desc:
            /// Default:
            /// Nullable:True
            /// </summary>           
            public string ProductServiceInfo_SharePeopleNum { get; set; }

            /// <summary>
            /// Desc:
            /// Default:
            /// Nullable:True
            /// </summary>           
            public string ProductServiceInfo_CodesNum { get; set; }

            /// <summary>
            /// Desc:
            /// Default:
            /// Nullable:True
            /// </summary>           
            public string ProductServiceInfo_CaseNum { get; set; }

            /// <summary>
            /// Desc:
            /// Default:
            /// Nullable:True
            /// </summary>           
            public string ProductServiceInfo_PeriodsNum { get; set; }

            /// <summary>
            /// Desc:
            /// Default:
            /// Nullable:True
            /// </summary>           
            public string ProductServiceInfo_ExpenseType { get; set; }

            /// <summary>
            /// Desc:
            /// Default:
            /// Nullable:True
            /// </summary>           
            public string ProductServiceInfo_SelfExpenseAmount { get; set; }

            /// <summary>
            /// Desc:
            /// Default:
            /// Nullable:True
            /// </summary>           
            public DateTime? ProductServiceInfo_ServiceCycleStart { get; set; }

            /// <summary>
            /// Desc:
            /// Default:
            /// Nullable:True
            /// </summary>           
            public DateTime? ProductServiceInfo_ServiceCycleEnd { get; set; }

            /// <summary>
            /// Desc:
            /// Default:
            /// Nullable:True
            /// </summary>           
            public int? ProductServiceInfo_ServiceMonth { get; set; }
            /// <summary>
            /// 优惠类型
            /// </summary>
            public EnumGtisDiscountType DiscountType { get; set; }

        }

        public class ProductInfoAudit_Out : ProductInfo_Out
        {
            /// <summary>
            /// 开通月数
            /// </summary>
            public int? PreOpeningMonths { get; set; }

            /// <summary>
            /// 首次开通月数
            /// </summary>
            public int? PreFirstOpeningMonths { get; set; }

            /// <summary>
            /// 开通年数
            /// </summary>
            public int? PreOpeningYears { get; set; }

            /// <summary>
            /// 国家
            /// </summary>
            public string PreCountrys { get; set; }

            /// <summary>
            /// 主账号个数
            /// </summary>
            public int? PrePrimaryAccountsNum { get; set; }

            /// <summary>
            /// 子账号个数
            /// </summary>
            public int? PreSubAccountsNum { get; set; }

            /// <summary>
            /// 编码个数
            /// </summary>
            public int? PreCodesNum { get; set; }

            /// <summary>
            /// 套数
            /// </summary>
            public int? PreSetNum { get; set; }

            /// <summary>
            /// 期数
            /// </summary>
            public int? PrePeriodsNum { get; set; }

            /// <summary>
            /// 超级子账号数
            /// </summary>           
            public int? PreSuperSubAccountNum { get; set; }

            /// <summary>
            /// 价格格式
            /// </summary>           
            public string PreProductPrice { get; set; }

            /// <summary>
            /// 国家中文名称
            /// </summary>
            public string CountryNames { get; set; }
        }

        public class AddContractProductServiceInfoAppl_In
        {
            public AddContractProductServiceInfoGtisAppl_In ContractProductServiceInfoGtisAppl { get; set; }

            public AddContractProductServiceInfoDBAppl_In ContractProductServiceInfoDBAppl { get; set; }

            public AddContractProductServiceInfoCollegeAppl_In ContractProductServiceInfoCollegeAppl { get; set; }

            public AddContractProductServiceInfoGlobalSearchAppl_In ContractProductServiceInfoGlobalSearchAppl { get; set; }

            public AddContractProductServiceInfoOtherDataAppl_In ContractProductServiceInfoOtherDataAppl { get; set; }

            public AddContractProductServiceInfoProjectAppl_In ContractProductServiceInfoProjectAppl { get; set; }
        }

        public class G4DbNames
        {
            /// <summary>
            /// 国家ID
            /// </summary>
            public int? SID { get; set; }

            /// <summary>
            /// 中文名称
            /// </summary>           
            public string CountryName { get; set; }

            /// <summary>
            /// 洲的缩写
            /// </summary>           
            public string BFState { get; set; }

            /// <summary>
            /// 洲的名称
            /// </summary>           
            public string Continent { get; set; }

            /// <summary>
            /// Desc:国家类型 0：进口  1：出口
            /// </summary>           
            public int? CType { get; set; }

            /// <summary>
            /// Desc:国家英文名称，不分进出口类型
            /// </summary>           
            public string ImgName { get; set; }

            /// <summary>
            /// Desc:其他类型类型 0：正常 1：快板  2：统计
            /// </summary>           
            public int? OtherType { get; set; }

            /// <summary>
            /// 归属国家ID
            /// </summary>
            public int? BelongToSid { get; set; }

            public string? DBName { get; set; }
        }
        public class AddContractChangeAppl_In
        {
            /// <summary>
            /// 合同表id
            /// </summary>
            [Required(ErrorMessage = "合同表id不可为空")]
            public string ContractId { get; set; }
            /// <summary>
            /// 备注
            /// </summary>
            public string Remark { get; set; }
            /// <summary>
            /// 合同变更申请变更字段
            /// </summary>
            public List<ApplItem_IN> ApplItem { get; set; }
            /// <summary>
            /// 合同变更申请附件
            /// </summary>
            [Required(ErrorMessage = "合同变更申请附件不可为空")]
            public IFormFileCollection ApplAttachFile { get; set; }
        }
        [ModelBinder(BinderType = typeof(MetadataValueModelBinder))]
        public class ApplItem_IN
        {
            /// <summary>
            /// 合同变更字段描述表Id
            /// </summary>
            [Required(ErrorMessage = "合同变更字段描述表Id不可为空")]
            public string ContractChangeTypeId { get; set; }
            /// <summary>
            /// 变更前内容
            /// </summary>
            public string BeforeValue { get; set; }
            /// <summary>
            /// 变更后内容
            /// </summary>
            public string AfterValue { get; set; }
        }
        public class UpdateContractProductServiceInfoGtisAppl_In
        {
            /// <summary>
            /// 合同产品表Id
            /// </summary>
            [Required(ErrorMessage = "合同产品信息表id不可为空")]
            public string ContractProductInfoId { get; set; }
            /// <summary>
            /// 主账号数量
            /// </summary>
            public int PrimaryAccountsNum { get; set; }
            /// <summary>
            /// 子账号数量
            /// </summary>
            public int SubAccountsNum { get; set; }
            /// <summary>
            /// 每个账户共享人数
            /// </summary>
            public int SharePeopleNum { get; set; }
            /// <summary>
            /// 共享使用总数
            /// </summary>
            public int? ShareUsageNum { get; set; }
            /// <summary>
            /// 子账号授权国家次数
            /// </summary>
            public int? AuthorizationNum { get; set; }
            /// <summary>
            /// 服务周期-开始时间
            /// </summary>
            public DateTime? ServiceCycleStart { get; set; }
            /// <summary>
            /// 服务周期-结束时间
            /// </summary>
            public DateTime? ServiceCycleEnd { get; set; }
            /*/// <summary>
            /// 常驻国家
            /// </summary>
            public List<GtisApplResidentCountry>? GtisApplResidentCountry { get; set; }
            /// <summary>
            /// 常驻城市
            /// </summary>
            public List<GtisApplResidentCity>? GtisApplResidentCity { get; set; }*/
            /// <summary>
            /// 零售国家
            /// </summary>
            public int RetailCountry { get; set; }
            /// <summary>
            /// 备注
            /// </summary>
            public string Remark { get; set; }
            /// <summary>
            /// 环球慧思申请_开通国家
            /// </summary>
            public List<GtisApplCountry> GtisApplCountry { get; set; }
            /// <summary>
            /// 服务延长月份
            /// </summary>
            public int? ServiceAddMonth { get; set; }
            /// <summary>
            /// 变更后账户信息
            /// </summary>
            public List<UpdateContractProductServiceInfoGtisApplUser_In> UpdateContractProductServiceInfoGtisApplUser_In { get; set; }
            /// <summary>
            /// 变更项目
            /// </summary>
            public List<string> ChangeProjectType { get; set; }
            /// <summary>
            /// 优惠类型
            /// </summary>
            public int DiscountType { get; set; }
            /// <summary>
            /// 选中的优惠券Id列表
            /// </summary>
            public List<string>? CounponDetailIdList { get; set; }
            /// <summary>
            /// 优惠后服务月份
            /// </summary>
            public int? ServiceMonthAfterDiscount { get; set; }
            /// <summary>
            /// 优惠后服务开始时间
            /// </summary>
            public DateTime? ServiceCycleStartAfterDiscount { get; set; }
            /// <summary>
            /// 优惠后服务结束时间
            /// </summary>
            public DateTime? ServiceCycleEndAfterDiscount { get; set; }
            /// <summary>
            /// 延长的总服务天数
            /// </summary>
            public int? PerlongServiceDays { get; set; }
            /// <summary>
            /// 赠送的个人服务天数
            /// </summary>
            public int? PrivateServiceDays { get; set; }
            /// <summary>
            /// 需自费的个人服务天数
            /// </summary>
            public int? OverServiceDays { get; set; }
            /// <summary>
            /// 环球搜账号数量(主+子)
            /// </summary>
            public int? GlobalSearchAccountCount { get; set; }
            /// <summary>
            /// 加急处理状态
            /// </summary>
            public bool IsUrgent { get; set; }
            /// <summary>
            /// 变更原因
            /// </summary>
            /// <returns></returns>
            public List<EnumGtisServiceChangeProject> ChangeReasonList { get; set; }
            /// <summary>
            /// 禁用下载/导出权限
            /// </summary>
            public bool? ForbidSearchExport { get; set; }
            /// <summary>
            /// 是否开通定制报告
            /// </summary>
            public bool? WordRptPermissions { get; set; }
            /// <summary>
            /// 定制报告每年总次数
            /// </summary>
            public int? WordRptMaxTimes { get; set; }
        }
        public class UpdateContractProductServiceInfoGtisApplUser_In
        {
            /// <summary>
            /// 
            /// </summary>
            public string UserId { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string AccountNumber { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public int AccountType { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public int SharePeopleNum { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public int? AuthorizationNum { get; set; }
        }
        public class UpdateContractProductServiceInfoDBAppl_In : AddContractProductServiceInfoDBAppl_In
        {

        }
        public class UpdateContractProductServiceInfoCollegeAppl_In : AddContractProductServiceInfoCollegeAppl_In
        {
        }
        public class UpdateContractProductServiceInfoGlobalSearchAppl_In : AddContractProductServiceInfoGlobalSearchAppl_In
        {
            [Required(ErrorMessage = "合同产品信息表id不可为空")]
            public string ContractProductInfoId { get; set; }

            [Required(ErrorMessage = "主账号数量不可为空")]
            public int PrimaryAccountsNum { get; set; }

            [Required(ErrorMessage = "子账号数量不可为空")]
            public int SubAccountsNum { get; set; }

            [Required(ErrorMessage = "服务周期-开始时间不可为空")]
            public DateTime? ServiceCycleStart { get; set; }

            [Required(ErrorMessage = "服务周期-结束时间不可为空")]
            public DateTime? ServiceCycleEnd { get; set; }

            //[Required(ErrorMessage = "备注不可为空")]
            public string Remark { get; set; }
            public List<UpdateContractProductServiceInfoGlobalSearchApplUser_In> GlobalSearchUser { get; set; }

        }
        public class UpdateContractProductServiceInfoGlobalSearchApplUser_In
        {
            public string Id { get; set; }
            public string AccountNumber { get; set; }
        }
        public class UpdateContractProductServiceInfoOtherDataAppl_In : AddContractProductServiceInfoOtherDataAppl_In
        {
        }
        public class UpdateContractProductServiceInfoProjectAppl_In : AddContractProductServiceInfoProjectAppl_In
        {
        }
        public class UpdateContractProductServiceInfoAppl_In
        {
            public UpdateContractProductServiceInfoGtisAppl_In ContractProductServiceInfoGtisAppl { get; set; }

            public UpdateContractProductServiceInfoDBAppl_In ContractProductServiceInfoDBAppl { get; set; }

            public UpdateContractProductServiceInfoCollegeAppl_In ContractProductServiceInfoCollegeAppl { get; set; }

            public UpdateContractProductServiceInfoGlobalSearchAppl_In ContractProductServiceInfoGlobalSearchAppl { get; set; }

            public UpdateContractProductServiceInfoOtherDataAppl_In ContractProductServiceInfoOtherDataAppl { get; set; }

            public UpdateContractProductServiceInfoProjectAppl_In ContractProductServiceInfoProjectAppl { get; set; }
        }
        public class SearchRegisterContractBasicInfoList_In : ApiTableIn
        {
            /// <summary>
            /// 客户编码
            /// </summary>           
            public string ContractNum { get; set; }

            /// <summary>
            /// 甲方公司
            /// </summary>           
            public string FirstPartyName { get; set; }

            /// <summary>
            /// 创建时间
            /// </summary> 
            public DateTime? CreateDateStart { get; set; }

            /// <summary>
            /// 创建时间
            /// </summary> 
            public DateTime? CreateDateEnd { get; set; }
        }

        public class SearchContractBasicInfoList_In : ApiTableIn
        {
            /// <summary>
            /// 合同号
            /// </summary>           
            public string ContractNo { get; set; }

            /// <summary>
            /// 合同名称
            /// </summary>           
            public string ContractName { get; set; }

            /// <summary>
            /// 创建时间
            /// </summary> 
            public DateTime? CreateDateStart { get; set; }

            /// <summary>
            /// 创建时间
            /// </summary> 
            public DateTime? CreateDateEnd { get; set; }
        }


        public class SearchContractBasicInfoList_Out
        {
            public string Id { get; set; }

            /// <summary>
            /// 客户编码（合同编码）
            /// </summary>           
            public string ContractNum { get; set; }

            /// <summary>
            /// 合同类型
            /// </summary>           
            public string ContractMethodName
            {
                get
                {
                    return Dictionary.ContractMethod.First(e => e.Value == ContractMethod.ToString()).Name;
                }
            }

            /// <summary>
            /// 合同类型
            /// </summary>           
            public int ContractMethod { get; set; }

            /// <summary>
            /// 合同号
            /// </summary>           
            public string ContractNo { get; set; }

            /// <summary>
            /// 合同名称
            /// </summary>           
            public string ContractName { get; set; }

            /// <summary>
            /// 合同状态
            /// </summary>
            public int ContractStatus { get; set; }

            /// <summary>
            /// 合同状态名称
            /// </summary>
            public string ContractStatusName { get { return ((EnumContractStatus)ContractStatus).GetEnumDescription().ToString(); } }

            /// <summary>
            /// 出单人名称
            /// </summary>
            public string IssuerName { get; set; }

            /// <summary>
            /// 出单人
            /// </summary>           
            public string Issuer { get; set; }

            /// <summary>
            /// 签约日期
            /// </summary>
            private string _SigningDate;
            public string SigningDate
            {
                get { return Convert.ToDateTime(_SigningDate).ToString("yyyy-MM-dd"); }
                set { _SigningDate = value; }
            }

            /// <summary>
            /// 甲方公司
            /// </summary>  
            public string FirstPartyName { get; set; }

            /// <summary>
            /// 甲方公司
            /// </summary>           
            public string FirstParty { get; set; }

            /// <summary>
            /// 乙方公司
            /// </summary>
            public string SecondPartyName { get; set; }

            /// <summary>
            /// 乙方公司
            /// </summary>           
            public string SecondParty { get; set; }

            /// <summary>
            /// 合同金额
            /// </summary>           
            public decimal ContractAmount { get; set; }

            /// <summary>
            /// 外币合同金额
            /// </summary>           
            public decimal FCContractAmount { get; set; }

            /// <summary>
            /// 币种
            /// </summary>           
            public string CurrencyName
            {
                get
                {
                    return Dictionary.Currency.First(e => e.Value == Currency.ToString()).Name;
                }
            }

            /// <summary>
            /// 币种
            /// </summary>           
            public int Currency { get; set; }

            /// <summary>
            /// 签约类型
            /// </summary>           
            public string ContractTypeName
            {
                get
                {
                    return Dictionary.ContractType.First(e => e.Value == ContractType.ToString()).Name;
                }
            }

            /// <summary>
            /// 签约类型
            /// </summary>           
            public int ContractType { get; set; }

            /// <summary>
            /// 创建时间
            /// </summary>
            private string _CreateDate;
            public string CreateDate
            {
                get { return Convert.ToDateTime(_CreateDate).ToString("yyyy-MM-dd HH:mm"); }
                set { _CreateDate = value; }
            }

            /// <summary>
            /// 开票状态
            /// </summary>
            public string IsInvoiceName
            {
                get
                {
                    return IsInvoice == null ? "" : ((EnumContractInvoiceDisplayStatus)IsInvoice).GetEnumDescription().ToString();
                }
            }

            /// <summary>
            /// 开票状态
            /// </summary>
            public int? IsInvoice { get; set; }
        }
        public class SearchContractChangeAppl_OUT
        {
            public string Id { get; set; }
            public string ContractChangeApplId { get; set; }
            /// <summary>
            /// 合同号            
            /// </summary>
            public string ContractNo { get; set; }
            /// <summary>
            /// 客户编码
            /// </summary>
            public string ContractNum { get; set; }
            /// <summary>
            /// 合同名称
            /// </summary>
            public string ContractName { get; set; }
            /// <summary>
            /// 甲方公司
            /// </summary>
            public string FirstParty { get; set; }
            /// <summary>
            /// 甲方公司
            /// </summary>           
            public string FirstPartyName { get; set; }
            /// <summary>
            /// 变更状态
            /// </summary>         
            public int? State { get; set; }
            /// <summary>
            /// 变更状态
            /// </summary>   
            public string StateName { get; set; }
            /// <summary>
            /// 变更项目
            /// </summary>
            public List<SearchContractChangeApplItem_OUT> ChangeItems { get; set; }
            /// <summary>
            /// 变更项目数（不包括子项）
            /// </summary>         
            public int ChangeItemNum { get; set; }
            /// <summary>
            /// 申请人
            /// </summary>              
            public string ApplicantId { get; set; }
            /// <summary>
            /// 申请人
            /// </summary>            
            public string ApplicantName { get; set; }
            /// <summary>
            /// 申请时间
            /// </summary>   
            public string ApplicantDate { get; set; }
            /// <summary>
            /// 创建时间
            /// </summary>
            public string CreateDate { get; set; }
        }
        public class SearchContractChangeApplItem_OUT
        {
            public string Id { get; set; }
            public string ContractChangeApplId { get; set; }
            public string ContractChangeTypeId { get; set; }
            public string GroupId { get; set; }
            /// <summary>
            /// 列名
            /// </summary>   
            public string ColumnName { get; set; }
            /// <summary>
            /// 变更前内容
            /// </summary>   
            public string BeforeValue { get; set; }
            /// <summary>
            /// 变更后内容
            /// </summary>   
            public string AfterValue { get; set; }
            /// <summary>
            /// 修改变更后内容
            /// </summary>   
            public string ChangeValue { get; set; }
            /// <summary>
            /// 变更前内容
            /// </summary>   
            public string BeforeValueName { get; set; }
            /// <summary>
            /// 变更后内容
            /// </summary>   
            public string AfterValueName { get; set; }
            /// <summary>
            /// 修改变更后内容
            /// </summary>   
            public string ChangeValueName { get; set; }
        }
        public class SearchContractChangeAppl_IN : ApiTableIn
        {
            /// <summary>
            /// 合同名称
            /// </summary>
            public string ContractName { get; set; }
            /// <summary>
            /// 客户编码
            /// </summary>
            public string ContractNum { get; set; }
            /// <summary>
            /// 甲方公司名称
            /// </summary>
            public string FirstPartyName { get; set; }
            /// <summary>
            /// 变更项目
            /// </summary>
            public List<string> ContractChangeTypeId { get; set; }
            /// <summary>
            /// 变更状态
            /// </summary>
            public List<int> State { get; set; }
            /// <summary>
            /// 申请人
            /// </summary>
            public List<string> ApplicantId { get; set; }
            /// <summary>
            /// 创建时间
            /// </summary>
            public DateTime? CreateDateStart { get; set; }
            /// <summary>
            /// 创建时间
            /// </summary>
            public DateTime? CreateDateEnd { get; set; }
            public EnumQueryListType EnumQueryListType { get; set; }
        }
        public class ContractChangeAppl_OUT : ContractChangeAppl
        {
            /// <summary>
            /// 合同和到账信息
            /// </summary>
            public ContractInfoAndReceipt_Out ContractInfo { get; set; }
        }
        public class SearchContractChangeApplAttachFile_OUT
        {
            public string Id { get; set; }
            public string FileName { get; set; }
            public string FilePath { get; set; }
            public string FileType { get; set; }
        }
        public class AuditContractChangeAppl_IN
        {
            [Required(ErrorMessage = "Id不可为空")]
            public string AuditId { get; set; }
            [Required(ErrorMessage = "合同变更状态不可为空")]
            public int State { get; set; }
            public List<ChangeApplItem_IN> ApplItems { get; set; }
        }
        public class ChangeApplItem_IN
        {
            /// <summary>
            /// 合同变更字段Id
            /// </summary>
            [Required(ErrorMessage = "Id不可为空")]
            public string ContractChangeItemId { get; set; }
            /// <summary>
            /// 变更内容
            /// </summary>
            public string ChangeValue { get; set; }
        }
        public class RevokeContractChangeAudit_IN
        {
            [Required(ErrorMessage = "Id不可为空")]
            public string AuditId { get; set; }
        }
        public class GetContractChangeColumns_OUT
        {
            public string Name { get; set; }
            public string Value { get; set; }
            public string Id { get; set; }
            public string GroupId { get; set; }
        }

        public class AddUrgeRegistration_In
        {
            /// <summary>
            /// 合同id
            /// </summary>
            [Required(ErrorMessage = "合同主键不可为空")]
            public string Id { get; set; }

            [Required(ErrorMessage = "上传附件不可为空")]
            public IFormFileCollection PaymentVoucher { get; set; }
        }

        public class ContractProjectInfo_Out
        {
            /// <summary>
            /// 合同id
            /// </summary>
            public string ContractId { get; set; }

            /// <summary>
            /// 项目名称
            /// </summary>
            public string ProjectName { get; set; }

            /// <summary>
            /// 售价
            /// </summary>
            public decimal? Price { get; set; }

            /// <summary>
            /// 项目数
            /// </summary>
            public int? ItemsNum { get; set; }

            /// <summary>
            /// 项目类型
            /// </summary>
            public EnumProjectType Type { get; set; }

            /// <summary>
            /// 项目类型
            /// </summary>
            public string TypeName { get; set; }

            /// <summary>
            /// 其他项目名称
            /// </summary>
            public List<string> OtherNames { get; set; }
        }

        public class UploadContract_In
        {
            /// <summary>
            /// 合同id
            /// </summary>
            [Required(ErrorMessage = "合同主键不可为空")]
            public string Id { get; set; }

            //[Required(ErrorMessage = "电子合同不可为空")]
            public IFormFileCollection ElectronicContract { get; set; }

            //[Required(ErrorMessage = "邓白氏合同原件不可为空")]
            public IFormFileCollection DBOriginalContract { get; set; }

            //[Required(ErrorMessage = "已盖章合同不可为空")]
            public IFormFileCollection SealedContract { get; set; }
        }

        public class SupplementaryUploadContract_In
        {
            /// <summary>
            /// 合同id
            /// </summary>
            [Required(ErrorMessage = "合同主键不可为空")]
            public string Id { get; set; }

            //[Required(ErrorMessage = "电子合同不可为空")]
            public IFormFileCollection ElectronicContract { get; set; }

            //[Required(ErrorMessage = "邓白氏合同原件不可为空")]
            public IFormFileCollection DBOriginalContract { get; set; }

            //[Required(ErrorMessage = "已盖章合同不可为空")]
            public IFormFileCollection SealedContract { get; set; }

            //[Required(ErrorMessage = "未盖章合同不可为空")]
            public IFormFileCollection ContractFile { get; set; }

            //[Required(ErrorMessage = "合同特殊附件不可为空")]
            public IFormFileCollection ContractSpecialAttachFile { get; set; }
        }

        public class SupplementarySealedAndElectronicUploadContract_In
        {
            /// <summary>
            /// 合同id
            /// </summary>
            [Required(ErrorMessage = "合同主键不可为空")]
            public string Id { get; set; }

            //[Required(ErrorMessage = "电子合同不可为空")]
            public IFormFileCollection ElectronicContract { get; set; }

            //[Required(ErrorMessage = "已盖章合同不可为空")]
            public IFormFileCollection SealedContract { get; set; }

        }

        public class OtherStampReviewUploadContract_In
        {
            /// <summary>
            /// 合同id
            /// </summary>
            [Required(ErrorMessage = "合同主键不可为空")]
            public string Id { get; set; }

            //[Required(ErrorMessage = "电子合同不可为空")]
            public IFormFileCollection OtherStampReview { get; set; }

        }

        public class GetContractByCustomerId_In : SearchContractList_In
        {
        }

        public class GetContractByCustomerId_Out : SearchContractList_Out
        {
            /// <summary>
            /// 保护截止日
            /// </summary>
            public string ProtectionDeadline { get; set; }
        }
        public class GetContractInfoRecords_Out : IComparable<GetContractInfoRecords_Out>
        {
            /// <summary>
            /// 该记录的主键[可能是跟踪记录、日程计划、下载合同]
            /// </summary>
            public string Id { get; set; }

            /// <summary>
            /// 用户Id
            /// </summary>
            public string UserId { get; set; }

            /// <summary>
            /// 用户姓名
            /// </summary>
            public string UserName { get; set; }

            /// <summary>
            /// 记录来源 1 == 跟踪记录 2 == 日程计划 3 == 下载合同
            /// </summary>
            public int RecordSource { get; set; }

            /// <summary>
            /// 记录来源名称[可能是跟踪记录、日程计划、下载合同]
            /// </summary>
            public string RecordSourceName { get; set; }

            /// <summary>
            /// 跟踪方式描述
            /// </summary>
            public string TrackingTypeName { get; set; }

            /// <summary>
            /// 跟踪目的描述
            /// </summary>
            public string TrackingPurposeName { get; set; }

            /// <summary>
            /// 跟踪阶段描述
            /// </summary>
            public string TrackingStageName { get; set; }

            /// <summary>
            /// 合同Id
            /// </summary>
            public string ContractId { get; set; }

            /// <summary>
            /// 合同名称
            /// </summary>
            public string ContractName { get; set; }

            /// <summary>
            /// 创建时间
            /// </summary>
            public DateTime CreateDate { get; set; }

            /// <summary>
            /// 访客开始时间
            /// </summary>
            public DateTime? VisitorTimeStart { get; set; }

            /// <summary>
            /// 访客时间-结束
            /// </summary>
            public DateTime? VisitorTimeEnd { get; set; }

            /// <summary>
            /// 备注or内容
            /// </summary>
            public string Remark { get; set; }

            /// <summary>
            /// 图片附件
            /// </summary>
            public List<object> ImageAttachFile { get; set; } = new();

            /// <summary>
            /// 文档附件
            /// </summary>
            public List<object> DocumentAttachFile { get; set; } = new();

            /// <summary>
            /// 跟踪事件描述【日程计划专享】
            /// </summary>
            public string TrackingEventsName { get; set; }

            /// <summary>
            /// 合同信息【日程计划专享】
            /// </summary>
            public Schedule_ContractInfo ContractInfo { get; set; }

            public int CompareTo(GetContractInfoRecords_Out? other)
            {
                if (ReferenceEquals(this, other))
                {
                    return 0;
                }

                if (ReferenceEquals(null, other))
                {
                    return 1;
                }

                return other.CreateDate.CompareTo(CreateDate);
            }

            /// <summary>
            /// 记录人头像图片
            /// </summary>
            public string AvatarImage { get; set; }

            /// <summary>
            /// 记录人头像图片Url
            /// </summary>
            public string? AvatarImageUrl
            {
                get
                {
                    return string.IsNullOrEmpty(AvatarImage) || string.IsNullOrEmpty(UserId)
                            ? null
                            : $"/api/Attachfile/Preview?id={UserId}&fileType=AvatarImage&Authorization={TokenModel.Token}";
                }
            }
            /// <summary>
            /// 允许修改 0 不可 1 可以 
            /// </summary>
            public bool? CanEdit
            {
                get; set;
            }
        }

        public class CheckUploadContract_Out
        {
            public bool IsContainDb { get; set; }

            public bool IsHaveElectronicContract { get; set; }

            public bool IsHaveDBOriginalContract { get; set; }

            public bool IsHaveSealedContract { get; set; }
        }

        public class GetContractInfoRecords_In
        {
            /// <summary>
            /// 合同Id
            /// </summary>
            [Required(ErrorMessage = "合同Id不可为空!")]
            public string contractId { get; set; }

            /// <summary>
            /// 记录来源 1 == 跟踪记录 2 == 日程计划 3 == 下载合同
            /// </summary>
            public int? RecordSource { get; set; }

            /// <summary>
            /// 发布时间起始
            /// </summary>
            public DateTime? CreateDateStart { get; set; }

            /// <summary>
            /// 发布时间结束
            /// </summary>
            public DateTime? CreateDateEnd { get; set; }

        }

        public class GetContractAuditByContractIdIn : ApiTableIn
        {
            /// <summary>
            /// 记录id
            /// </summary>
            [Required(ErrorMessage = "合同id不可为空")]
            public string ContractId { get; set; }

            /// <summary>
            /// 创建时间
            /// </summary> 
            public DateTime? EndDateStart { get; set; }

            /// <summary>
            /// 创建时间
            /// </summary> 
            public DateTime? EndDateEnd { get; set; }
        }

        public class GetContractAuditByContractId_Out
        {
            /// <summary>
            /// id
            /// </summary>           
            public string Id { get; set; }

            /// <summary>
            /// 合同id
            /// </summary>           
            public string ContractId { get; set; }

            /// <summary>
            /// 申请人id
            /// </summary>           
            public string ApplicantId { get; set; }

            /// <summary>
            /// 申请人
            /// </summary>           
            public string ApplicantName { get; set; }

            /// <summary>
            /// 申请时间
            /// </summary>           
            public string ApplicantDate { get; set; }

            /// <summary>
            /// 审核状态
            /// </summary>           
            public int? State { get; set; }

            /// <summary>
            /// 审核状态
            /// </summary>           
            public string StateName { get; set; }

            /// <summary>
            /// 审核人id
            /// </summary>           
            public string ReviewerId { get; set; }

            /// <summary>
            /// 审核人
            /// </summary>           
            public string ReviewerName { get; set; }

            /// <summary>
            /// 审核时间
            /// </summary>           
            public string ReviewerDate { get; set; }

            /// <summary>
            /// 审核类型
            /// </summary>           
            public long AuditCategory { get; set; }

            /// <summary>
            /// 审核类型
            /// </summary>
            public string AuditCategoryName
            {
                get
                {
                    List<Db_sys_dictionary> dictionary = Dictionary.ContractAuditCategory.Where(e => e.Value == AuditCategory.ToString()).ToList();
                    if (dictionary.Count > 0)
                    {
                        return dictionary.First().Name;
                    }
                    else
                    {
                        return "";
                    }
                }
            }

            /// <summary>
            /// 审核反馈
            /// </summary>           
            public string Feedback { get; set; }

        }

        public class GetContractAuditByContractId4Record_Out : GetContractAuditByContractId_Out
        {
            /// <summary>
            /// 合同名称
            /// </summary>
            public string ContractName { get; set; }
            /// <summary>
            /// 记录人头像图片
            /// </summary>
            public string AvatarImage { get; set; }
        }

        public class ContractDueReminder_Out
        {
            /// <summary>
            /// 合同号
            /// </summary>           
            public string ContractNo { get; set; }

            /// <summary>
            /// 合同名称
            /// </summary>           
            public string ContractName { get; set; }

            /// <summary>
            /// 出单人
            /// </summary>           
            public string Issuer { get; set; }

            /// <summary>
            /// 甲方公司
            /// </summary>  
            public string FirstPartyName { get; set; }

            /// <summary>
            /// 甲方公司
            /// </summary>           
            public string FirstParty { get; set; }
        }

        public class ProductMonthYear_Out
        {
            /// <summary>
            /// 月
            /// </summary>           
            public int? Month { get; set; }

            /// <summary>
            /// 年
            /// </summary>           
            public int? Year { get; set; }

            /// <summary>
            /// 描述
            /// </summary>           
            public string Describe { get; set; }

            /// <summary>
            /// 类型
            /// </summary>           
            public int? Type { get; set; }
        }

        public class ProductRules_Out
        {
            public string RuleId { get; set; }

            public Guid ProductId { get; set; }

            public List<ProductRulesPrice> PriceList { get; set; }

            public List<G4DbNames> G4DbNames { get; set; }

            public List<ProductRulesPrice> SubaccountsPriceList { get; set; }

            public bool Deleted { get; set; } = false;
        }

        public class ProductRules_In
        {
            public List<ProductInfo_In> productInfoList { get; set; }

            [Required(ErrorMessage = "销售国家不可为空")]
            public int salesCountry { get; set; }

            [Required(ErrorMessage = "币种不可为空")]
            public int currency { get; set; }
            /// <summary>
            /// 甲方公司
            /// </summary>
            public string firstparty { get; set; } = string.Empty;

            public string parentContractId { get; set; } = null;
        }

        public class ProductRulesPrice
        {
            public string Id { get; set; }

            /// <summary>
            /// 产品名称
            /// </summary>
            public string ProductName { get; set; }

            /// <summary>
            /// 产品类型
            /// </summary>
            public int? ProductType { get; set; }

            /// <summary>
            /// 特惠产品描述
            /// </summary>
            public string? EventMark { get; set; }

            /// <summary>
            /// 币种
            /// </summary>
            public int Currency { get; set; }

            /// <summary>
            /// 币种名称
            /// </summary>
            public string CurrencyName
            {
                get
                {
                    return Currency.ToEnum<EnumCurrency>().GetEnumDescription();
                }
            }

            /// <summary>
            /// 币种价格
            /// </summary>
            public string CurrencyPriceLite
            {
                get
                {
                    return Price.ToString("N2") + " " + Currency.ToEnum<EnumCurrencyLite>().GetEnumDescription();
                }
            }

            ///// <summary>
            ///// 币种价格周期
            ///// </summary>
            //public string CurrencyPrice
            //{
            //    get
            //    {
            //        return Price.ToString("N2") + " " + Currency.ToEnum<EnumCurrencyLite>().GetEnumDescription() + " " + ChargingParametersName.Replace("每", "/");
            //    }
            //}

            /// <summary>
            /// 价格分类
            /// </summary>
            public int PriceMode { get; set; }

            public string PriceModeName
            {
                get
                {
                    return PriceMode < 10 ? EnumPriceMode.Fixed.GetEnumDescription() : PriceMode.ToEnum<EnumPriceMode>().GetEnumDescription();

                }
            }

            /// <summary>
            /// 售价
            /// </summary>
            public decimal Price { get; set; }

            /// <summary>
            /// 起售价
            /// </summary>
            public decimal PricePart1 { get; set; }

            /// <summary>
            /// 复购售价
            /// </summary>
            public decimal PricePart2 { get; set; }

            /// <summary>
            /// 总价售价
            /// </summary>
            public decimal TotalPrice { get; set; }

            public int PriceInfo { get; set; }
        }

        public class CheckContractStatus_Out
        {
            public int Type { get; set; }

            public string Message { get; set; }

            public bool Success { get; set; } = true;
        }

        public class CheckContractCanAddItem_In
        {
            /// <summary>
            /// 合同ID
            /// </summary>
            [Required(ErrorMessage = "合同ID不能为空")]
            [StringTrim]
            public string ContractId { get; set; }
        }

        public class CheckContractCanAddItem_Out
        {
            /// <summary>
            /// 是否可以增项
            /// </summary>
            public bool CanAddItem { get; set; }

            /// <summary>
            /// 提示信息
            /// </summary>
            public string Message { get; set; }

            /// <summary>
            /// 操作是否成功
            /// </summary>
            public bool Success { get; set; } = true;
        }

        public class CheckContractProductPrice_Out
        {
            public List<CheckProductPrice_Out> Message { get; set; }

            public bool Success { get; set; } = true;

            /// <summary>
            /// 总价售价
            /// </summary>
            public decimal TotalPrice { get; set; }
        }

        public class CheckProductPrice_Out
        {
            public string Message { get; set; }

            public bool Success { get; set; } = true;
        }

        public class AutoAuditReturnMessage_Out
        {
            public List<AutoAuditReturnMessageContent_Out> Message { get; set; }

            public bool Success { get; set; } = true;

            public bool IsAutoAudit { get; set; } = true;

            public bool IsHaveGtis { get; set; } = false;

            public List<string> ErrorTermContents { get; set; }
            public bool ErrorTerm { get; set; }
        }

        public class AutoAuditReturnMessageContent_Out
        {
            public string Message { get; set; }

            public bool Success { get; set; } = true;
        }

        public class ContractProductServiceinfoStatus
        {
            /// <summary>
            /// Desc:合同表id
            /// Default:
            /// Nullable:True
            /// </summary>           
            public string ContractId { get; set; }

            /// <summary>
            /// Desc:产品数量
            /// Default:
            /// Nullable:True
            /// </summary>           
            public int? ProductNum { get; set; }

            /// <summary>
            /// Desc:待开通数量
            /// Default:
            /// Nullable:True
            /// </summary>           
            public int? ToBeOpenedNum { get; set; }

            /// <summary>
            /// Desc:提交数量
            /// Default:
            /// Nullable:True
            /// </summary>           
            public int? SubmitNum { get; set; }

            /// <summary>
            /// Desc:开通数量
            /// Default:
            /// Nullable:True
            /// </summary>           
            public int? OpenedNum { get; set; }

            /// <summary>
            /// Desc:拒绝数量
            /// Default:
            /// Nullable:True
            /// </summary>           
            public int? RefuseNum { get; set; }

            /// <summary>
            /// Desc:过期数量
            /// Default:
            /// Nullable:True
            /// </summary>           
            public int? ExpireNum { get; set; }

            /// <summary>
            /// Desc:变更数量
            /// Default:
            /// Nullable:True
            /// </summary>           
            public int? ChangeSubmitNum { get; set; }

            /// <summary>
            /// Desc:服务结束时间
            /// Default:
            /// Nullable:True
            /// </summary>           
            public DateTime? ServiceCycleEndMax { get; set; }

            /// <summary>
            /// Desc:服务状态
            /// Default:
            /// Nullable:True
            /// </summary>           
            public int? ProductServiceInfoStatus { get; set; }

            public int? ContractStatus { get; set; }
        }

        public class CompanyAddress
        {
            public string Id { get; set; }

            /// <summary>
            /// 名称
            /// </summary>           
            public string Name { get; set; }

            /// <summary>
            /// 地址
            /// </summary>           
            public string Address { get; set; }

            /// <summary>
            /// 邮编
            /// </summary>           
            public string PostalCode { get; set; }
        }

        public class GetRenewGtisExpireDate_Out
        {
            /// <summary>
            /// 公司名称
            /// </summary>
            public string CompanyName { get; set; }
            /// <summary>
            /// 产品名称
            /// </summary>
            public string ProductName { get; set; }
            /// <summary>
            /// 到期时间
            /// </summary>
            public string ExpireDate { get; set; }
        }

        [ModelBinder(BinderType = typeof(MetadataValueModelBinder))]
        public class ContractModifyContractTemplateAddRow_In //: AddContractTemplateAddRow_In
        {
            //[Required(ErrorMessage = "合同表id不可为空")]
            //public string ContractId { get; set; }

            [Required(ErrorMessage = "模版类型不可为空")]
            public int TemplateType { get; set; }

            //[Required(ErrorMessage = "币种不可为空")]
            //public int Currency { get; set; }

            [Required(ErrorMessage = "产品表不可为空")]
            public int ProductTable { get; set; }

            [Required(ErrorMessage = "条款不可为空")]
            public int Item { get; set; }
        }

        public class ModifyContractTemplateAddRow_In : AddContractTemplateAddRow_In
        {

        }

        public class AddContractTemplateAddRow_In
        {
            [Required(ErrorMessage = "合同表id不可为空")]
            public string ContractId { get; set; }

            [Required(ErrorMessage = "模版类型不可为空")]
            public int TemplateType { get; set; }

            [Required(ErrorMessage = "币种不可为空")]
            public int Currency { get; set; }

            [Required(ErrorMessage = "产品表不可为空")]
            public int ProductTable { get; set; }

            [Required(ErrorMessage = "条款不可为空")]
            public int Item { get; set; }
        }

        public class UpdateContractTemplateAddRow_In : AddContractTemplateAddRow_In
        {
            public string Id { get; set; }
        }

        //public class AddContractTemplateAddRow_Out
        //{
        //    [Required(ErrorMessage = "合同表id不可为空")]
        //    public string ContractId { get; set; }

        //    [Required(ErrorMessage = "模版类型不可为空")]
        //    public int TemplateType { get; set; }

        //    [Required(ErrorMessage = "币种不可为空")]
        //    public int Currency { get; set; }

        //    [Required(ErrorMessage = "产品表不可为空")]
        //    public int ProductTable { get; set; }

        //    [Required(ErrorMessage = "条款不可为空")]
        //    public int Item { get; set; }
        //}

        public class ContractTemplateAddRow_Out
        {
            public string Id { get; set; }

            public string ContractId { get; set; }

            public int? TemplateType { get; set; }

            public int? Currency { get; set; }

            public int? ProductTable { get; set; }

            public int? Item { get; set; }
        }

        public class GetContractNumList_Out
        {
            public string ContractId { get; set; }

            public string CustomerId { get; set; }

            public string ContractNum { get; set; }

            public string FirstParty { get; set; }
            public string FirstPartyName { get; set; }
        }

        public class GetContractTemplate_Out
        {
            /// <summary>
            /// 合同模板类型
            /// </summary>           
            public int TemplateType { get; set; }

            /// <summary>
            /// 中文模版币种
            /// </summary>           
            public string ChineseTemplateCurrency { get; set; }

            /// <summary>
            /// 英文模板币种
            /// </summary>           
            public string EnglishTemplateCurrency { get; set; }

            /// <summary>
            /// 是否中英文版
            /// </summary>           
            public int IsBothChineseAndEnglish { get; set; }
        }

        public class AddSecondPartyContacts_In
        {
            /// <summary>
            /// 签约合同代理人
            /// </summary>       
            [Required(ErrorMessage = "签约合同代理人不可为空")]
            public string SigningAgentName { get; set; }

            /// <summary>
            /// 签约合同代理人电话
            /// </summary>           
            [Required(ErrorMessage = "签约合同代理人电话不可为空")]
            public string SigningAgentPhone { get; set; }

            /// <summary>
            /// 签约合同代理人Email
            /// </summary>           
            [Required(ErrorMessage = "签约合同代理人Email不可为空")]
            public string SigningAgentEmail { get; set; }

            /// <summary>
            /// 签约合同代理人传真
            /// </summary>           
            public string SigningAgentFax { get; set; }
        }

        public class UpdateSecondPartyContacts_In : AddSecondPartyContacts_In
        {
            /// <summary>
            /// 主键
            /// </summary>
            [Required(ErrorMessage = "乙方联系信息表主键不可为空")]
            public string Id { get; set; }
        }

        public class SearchSecondPartyContactsList_Out
        {
            /// <summary>
            /// 主键
            /// </summary> 
            public string Id { get; set; }

            /// <summary>
            /// 用户表id
            /// </summary>           
            public string UserId { get; set; }

            /// <summary>
            /// 签约合同代理人
            /// </summary>           
            public string SigningAgentName { get; set; }

            /// <summary>
            /// 签约合同代理人电话
            /// </summary>           
            public string SigningAgentPhone { get; set; }

            /// <summary>
            /// 签约合同代理人Email
            /// </summary>           
            public string SigningAgentEmail { get; set; }

            /// <summary>
            /// 签约合同代理人传真
            /// </summary>           
            public string SigningAgentFax { get; set; }
        }

        public class SearchSecondPartyContactsList_In : ApiTableIn
        {

        }

        public class GetSecondPartyContactsList_Out
        {
            /// <summary>
            /// 主键
            /// </summary> 
            public string Id { get; set; }

            /// <summary>
            /// 用户表id
            /// </summary>           
            public string UserId { get; set; }

            /// <summary>
            /// 签约合同代理人
            /// </summary>           
            public string SigningAgentName { get; set; }

            /// <summary>
            /// 签约合同代理人电话
            /// </summary>           
            public string SigningAgentPhone { get; set; }

            /// <summary>
            /// 签约合同代理人Email
            /// </summary>           
            public string SigningAgentEmail { get; set; }

            /// <summary>
            /// 签约合同代理人传真
            /// </summary>           
            public string SigningAgentFax { get; set; }
        }
        public class AuditContractTerm_IN
        {
            /// <summary>
            /// 合同id
            /// </summary>
            public string ContractId { get; set; }
            /// <summary>
            /// 合同审核结果
            /// </summary>
            public int State { get; set; }
            /// <summary>
            /// 合同条款审核结果
            /// </summary>
            public List<AuditSingleContractTerm> ContractTermAuditList { get; set; }
        }
        public class AuditContractTerm_Team_IN
        {
            /// <summary>
            /// 合同id
            /// </summary>
            public string ContractId { get; set; }
            /// <summary>
            /// 合同审核结果
            /// </summary>
            public int State { get; set; }
            /// <summary>
            /// 合同条款审核结果
            /// </summary>
            public List<AuditSingleContractTerm_Team> ContractTermAuditList { get; set; }
        }
        public class ReviewContractTerm_IN
        {
            /// <summary>
            /// 合同id
            /// </summary>
            public string ContractId { get; set; }
            /// <summary>
            /// 合同审核结果
            /// </summary>
            public int State { get; set; }
            /// <summary>
            /// 合同条款审核结果
            /// </summary>
            public List<ReviewSingleContractTerm> ContractTermAuditList { get; set; }
        }

        public class Isstage_Out
        {
            /// <summary>
            /// 中文服务费
            /// </summary>           
            public string ServiceChargeZh { get; set; }

            /// <summary>
            /// 英文服务费
            /// </summary>           
            public string ServiceChargeEn_Usd { get; set; }

            /// <summary>
            /// 英文服务费
            /// </summary>           
            public string ServiceChargeEn_Eur { get; set; }

            /// <summary>
            /// 英文服务费
            /// </summary>           
            public string ServiceChargeEn_Cny { get; set; }

            /// <summary>
            /// 中文付款期限
            /// </summary>           
            public string PaymentTermZh { get; set; }

            /// <summary>
            /// 英文付款期限
            /// </summary>           
            public string PaymentTermEn { get; set; }

            /// <summary>
            /// 中文服务交付
            /// </summary>           
            public string ServiceDeliveryZh { get; set; }

            /// <summary>
            /// 英文服务交付
            /// </summary>           
            public string ServiceDeliveryEn { get; set; }

        }

        public class ContractIsstage_Out
        {
            public string Id { get; set; }

            /// <summary>
            /// 合同表id
            /// </summary>           
            public string ContractId { get; set; }

            /// <summary>
            /// 中文服务费
            /// </summary>           
            public string ServiceChargeZh { get; set; }

            /// <summary>
            /// 英文服务费
            /// </summary>           
            public string ServiceChargeEn { get; set; }

            /// <summary>
            /// 中文付款期限
            /// </summary>           
            public string PaymentTermZh { get; set; }

            /// <summary>
            /// 英文付款期限
            /// </summary>           
            public string PaymentTermEn { get; set; }

            /// <summary>
            /// 中文服务交付
            /// </summary>           
            public string ServiceDeliveryZh { get; set; }

            /// <summary>
            /// 英文服务交付
            /// </summary>           
            public string ServiceDeliveryEn { get; set; }
        }

        public class AddContractIsstage_In
        {
            /// <summary>
            /// 合同表id
            /// </summary>   
            [Required(ErrorMessage = "合同主键不可为空")]
            public string ContractId { get; set; }

            /// <summary>
            /// 中文服务费
            /// </summary>           
            public string ServiceChargeZh { get; set; }

            /// <summary>
            /// 英文服务费
            /// </summary>           
            public string ServiceChargeEn { get; set; }

            /// <summary>
            /// 中文付款期限
            /// </summary>           
            public string PaymentTermZh { get; set; }

            /// <summary>
            /// 英文付款期限
            /// </summary>           
            public string PaymentTermEn { get; set; }

            /// <summary>
            /// 中文服务交付
            /// </summary>           
            public string ServiceDeliveryZh { get; set; }

            /// <summary>
            /// 英文服务交付
            /// </summary>           
            public string ServiceDeliveryEn { get; set; }
        }

        public class UpdateContractIsstage_In : AddContractIsstage_In
        {
            /// <summary>
            /// 主键
            /// </summary>
            [Required(ErrorMessage = "合同分期信息主键不可为空")]
            public string Id { get; set; }
        }

        /// <summary>
        /// 申请服务是显示内容返回类
        /// </summary>
        public class GetProductInfoByContractId4ServeApply_Out
        {
            /// <summary>
            /// Gtis系列产品(包含Gtis、Vip零售国家、Gtis超级子账号、环球搜、慧思学院、SalesWits)
            /// </summary>
            public GtisSeiresInfo4ServeApply WitsProduct { get; set; } = new();
            /// <summary>
            /// 邓白氏产品
            /// </summary>
            public List<ProductInfo4ServeApply> DBProduct { get; set; } = new();
            /// <summary>
            /// 其他数据产品(海关编码数据)
            /// </summary>
            public List<ProductInfo4ServeApply> OtherDataProduct { get; set; } = new();
            /// <summary>
            /// 合同签约产品列表
            /// </summary>
            public List<ContractSignProduct> ProductList { get; set; } = new();

        }

        public class GetProductInfoByContractId4ServeApply_In
        {
            /// <summary>
            /// 合同ID
            /// </summary>
            public string ContractId { get; set; }
            /// <summary>
            /// 是否用于服务变更场景
            /// </summary>
            public bool IsForServiceChange { get; set; } = false;
        }


        public class ContractSignProduct
        {
            /// <summary>
            /// 产品名称
            /// </summary>
            public string ProductName { get; set; }
            /// <summary>
            /// 服务周期
            /// </summary>
            public int OpeningMonths { get; set; }
            /// <summary>
            /// 首次开通月数
            /// </summary>
            public int? FirstOpeningMonths { get; set; }
            /// <summary>
            /// 产品价格
            /// </summary>           
            public string ProductPrice { get; set; }
        }
        /// <summary>
        /// 申请服务是显示内容返回类_Gtis系列产品参数
        /// </summary>
        public class GtisSeiresInfo4ServeApply
        {
            /// <summary>
            /// 产品列表
            /// </summary>
            public List<ProductInfo4ServeApply> ProductList { get; set; } = new();
            /// <summary>
            /// 账号可选权限列表
            /// </summary>
            public List<EnumGtisSeriesPermission> GtisAccountPermissionList { get; set; } = new();
            /// <summary>
            /// 能否使用优惠券
            /// </summary>
            public bool CouldUseCoupon { get; set; } = false;
            /// <summary>
            /// 优惠券列表
            /// </summary>
            public List<CounponDetail> CouponList { get; set; }
            /// <summary>
            /// 能否使用个人服务天数
            /// </summary>
            public bool CouldUsePrivateDays { get; set; } = false;
            /// <summary>
            /// 可使用的个人服务天数详情
            /// </summary>
            public DurationInfo PrivateDaysInfo { get; set; }
            /// <summary>
            /// 慧思产品合同产品表Id
            /// </summary>
            public string ContractProductInfoSeriesId { get; set; }
            /// <summary>
            /// 可续约的客户编码
            /// </summary>
            public List<string> RenewableContractNumList { get; set; }
            /// <summary>
            /// 超级子账号数
            /// </summary>           
            public int? SuperSubAccountNum { get; set; }
            /// <summary>
            /// 续约时用，在服的账号信息
            /// </summary>
            public List<OriginUserGroupInfo> OriUserGroupInfos { get; set; }
        }

        public class OriginUserGroupInfo
        {
            /// <summary>
            /// 客户编码
            /// </summary>
            public string ContractNum { get; set; }
            /// <summary>
            /// 客户编码对应的Wits服务表主键
            /// </summary>
            public string WitsServeId { get; set; }
            /// <summary>
            /// 账号列表
            /// </summary>
            public List<OriginUser> OriginUserList { get; set; } = new();
            /// <summary>
            /// Gtis系统在服信息
            /// </summary>
            public GtisServiceInfo GtisOnServiceInfo { get; set; }
            /// <summary>
            /// 环球搜在服信息
            /// </summary>
            public GlobalSearchServiceInfo GlobalSearchOnServiceInfo { get; set; }
            /// <summary>
            /// 慧思学院在服信息
            /// </summary>
            public CollegeServiceInfo CollegeOnServiceInfo { get; set; }
            /// <summary>
            /// SalesWits在服信息
            /// </summary>
            public SalesWitsServiceInfo SalesWitsOnServiceInfo { get; set; }
        }


        public class OriginUser
        {
            /// <summary>
            /// 主键Id
            /// </summary>
            public string Id { get; set; }
            /// <summary>
            /// Gtis系统中的userId
            /// </summary>
            public string SysUserId { get; set; }
            /// <summary>
            /// 账号类型
            /// </summary>
            public int AccountType { get; set; }
            /// <summary>
            /// 账号
            /// </summary>
            public string AccountNumber { get; set; }
            /// <summary>
            /// 账号共享人数
            /// </summary>
            public int SharePeopleNum { get; set; }
            /// <summary>
            /// 子账号授权国家次数
            /// </summary>
            public int AuthorizationNum { get; set; }
            /// <summary>
            /// Gtis权限
            /// </summary>
            public bool GtisPermission { get; set; }
            /// <summary>
            /// Vip权限
            /// </summary>
            public bool VipPermission { get; set; }
            /// <summary>
            /// 环球搜权限
            /// </summary>
            public bool GlobalSearchPermission { get; set; }
            /// <summary>
            /// 慧思学院权限
            /// </summary>
            public bool CollegePermission { get; set; }
            /// <summary>
            /// SalesWits权限
            /// </summary>
            public bool SalesWitsPermission { get; set; }
            /// <summary>
            /// 使用者
            /// </summary>
            public List<OriginPhoneUser> OriPhoneUsers { get; set; } = new();
            /// <summary>
            /// SalesWits绑定的使用者Id
            /// </summary>
            public string SalesWitsBindPhoneId { get; set; }
        }
        /// <summary>
        /// 使用者信息
        /// </summary>
        public class OriginPhoneUser
        {
            /// <summary>
            /// 使用者id
            /// </summary>
            public string SysUserPhoneID { get; set; }
            /// <summary>
            /// 使用者
            /// </summary>
            public string LinkMan { get; set; }
            /// <summary>
            /// 已绑定SalesWits
            /// </summary>
            public bool SalesWitsPermission { get; set; }
        }

        /// <summary>
        /// 2025.7 新版服务申请时数据内容，基本继承ProductInfo_Out
        /// </summary>
        public class ProductInfo4ServeApply
        {
            public string Id { get; set; }
            /// <summary>
            /// 合同表id
            /// </summary>
            public string ContractId { get; set; }
            /// <summary>
            /// 产品表id
            /// </summary>
            public string ProductId { get; set; }
            /// <summary>
            /// 产品名称
            /// </summary>
            public string ProductName { get; set; }
            /// <summary>
            /// 产品英文名称
            /// </summary>
            public string ProductNameEN { get; set; }
            /// <summary>
            /// 产品描述
            /// </summary>
            public string ProductDescription { get; set; }
            /// <summary>
            /// 产品英文描述
            /// </summary>
            public string ProductDescriptionEn { get; set; }
            /// <summary>
            /// 产品编号
            /// 2025年6月16日 修改
            /// </summary>
            public string ProductNum { get; set; }
            /// <summary>
            /// 币种
            /// </summary>
            public int Currency { get; set; }
            /// <summary>
            /// 币种
            /// </summary>
            public string CurrencyName
            {
                get
                {
                    List<Db_sys_dictionary> dictionary = Dictionary.Currency.Where(e => e.Value == Currency.ToString()).ToList();
                    if (dictionary.Count > 0)
                    {
                        return dictionary.First().Name;
                    }
                    else
                    {
                        return "";
                    }
                    //return Dictionary.Currency.First(e => e.Value == Currency.ToString()).Name;
                }
            }
            /// <summary>
            /// 售价
            /// </summary>
            public decimal Price { get; set; }
            /// <summary>
            /// 计费参数
            /// </summary>
            public int ChargingParameters { get; set; }
            /// <summary>
            /// 计费参数
            /// </summary>
            public string ChargingParametersName
            {
                get
                {
                    List<Db_sys_dictionary> dictionary = Dictionary.ChargingParameters.Where(e => e.Value == ChargingParameters.ToString()).ToList();
                    if (dictionary.Count > 0)
                    {
                        return dictionary.First().Name;
                    }
                    else
                    {
                        return "";
                    }
                    //return Dictionary.ChargingParameters.First(e => e.Value == ChargingParameters.ToString()).Name;
                }
            }
            /// <summary>
            /// 币种价格周期
            /// </summary>
            public string CurrencyPrice
            {
                get
                {
                    return Price.ToString("N2") + " " + Currency.ToEnum<EnumCurrencyLite>().GetEnumDescription() + " " + ChargingParametersName.Replace("每", "/");
                }
            }
            /// <summary>
            /// 产品类型
            /// </summary>
            public EnumProductType ProductType { get; set; }
            /// <summary>
            /// 开通月数
            /// </summary>
            public int? OpeningMonths { get; set; }
            /// <summary>
            /// 首次开通月数
            /// </summary>
            public int? FirstOpeningMonths { get; set; }
            /// <summary>
            /// 开通年数
            /// </summary>
            public int? OpeningYears { get; set; }
            /// <summary>
            /// 国家
            /// </summary>
            public string Countries { get; set; }
            /// <summary>
            /// 主账号个数
            /// </summary>
            public int? PrimaryAccountsNum { get; set; }
            /// <summary>
            /// 子账号个数
            /// </summary>
            public int? SubAccountsNum { get; set; }
            /// <summary>
            /// 编码个数
            /// </summary>
            public int? CodesNum { get; set; }
            /// <summary>
            /// 套数
            /// </summary>
            public int? SetNum { get; set; }
            /// <summary>
            /// 期数
            /// </summary>
            public int? PeriodsNum { get; set; }
            /// <summary>
            /// 超级子账号数
            /// </summary>           
            public int? SuperSubAccountNum { get; set; }
            /// <summary>
            /// 定制报告下载次数
            /// </summary>           
            public int? CustomizedReportDownloadsNum { get; set; }
            /// <summary>
            /// 创建人
            /// </summary>
            public string CreateUser { get; set; }
            /// <summary>
            /// 创建时间
            /// </summary>
            public DateTime? CreateDate { get; set; }
            /// <summary>
            /// 申请id
            /// </summary>
            public string ProductServiceInfoApplId { get; set; }
            /// <summary>
            /// 申请状态
            /// </summary>
            public int? ProductServiceInfoApplState { get; set; }
            /// <summary>
            /// 申请状态
            /// </summary>
            public string ProductServiceInfoApplStateName
            {
                get
                {
                    return ProductServiceInfoApplState == null ? null : Dictionary.ProcessStatus.First(e => e.Value == ProductServiceInfoApplState.ToString()).Name;
                }
            }
            /// <summary>
            /// 申请类型
            /// </summary>
            public int? ProductServiceInfoApplProcessingType { get; set; }
            /// <summary>
            /// 申请类型
            /// </summary>
            public string ProductServiceInfoApplProcessingTypeName
            {
                get
                {
                    return ProductServiceInfoApplProcessingType == null ? null : Dictionary.ProcessingType.First(e => e.Value == ProductServiceInfoApplProcessingType.ToString()).Name;
                }
            }
            /// <summary>
            /// 服务状态
            /// </summary>
            public int? ServiceState { get; set; }
            /// <summary>
            /// 服务状态
            /// </summary>
            public string ServiceStateName
            {
                get
                {
                    return ServiceState == null ? null : ((EnumContractServiceState)ServiceState).GetEnumDescription();
                }
            }
            /// <summary>
            /// 服务周期
            /// </summary>
            public int? ServiceCycle { get; set; }
            /// <summary>
            /// 周期月份最短
            /// </summary>           
            public int? ServiceCycleStart { get; set; }
            /// <summary>
            /// 周期月份最长
            /// </summary>           
            public int? ServiceCycleEnd { get; set; }
            /// <summary>
            /// 产品价格Id
            /// </summary>           
            public string ProductPriceId { get; set; }
            /// <summary>
            /// 产品价格
            /// </summary>           
            public string ProductPrice { get; set; }
            /// <summary>
            /// 子账号价格id
            /// </summary>
            public string SubAccountsProductPriceId { get; set; }
            /// <summary>
            /// 子账号价格
            /// </summary>
            public decimal? SubAccountsProductPrice { get; set; }
            /// <summary>
            /// 价格
            /// </summary>           
            public decimal ContractProductinfoPrice { get; set; }
            /// <summary>
            /// 价格
            /// </summary>           
            public decimal ContractProductinfoPriceTotal { get; set; }
            /// <summary>
            /// 开通月数
            /// </summary>
            public DateTime? ProtectionDeadline
            {
                get
                {
                    if (ProductType == EnumProductType.Periodicals)
                    {
                        DateTime result = DateTime.Now.AddMonths(PeriodsNum.Value);
                        return new DateTime(result.Year, result.Month, DateTime.DaysInMonth(result.Year, result.Month));
                    }
                    else if (ProductType == EnumProductType.Event)
                    {
                        return null;
                    }
                    else if (ProductType == EnumProductType.Gtis && ProductId == Guid.Empty.ToString())
                    {
                        return null;
                    }
                    else
                    {
                        return DateTime.Now.AddMonths(OpeningMonths.Value);
                    }
                }
            }
            /// <summary>
            /// 产品表id(组合产品的上级节点)
            /// </summary>           
            public string ParentProductId { get; set; }
            /// <summary>
            /// 产品名称(组合产品的上级节点)
            /// </summary>           
            public string ParentProductName { get; set; }
            /// <summary>
            /// 产品价格Id(组合产品的上级节点)
            /// </summary>           
            public string ParentProductPriceId { get; set; }
            /// <summary>
            /// 产品价格(组合产品的上级节点)
            /// </summary>           
            public string ParentProductPrice { get; set; }
            /// <summary>
            /// 价格(组合产品的上级节点)
            /// </summary>           
            public decimal ParentContractProductinfoPrice { get; set; }
            /// <summary>
            /// 价格(组合产品的上级节点)
            /// </summary>           
            public decimal ParentContractProductinfoPriceTotal { get; set; }
            /// <summary>
            /// 套餐产品的内容
            /// </summary>
            public List<ProductInfo_Out> ChildNode { get; set; }
            /// <summary>
            /// 备注
            /// </summary>
            public string Remark { get; set; }
            /// <summary>
            /// 产品简介
            /// </summary>
            public string BriefIntroduction { get; set; }
            /// <summary>
            /// 组合产品简介
            /// </summary>
            public string ParentBriefIntroduction { get; set; }
            /// <summary>
            /// 国内国外
            /// </summary>
            public int? NotForSale { get; set; }
            /// <summary>
            /// 服务开始时间
            /// </summary>           
            public DateTime? ServiceStartDate { get; set; }
            /// <summary>
            /// 服务结束时间
            /// </summary>           
            public DateTime? ServiceEndDate { get; set; }
            /// <summary>
            /// 开通周期
            /// </summary>
            public string OpenServiceCycle { get; set; }
            /// <summary>
            /// 备注
            /// </summary>
            public string ServiceRemark { get; set; }
            /// <summary>
            /// 优惠券列表
            /// </summary>
            public List<CounponDetail> CouponList { get; set; }

            /*以下是新增*/

            /// <summary>
            /// 合同产品信息表系列Id
            /// </summary>
            public string ContractProductInfoSeriesId { get; set; }
            /// <summary>
            /// 合同产品信息表系列Id
            /// </summary>
            public string ContractProductInfoId { get; set; }
            /// <summary>
            /// SalesWits的充值金额
            /// </summary>
            public decimal SalesWitsAddCredit { get; set; }
            /// <summary>
            /// Gtis产品是否存在Vip零售国家
            /// </summary>
            public bool IsGtisHasVip { get; set; }

        }


        public class AddWitsAppl_In
        {
            /// <summary>
            /// 合同Id
            /// </summary>
            public string ContractId { get; set; }
            /// <summary>
            /// 合同产品信息GroupId不可为空
            /// </summary>
            [Required(ErrorMessage = "合同产品信息SeriesId不可为空")]
            public string ContractProductInfoSeriesId { get; set; }
            /// <summary>
            /// 优惠类型
            /// </summary>
            public EnumGtisDiscountType DiscountType { get; set; }
            /// <summary>
            /// 选中的优惠券Id列表
            /// </summary>
            public List<string>? CounponDetailIdList { get; set; }
            /// <summary>
            /// 延长的总服务天数
            /// </summary>
            public int? PerlongServiceDays { get; set; }
            /// <summary>
            /// 赠送的个人服务天数
            /// </summary>
            public int? PrivateServiceDays { get; set; }
            /// <summary>
            /// 需自费的个人服务天数
            /// </summary>
            public int? OverServiceDays { get; set; }
            /// <summary>
            /// 账号生成方式
            /// </summary>
            public EnumGtisAccountGenerationMethod AccountGenerationMethod { get; set; }
            /// <summary>
            /// 加急处理状态
            /// </summary>
            public bool IsUrgent { get; set; }
            /// <summary>
            /// （续约）甲方公司
            /// </summary>
            public string RenewFirstParty { get; set; }
            /// <summary>
            /// (续约)客户编码
            /// </summary>
            public string RenewContractNum { get; set; }
            /// <summary>
            /// 使用原有账号时在服信息的WitsServiceId
            /// </summary>
            public string OriWitsServiceId { get; set; }
            /// <summary>
            /// 共享使用总数
            /// </summary>
            public int? ShareUsageNum { get; set; }
            /// <summary>
            /// 合计的超级子账号个数
            /// </summary>
            public int SuperSubAccountNum { get; set; }
            /// <summary>
            /// 备注
            /// </summary>
            public string Remark { get; set; }
            /// <summary>
            /// Gtis申请数据
            /// </summary>
            public AddGtisSeriesAppl_In_Gtis? GtisApply { get; set; }
            /// <summary>
            /// 环球搜申请数据
            /// </summary>
            public AddGtisSeriesAppl_In_GlobalSearch? GlobalSearchApply { get; set; }
            /// <summary>
            /// 慧思学院申请数据
            /// </summary>
            public AddGtisSeriesAppl_In_College? CollegeApply { get; set; }
            /// <summary>
            /// SalesWits申请数据
            /// </summary>
            public AddGtisSeriesAppl_In_SalesWits? SalesWitsApply { get; set; }
            /// <summary>
            /// 账号信息列表
            /// </summary>
            public List<AddOrAuditWitsApplUser> UserList { get; set; }
        }

        public class AddGtisSeriesAppl_In_Gtis
        {
            /// <summary>
            /// 合同产品信息表id
            /// </summary>           
            public string ContractProductInfoId { get; set; }
            /// <summary>
            /// 产品表id
            /// </summary>           
            public string ProductId { get; set; }
            /// <summary>
            /// 子账号数量
            /// </summary>
            public int SubAccountsNum { get; set; }
            /// <summary>
            /// 子账号授权国家次数
            /// </summary>
            public int? AuthorizationNum { get; set; }
            /// <summary>
            /// 服务月份
            /// </summary>
            public int? ServiceMonth { get; set; }
            /// <summary>
            /// 服务开始时间
            /// </summary>
            public DateTime? ServiceCycleStart { get; set; }
            /// <summary>
            /// 服务结束时间
            /// </summary>
            public DateTime? ServiceCycleEnd { get; set; }
            /// <summary>
            /// 优惠后服务月份
            /// </summary>
            public int? ServiceMonthAfterDiscount { get; set; }
            /// <summary>
            /// 优惠后服务开始时间
            /// </summary>
            public DateTime? ServiceCycleStartAfterDiscount { get; set; }
            /// <summary>
            /// 优惠后服务结束时间
            /// </summary>
            public DateTime? ServiceCycleEndAfterDiscount { get; set; }
            /// <summary>
            /// 是否添加零售国家
            /// 1-添加；2-不添加
            /// </summary>
            public int? RetailCountry { get; set; }
            /// <summary>
            /// 开通国家
            /// </summary>
            public List<GtisApplCountry>? GtisApplCountry { get; set; }
            /// <summary>
            /// 禁用下载/导出权限
            /// </summary>
            public bool ForbidSearchExport { get; set; }
            /// <summary>
            /// 是否开通定制报告
            /// </summary>
            public bool WordRptPermissions { get; set; }
            /// <summary>
            /// 定制报告每年总次数
            /// </summary>
            public int? WordRptMaxTimes { get; set; }

        }
        public class AddGtisSeriesAppl_In_GlobalSearch
        {
            /// <summary>
            /// 产品表id
            /// </summary>           
            public string ProductId { get; set; }
            /// <summary>
            /// 合同产品信息表id
            /// </summary>           
            public string ContractProductInfoId { get; set; }
            /// <summary>
            /// 环球搜账号数量(主+子)
            /// </summary>
            public int? AccountCount { get; set; }
            /// <summary>
            /// 环球搜服务月份
            /// </summary>
            public int? ServiceMonth { get; set; }
            /// <summary>
            /// 环球搜服务开始时间
            /// </summary>
            public DateTime? ServiceCycleStart { get; set; }
            /// <summary>
            /// 环球搜服务结束时间
            /// </summary>
            public DateTime? ServiceCycleEnd { get; set; }
            /// <summary>
            /// 优惠后服务月份
            /// </summary>
            public int? ServiceMonthAfterDiscount { get; set; }
            /// <summary>
            /// 优惠后环球搜服务开始时间
            /// </summary>
            public DateTime? ServiceCycleStartAfterDiscount { get; set; }
            /// <summary>
            /// 优惠后环球搜服务结束时间
            /// </summary>
            public DateTime? ServiceCycleEndAfterDiscount { get; set; }
        }
        public class AddGtisSeriesAppl_In_College
        {
            /// <summary>
            /// 产品表id
            /// </summary>           
            public string ProductId { get; set; }
            /// <summary>
            /// 合同产品信息表id
            /// </summary>           
            public string ContractProductInfoId { get; set; }
            /// <summary>
            /// 慧思学院账号数量
            /// </summary>
            public int? AccountCount { get; set; }
            /// <summary>
            /// 慧思学院服务月份
            /// </summary>
            public int? ServiceMonth { get; set; }
            /// <summary>
            /// 慧思学院服务开始时间
            /// </summary>
            public DateTime? ServiceCycleStart { get; set; }
            /// <summary>
            /// 慧思学院服务结束时间
            /// </summary>
            public DateTime? ServiceCycleEnd { get; set; }
            /// <summary>
            /// 优惠后服务月份
            /// </summary>
            public int? ServiceMonthAfterDiscount { get; set; }
            /// <summary>
            /// 优惠后慧思学院服务开始时间
            /// </summary>
            public DateTime? ServiceCycleStartAfterDiscount { get; set; }
            /// <summary>
            /// 优惠后慧思学院服务结束时间
            /// </summary>
            public DateTime? ServiceCycleEndAfterDiscount { get; set; }
        }
        public class AddGtisSeriesAppl_In_SalesWits
        {
            /// <summary>
            /// 产品表id
            /// </summary>           
            public string ProductId { get; set; }
            /// <summary>
            /// 合同产品信息表id
            /// </summary>           
            public string ContractProductInfoId { get; set; }
            /// <summary>
            /// SalesWits账号数量
            /// </summary>
            public int? AccountCount { get; set; }
            /// <summary>
            /// SalesWits服务月份
            /// </summary>
            public int? ServiceMonth { get; set; }
            /// <summary>
            /// SalesWits服务开始时间
            /// </summary>
            public DateTime? ServiceCycleStart { get; set; }
            /// <summary>
            /// SalesWits服务结束时间
            /// </summary>
            public DateTime? ServiceCycleEnd { get; set; }
            /// <summary>
            /// 优惠后服务月份
            /// </summary>
            public int? ServiceMonthAfterDiscount { get; set; }
            /// <summary>
            /// 优惠后SalesWits服务开始时间
            /// </summary>
            public DateTime? ServiceCycleStartAfterDiscount { get; set; }
            /// <summary>
            /// 优惠后SalesWits服务结束时间
            /// </summary>
            public DateTime? ServiceCycleEndAfterDiscount { get; set; }
            /// <summary>
            /// SalesWtis充值金额
            /// </summary>
            public decimal? AddCredit { get; set; }
            /// <summary>
            /// 赠送邮件数量
            /// </summary>
            public int EmailCount { get; set; }
            /// <summary>
            /// 赠送token数量
            /// </summary>
            public int TokenCount { get; set; }
        }

        public class AddGtisSeriesAppl_In_UserList
        {
            /// <summary>
            /// Gtis系统的UserID
            /// </summary>
            public string SysUserId { get; set; }
            /// <summary>
            /// 账号类型
            /// </summary>
            public int? AccountType { get; set; }
            /// <summary>
            /// 账号
            /// </summary>
            public string AccountNumber { get; set; }
            /// <summary>
            /// 每个账户共享人数
            /// </summary>
            public int? SharePeopleNum { get; set; }
            /// <summary>
            /// Gtis权限
            /// </summary>
            public bool? GtisPermission { get; set; } = false;
            /// <summary>
            /// Vip零售国家权限
            /// </summary>
            public bool? VipPermission { get; set; } = false;
            /// <summary>
            /// 环球搜权限
            /// </summary>
            public bool? GlobalSearchPermission { get; set; } = false;
            /// <summary>
            /// 慧思学院权限
            /// </summary>
            public bool? CollegePermission { get; set; } = false;
            /// <summary>
            /// SalesWits权限
            /// </summary>
            public bool? SalesWitsPermission { get; set; } = false;
        }


    }
}
