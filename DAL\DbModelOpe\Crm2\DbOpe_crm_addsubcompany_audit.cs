using CRM2_API.BLL;
using CRM2_API.Common.Cache;
using CRM2_API.Common.JWT;
using CRM2_API.DAL.DbCommon;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.System;
using DocumentFormat.OpenXml.ExtendedProperties;
using DocumentFormat.OpenXml.Presentation;
using DocumentFormat.OpenXml.Spreadsheet;
using LgyUtil;
using SqlSugar;
using System.Collections.Generic;

namespace CRM2_API.DAL.DbModelOpe.Crm2
{
    /// <summary>
    /// crm_addsubcompany_audit表操作
    /// </summary>
    public class DbOpe_crm_addsubcompany_audit : DbOperateCrm2Ex<Db_crm_addsubcompany_audit, DbOpe_crm_addsubcompany_audit>
    {
        private List<string> ALLSTATES = new List<string> { "3cb3e614-487f-42b1-ff56-795514438b28" };
        public List<int> GetUserStates(string userId)
        {
            List<int> r = new List<int>();
            var list = RedisCache.UserButtonRight.GetUserButtonRight(UserId)?.ToList();
            if (list.Find(l => ALLSTATES.Contains(l.Id)) == null)
            {
                r.Add((int)EnumCustomerAuditState.InProcess);
            }
            else
            {
                r.Add((int)EnumCustomerAuditState.InProcess);
                r.Add((int)EnumCustomerAuditState.OK);
                r.Add((int)EnumCustomerAuditState.Refuse);
                r.Add((int)EnumCustomerAuditState.Split);
            }
            return r.Distinct().ToList();
        }
        
        /// <summary>
        /// 查询审核列表（20240524 分权限显示）
        /// </summary>
        /// <param name="searchCustomerAudit_IN"></param>
        /// <param name="userId"></param>
        /// <param name="mergeManage"></param>
        /// <param name="total"></param>
        /// <returns></returns>
        public List<SearchCustomerAudit_OUT> SearchCustomerCompanyRelationshipAudit(SearchCustomerAudit searchCustomerAudit_IN, string userId, ref int total, bool mergeManage = true)
        {
            List<string> tempSplit = new List<string>();
            var effectiveNameInputFlag = new ChineseAnalyzerCls().CheckEffectiveNameInput(searchCustomerAudit_IN.CustomerName, ref tempSplit);
            if (mergeManage)
            {
                if (!effectiveNameInputFlag)
                {
                    var states = GetUserStates(userId);
                    if (!ArrayUtil.IsNullOrEmpty(searchCustomerAudit_IN.State))
                    {
                        foreach (var state in searchCustomerAudit_IN.State)
                        {
                            if (!states.Contains(state))
                            {
                                searchCustomerAudit_IN.State.Remove(state);
                            }
                        }
                    }
                    else
                    {
                        searchCustomerAudit_IN.State = states;
                    }
                    if (searchCustomerAudit_IN.State.Count == 0)
                    {
                        return new List<SearchCustomerAudit_OUT>();
                    }
                }
            }
            searchCustomerAudit_IN.SplitCustomerName = tempSplit;
            var q1 = DbOpe_crm_mergecompany_audit.Instance.SearchCustomerCompanyRelationshipAudit(searchCustomerAudit_IN, mergeManage);
            var q2 = DbOpe_crm_addsubcompany_audit.Instance.SearchCustomerCompanyRelationshipAudit(searchCustomerAudit_IN, mergeManage);
            var MergeCustomerInProcessCount = DbOpe_crm_mergecompany_audit.Instance.GetAuditCount(searchCustomerAudit_IN);
            var AddSubCompanyInProcessCount = DbOpe_crm_addsubcompany_audit.Instance.GetAuditCount(searchCustomerAudit_IN);
            var list = Db.UnionAll(q1, q2)
                .WhereIF(!ArrayUtil.IsNullOrEmpty(searchCustomerAudit_IN.Type), o => SqlFunc.ContainsArray(searchCustomerAudit_IN.Type, o.Type))
                .OrderByDescending(it => it.ApplicantDate)
                .Mapper(it =>
                {
                    it.CustomerSourceName = it.CustomerSource.GetEnumDescription();
                    it.CustomerLevelName = it.CustomerLevel.GetEnumDescription();
                    it.TypeName = it.Type.GetEnumDescription();
                    it.StateName = it.State.GetEnumDescription();
                    //it.ApplicantOrgName = Db.Queryable<Db_v_userwithorg>()
                    //.ToParentList(o => o.ParentId, it.ApplicantOrg)
                    //.OrderBy(o => o.OrgType)
                    //.Select(o => o.OrgName)
                    //.JoinToString("/");
                    it.InProcessCount = MergeCustomerInProcessCount + AddSubCompanyInProcessCount;
                })
                .ToPageList(searchCustomerAudit_IN.PageNumber, searchCustomerAudit_IN.PageSize, ref total);
            Db.ThenMapper(list, item =>
            {
                item.SubCompanys = Db.Queryable<Db_crm_customer_subcompany>()
                .Where(it => it.Deleted == (int)EnumCustomerDel.NotDel && it.IsMain == (int)EnumCustomerCompanyMain.Sub)
                .Select(it => new CompanySimple_OUT() { Id = it.Id, CompanyName = it.CompanyName, CustomerName = it.CompanyName, CreditCode = it.CreditCode, CustomerId = it.CustomerId, Contacts = it.Contacts })
                .SetContext(x => x.CustomerId, () => item.CustomerId, item).ToList();
            });
            return list;
        }
        /// <summary>
        /// 查询审核列表
        /// </summary>
        /// <param name="searchCustomerApplicant_IN"></param>
        /// <returns></returns>
        public List<GetOrganizationWithUserTree_Out> SearchCustomerCompanyRelationshipApplicants(SearchCustomerAudit searchCustomerApplicant_IN)
        {
            var con = searchCustomerApplicant_IN.MappingTo<SearchCustomerAudit>();
            var q1 = DbOpe_crm_mergecompany_audit.Instance.SearchCustomerCompanyRelationshipAudit(con, true);
            var q2 = DbOpe_crm_addsubcompany_audit.Instance.SearchCustomerCompanyRelationshipAudit(con, true);
            var userList = Db.UnionAll(q1, q2)
                .WhereIF(!ArrayUtil.IsNullOrEmpty(con.Type), o => SqlFunc.ContainsArray(con.Type, o.Type))
                .OrderByDescending(it => it.ApplicantDate)
                .Select(it => it.ApplicantId)
                .ToList().Distinct().ToList();
            // 获取获取系统中的组织机构[携带用户]基础树
            var baseData = DbOpe_sys_organization.Instance.GetBaseOrgWithUserTree();
            List<GetOrganizationWithUserTree_Out> outData = new();
            if (baseData is not null and { Count: > 0 })
            {
                foreach (GetBaseOrgWithUserTree item in baseData)
                {
                    var childs = ConvertHasUserTreeOutModel(item, userList);
                    if (childs.Count > 0)
                    {
                        var temp = new GetOrganizationWithUserTree_Out()
                        {
                            Id = item.Id,
                            Name = item.OrgName,
                            Leaf = false,
                            Child = childs
                        };
                        outData.Add(temp);
                    }
                }
            }
            return outData;
        }
        public List<GetOrganizationWithUserTree_Out> ConvertHasUserTreeOutModel(GetBaseOrgWithUserTree item, List<string> userIds = default)
        {
            List<GetOrganizationWithUserTree_Out> outData = new();
            GetOrganizationWithUserTree_Out temp = default;
            if (item == null)
            {
                return outData;
            }
            var orgChild = item.OrgChild;
            var userChild = item.UserChild;
            if (orgChild is not null and { Count: > 0 })
            {
                foreach (GetBaseOrgWithUserTree orgItem in orgChild)
                {
                    var childs = ConvertHasUserTreeOutModel(orgItem, userIds);
                    if (childs.Count > 0)
                    {
                        var tempOut = new GetOrganizationWithUserTree_Out()
                        {
                            Id = orgItem.Id,
                            Name = orgItem.OrgName,
                            Leaf = false,
                            Child = childs
                        };
                        outData.Add(tempOut);
                    }
                }
            }
            if (userChild is not null and { Count: > 0 })
            {
                foreach (Db_v_userwithorg userItem in userChild)
                {
                    if (userIds.Contains(userItem.Id))
                    {
                        temp = new GetOrganizationWithUserTree_Out()
                        {
                            Id = userItem.Id,
                            Name = userItem.Name,
                            UserName = userItem.UserName,
                            UserNum = userItem.UserNum,
                            AvatarImage = userItem.AvatarImage,
                            AvatarImageUrl = string.IsNullOrEmpty(userItem.AvatarImage) ? null : $"/api/Attachfile/Preview?id={userItem.Id}&fileType=AvatarImage&Authorization={TokenModel.Token}",
                            Leaf = true,
                            MaxSaveCustomer = userItem.MaxSaveCustomer > userItem.MaxSaveCustomerMannualInput ? userItem.MaxSaveCustomer : userItem.MaxSaveCustomerMannualInput,
                        };
                        outData.Add(temp);
                    }
                }
            }
            return outData;
        }


        /// <summary>
        /// 查询客户审核历史记录
        /// </summary>
        /// <param name="getCustomerAuditRecord_IN"></param>
        /// <param name="total"></param>
        /// <returns></returns>
        public List<GetCustomerAuditRecord_OUT> GetCustomerAuditRecords(GetCustomerAuditRecord_IN getCustomerAuditRecord_IN, ref int total)
        {
            var q1 = DbOpe_crm_mergecompany_audit.Instance.GetCustomerAuditRecords(getCustomerAuditRecord_IN);
            var q2 = DbOpe_crm_addsubcompany_audit.Instance.GetCustomerAuditRecords(getCustomerAuditRecord_IN);
            var q3 = DbOpe_crm_splitcompany_audit.Instance.GetCustomerAuditRecords(getCustomerAuditRecord_IN);
            var list = Db.UnionAll(q1, q2, q3)
                .OrderByDescending(it => it.ApplicantDate)
                .Mapper(it =>
                {
                    it.CustomerSourceName = it.CustomerSource.GetEnumDescription();
                    it.CustomerLevelName = it.CustomerLevel.GetEnumDescription();
                    it.TypeName = it.Type.GetEnumDescription();
                    it.StateName = it.State.GetEnumDescription().Replace("待审核", "创建");
                })
                .ToPageList(getCustomerAuditRecord_IN.PageNumber, getCustomerAuditRecord_IN.PageSize, ref total);
            foreach (var obj in list)
            {
                switch (obj.Type)
                {
                    case EnumCustomerAuditType.AddSub:
                        if (obj.State == EnumCustomerAuditState.Split)
                        {
                            obj.State = EnumCustomerAuditState.OK;
                            obj.StateName = obj.State.GetEnumDescription();
                        }
                        obj.AuditSubCompanys = Db.Queryable<Db_crm_addsubcompany_audit_subcompany>()
                            .LeftJoin<Db_crm_addsubcompany_audit>((sub, audit) => audit.Id == sub.AddSubCompanyAuditId)
                            .Where((sub, audit) => sub.Deleted == (int)EnumCustomerDel.NotDel && audit.Deleted == (int)EnumCustomerDel.NotDel)
                            .Where((sub, audit) => audit.Id == obj.Id)
                            .Select((sub, audit) => new AuditCompanySimple_OUT() { Id = sub.Id, AuditId = audit.Id, CompanyName = sub.CompanyName, CreditCode = sub.CreditCode, CustomerId = audit.CustomerId })
                            .ToList();
                        break;
                    case EnumCustomerAuditType.Split:
                        obj.SplitSubCompanys = Db.Queryable<Db_crm_splitcompany_audit_companylist>()
                            .LeftJoin<Db_crm_splitcompany_audit>((sub, audit) => audit.Id == sub.SplitCompanyAuditId)
                            .LeftJoin<Db_crm_customer_subcompany>((sub, audit, company) => sub.CustomerSubCompanyId == company.Id && company.Deleted == (int)EnumCustomerDel.NotDel)
                            .Where((sub, audit, company) => sub.Deleted == (int)EnumCustomerDel.NotDel && audit.Deleted == (int)EnumCustomerDel.NotDel)
                            .Where((sub, audit, company) => audit.Id == obj.Id)
                            .Select((sub, audit, company) => new AuditCompanySimple_OUT() { Id = sub.Id, AuditId = audit.Id, CompanyName = company.CompanyName, CreditCode = company.CreditCode, CustomerId = audit.CustomerId })
                            .ToList();
                        break;
                    case EnumCustomerAuditType.Merge:
                        if (obj.State == EnumCustomerAuditState.Split)
                        {
                            obj.State = EnumCustomerAuditState.OK;
                            obj.StateName = obj.State.GetEnumDescription();
                        }
                        break;
                }
            }

            //Db.ThenMapper(list, item =>
            //{
            //    item.AuditSubCompanys = Db.Queryable<Db_crm_addsubcompany_audit_subcompany>()
            //    .LeftJoin<Db_crm_addsubcompany_audit>((sub,audit)=>audit.Id ==sub.AddSubCompanyAuditId)
            //    .Where((sub, audit) => sub.Deleted == (int)EnumCustomerDel.NotDel && audit.Deleted == (int)EnumCustomerDel.NotDel)
            //    .Select((sub, audit) => new AuditCompanySimple_OUT() { Id = sub.Id, AuditId = audit.Id, CompanyName = sub.CompanyName, CreditCode = sub.CreditCode, CustomerId = audit.CustomerId })
            //    .MergeTable()
            //    .SetContext(x => x.AuditId, () => item.Id, item).ToList();
            //    item.SplitSubCompanys = Db.Queryable<Db_crm_splitcompany_audit_companylist>()
            //    .LeftJoin<Db_crm_splitcompany_audit>((sub, audit) => audit.Id == sub.SplitCompanyAuditId)
            //    .LeftJoin<Db_crm_customer_subcompany>((sub, audit,company) => sub.CustomerSubCompanyId == company.Id&&company.Deleted == (int)EnumCustomerDel.NotDel)
            //    .Where((sub, audit, company) => sub.Deleted == (int)EnumCustomerDel.NotDel && audit.Deleted == (int)EnumCustomerDel.NotDel)
            //    .Select((sub, audit, company) => new AuditCompanySimple_OUT() { Id = sub.Id, AuditId = audit.Id, CompanyName = company.CompanyName, CreditCode = company.CreditCode, CustomerId = audit.CustomerId })
            //    .MergeTable()
            //    .SetContext(x => x.AuditId, () => item.Id, item).ToList();
            //});
            return list;
        }
        public ISugarQueryable<SearchCustomerAudit_OUT> SearchCustomerCompanyRelationshipAudit(SearchCustomerAudit searchCustomerAudit_IN, bool mergeManage = true)
        {
            DateTime dt = DateTime.Now;
            var dtDay = DateTime.Parse(dt.ToString("yyyy-MM-dd"));
            var weekEnd = dt.AddDays(1 - Convert.ToInt32(dt.DayOfWeek.ToString("d"))).AddDays(6).ToString("yyyy-MM-dd");
            var weekStart = dt.AddDays(1 - Convert.ToInt32(dt.DayOfWeek.ToString("d"))).ToString("yyyy-MM-dd");
            var inProcessAuditCount = GetAuditCount(searchCustomerAudit_IN);
            List<string> customerIds = new List<string>();
            if (!string.IsNullOrEmpty(searchCustomerAudit_IN.CustomerId))
            {
                if (!mergeManage)
                {
                    customerIds = DbOpe_crm_mergecompany_relationship.Instance.GetMergedCustomerIds(searchCustomerAudit_IN.CustomerId);
                }
                else
                {
                    customerIds = new List<string>() { searchCustomerAudit_IN.CustomerId };
                }
            }
            var exp = Expressionable.Create<Db_crm_addsubcompany_audit, Db_crm_customer_copy, Db_v_userwithorg, Db_sys_user>();
            foreach (var word in searchCustomerAudit_IN.SplitCustomerName)
            {
                exp.And((audit, customerCopy, user, reviewer) => customerCopy.CompanyName.Contains(word));
            }
            var q = Queryable
                .LeftJoin<Db_crm_customer_copy>((audit, customerCopy) => audit.Id == customerCopy.AuditId && audit.CustomerId == customerCopy.CustomerId && customerCopy.AuditType == (int)EnumCustomerCopyAuditType.AddSub)
                //.InnerJoin<Db_crm_customer_subcompany>((audit, customerCopy) => audit.CustomerId == subcompany.CustomerId && subcompany.IsMain == (int)EnumCustomerCompanyMain.Main)
                .LeftJoin<Db_v_userwithorg>((audit, customerCopy, user) => user.Id == audit.ApplicantId)
                .LeftJoin<Db_sys_user>((audit, customerCopy, user, reviewer) => reviewer.Id == audit.ReviewerId)
                .WhereIF(!ArrayUtil.IsNullOrEmpty(customerIds), (audit, customerCopy, user, reviewer) => SqlFunc.ContainsArray(customerIds, audit.CustomerId))
                //.WhereIF(StringUtil.IsNotNullOrEmpty(searchCustomerAudit_IN.CustomerName), (audit, customerCopy, user, reviewer) => customerCopy.CompanyName.Contains(searchCustomerAudit_IN.CustomerName))
                //.WhereIF(StringUtil.IsNotNullOrEmpty(searchCustomerAudit_IN.Contacts), (audit, customerCopy, user, reviewer) => subcompany.Contacts.Contains(searchCustomerAudit_IN.Contacts))
                .Where(exp.ToExpression())
                .WhereIF(StringUtil.IsNotNullOrEmpty(searchCustomerAudit_IN.CustomerNum), (audit, customerCopy, user, reviewer) => customerCopy.CustomerNum.Contains(searchCustomerAudit_IN.CustomerNum))
                .WhereIF(StringUtil.IsNotNullOrEmpty(searchCustomerAudit_IN.ApplicantName), (audit, customerCopy, user, reviewer) => user.Name.Contains(searchCustomerAudit_IN.ApplicantName))
                .WhereIF(!ArrayUtil.IsNullOrEmpty(searchCustomerAudit_IN.State), (audit, customerCopy, user, reviewer) => SqlFunc.ContainsArray(searchCustomerAudit_IN.State, audit.State))
                .WhereIF(!ArrayUtil.IsNullOrEmpty(searchCustomerAudit_IN.ApplicantId), (audit, customerCopy, user, reviewer) => SqlFunc.ContainsArray(searchCustomerAudit_IN.ApplicantId, audit.ApplicantId))
                .Where((audit, customerCopy, user, reviewer) => audit.Deleted == (int)EnumCustomerDel.NotDel)
                .WhereIF(searchCustomerAudit_IN.enumCustomerAuditQueryListType == EnumCustomerAuditQueryListType.Today, (audit, customerCopy, user, reviewer) => audit.CreateDate != null && SqlFunc.DateIsSame(audit.CreateDate.Value, dtDay))
                .WhereIF(searchCustomerAudit_IN.enumCustomerAuditQueryListType == EnumCustomerAuditQueryListType.Week, (audit, customerCopy, user, reviewer) => audit.CreateDate != null && SqlFunc.Between(audit.CreateDate.Value, weekStart, weekEnd))
                .WhereIF(searchCustomerAudit_IN.ApplicantDateStart != null, (audit, customerCopy, user, reviewer) => audit.ApplicantDate != null && SqlFunc.ToDateShort(audit.ApplicantDate) >= SqlFunc.ToDateShort(searchCustomerAudit_IN.ApplicantDateStart))
                .WhereIF(searchCustomerAudit_IN.ApplicantDateEnd != null, (audit, customerCopy, user, reviewer) => audit.ApplicantDate != null && SqlFunc.ToDateShort(audit.ApplicantDate) <= SqlFunc.ToDateShort(searchCustomerAudit_IN.ApplicantDateEnd))
                .Select((audit, customerCopy, user, reviewer) => new SearchCustomerAudit_OUT()
                {
                    Id = audit.Id,
                    CustomerId = audit.CustomerId,
                    CustomerNum = customerCopy.CustomerNum,
                    CustomerName = customerCopy.CompanyName,
                    State = (EnumCustomerAuditState)audit.State,
                    Type = (int)EnumCustomerAuditType.AddSub,
                    MergeCustomerName = SqlFunc.IIF(audit.State == (int)EnumCustomerAuditState.OK,
                    SqlFunc.Subqueryable<Db_crm_addsubcompany_audit_subcompany>()
                    .Where(s => s.AddSubCompanyAuditId == audit.Id)
                    .SelectStringJoin(s => s.CompanyName, ","),
                    SqlFunc.IIF(audit.State == (int)EnumCustomerAuditState.Split,
                    SqlFunc.Subqueryable<Db_crm_addsubcompany_audit_subcompany>()
                    .Where(s => s.AddSubCompanyAuditId == audit.Id)
                    .Where(s => s.State != (int)EnumCustomerAddSubCompanyAuditState.Split)
                    .SelectStringJoin(s => s.CompanyName, ","), "")),
                    ApplicantName = user.UserWithOrgFullName,
                    ApplicantOrg = user.OrganizationId,
                    ApplicantOrgName = user.OrgFullName,
                    ApplicantDate = audit.ApplicantDate.Value.ToString("yyyy-MM-dd HH:mm"),
                    Feedback = audit.Feedback,
                    ReviewerDate = audit.ReviewerDate,
                    RelationshipDescription = audit.RelationshipDescription,
                    ReviewerId = audit.ReviewerId,
                    ReviewerName = SqlFunc.IsNullOrEmpty(audit.ReviewerId) ? "" : reviewer.Name,
                    AvatarImage = user.AvatarImage,
                    ReviewerAvatarImage = SqlFunc.IsNullOrEmpty(audit.ReviewerId) ? "" : reviewer.AvatarImage
                }, true).Mapper(it =>
                {
                    it.CustomerSourceName = it.CustomerSource.GetEnumDescription();
                    it.CustomerLevelName = it.CustomerLevel.GetEnumDescription();
                });
            return q;
        }
        /// <summary>
        /// 验证是不是在合并审核流程中
        /// </summary>
        /// <param name="customerIds"></param>
        /// <param name="throwException"></param>
        public List<string> CheckAuditInProcess(List<string> customerIds, bool throwException = true)
        {
            var query = Queryable
                .Where(audit => audit.State == (int)EnumCustomerMergeAuditState.InProcess)
                .Where(audit => audit.Deleted == (int)EnumCustomerDel.NotDel)
                .Where(audit => SqlFunc.ContainsArray(customerIds, audit.CustomerId))
                .ToList();
            if (query.Count > 0 && throwException)
            {
                var unableNames = DbOpe_crm_customer_subcompany.Instance.GetCustomerMainCompany(query.Select(q => q.CustomerId).ToList());
                var companyNames = unableNames?.Select(c => c?.CompanyName).Where(name => !string.IsNullOrEmpty(name)).ToList() ?? new List<string>();
                throw new ApiException("公司(" + string.Join(",", companyNames) + ")已经在合并审核流程中");
            }
            else
            {
                return query.Select(q => q.CustomerId).ToList();
            }
        }
        /// <summary>
        /// 验证是不是在合并审核流程中
        /// </summary>
        /// <param name="customerIds"></param>
        /// <param name="mergeCompanyAuditId"></param>
        /// <param name="throwException"></param>
        public List<string> CheckAuditInProcessNoSelf(List<string> customerIds,string mergeCompanyAuditId, bool throwException = true)
        {
            var query = Queryable
                .Where(audit => audit.State == (int)EnumCustomerMergeAuditState.InProcess)
                .Where(audit => audit.Deleted == (int)EnumCustomerDel.NotDel)
                .Where(audit => SqlFunc.ContainsArray(customerIds, audit.CustomerId))
                .Where(audit => audit.Id != mergeCompanyAuditId)
                .ToList();
            if (query.Count > 0 && throwException)
            {
                var unableNames = DbOpe_crm_customer_subcompany.Instance.GetCustomerMainCompany(query.Select(q => q.CustomerId).ToList());
                var companyNames = unableNames?.Select(c => c?.CompanyName).Where(name => !string.IsNullOrEmpty(name)).ToList() ?? new List<string>();
                throw new ApiException("公司(" + string.Join(",", companyNames) + ")已经在合并审核流程中");
            }
            else
            {
                return query.Select(q => q.CustomerId).ToList();
            }
        }
        /// <summary>
        /// 取消审核流程
        /// </summary>
        /// <param name="customerIds"></param>
        public void CancelAuditInProcess(List<string> customerIds)
        {
            var query = Queryable
                .Where(audit => audit.State == (int)EnumCustomerMergeAuditState.InProcess)
                .Where(audit => audit.Deleted == (int)EnumCustomerDel.NotDel)
                .Where(audit => SqlFunc.ContainsArray(customerIds, audit.CustomerId))
                .ToList();
            foreach (var q in query)
            {
                q.Deleted = 1;
                Update(q);
            }
        }
        public ISugarQueryable<GetCustomerAuditRecord_OUT> GetCustomerAuditRecords(GetCustomerAuditRecord_IN getCustomerAuditRecord_IN)
        {
            DateTime dt = DateTime.Now;
            var dtDay = DateTime.Parse(dt.ToString("yyyy-MM-dd"));
            var weekEnd = dt.AddDays(1 - Convert.ToInt32(dt.DayOfWeek.ToString("d"))).AddDays(6).ToString("yyyy-MM-dd");
            var weekStart = dt.AddDays(1 - Convert.ToInt32(dt.DayOfWeek.ToString("d"))).ToString("yyyy-MM-dd");
            var q = Queryable
                .LeftJoin<Db_crm_customer>((audit, customer) => audit.CustomerId == customer.Id)
                .InnerJoin<Db_crm_customer_subcompany>((audit, customer, subcompany) => audit.CustomerId == subcompany.CustomerId && subcompany.IsMain == (int)EnumCustomerCompanyMain.Main)
                .LeftJoin<Db_v_userwithorg>((audit, customer, subcompany, user) => user.Id == audit.ApplicantId)
                .LeftJoin<Db_sys_user>((audit, customer, subcompany, user, reviewer) => reviewer.Id == audit.ReviewerId)
                .Where((audit, customer, subcompany, user, reviewer) => audit.Deleted == (int)EnumCustomerDel.NotDel)
                .WhereIF(StringUtil.IsNotNullOrEmpty(getCustomerAuditRecord_IN.CustomerId), (audit, customer, subcompany, user, reviewer) => customer.Id == getCustomerAuditRecord_IN.CustomerId)
                .WhereIF(StringUtil.IsNotNullOrEmpty(getCustomerAuditRecord_IN.AuditId), (audit, customer, subcompany, user, reviewer) => audit.Id == getCustomerAuditRecord_IN.AuditId)
                .WhereIF(getCustomerAuditRecord_IN.ApplicantDateStart != null, (audit, customer, subcompany, user, reviewer) => audit.ApplicantDate != null && SqlFunc.ToDateShort(audit.ApplicantDate) >= SqlFunc.ToDateShort(getCustomerAuditRecord_IN.ApplicantDateStart))
                .WhereIF(getCustomerAuditRecord_IN.ApplicantDateEnd != null, (audit, customer, subcompany, user, reviewer) => audit.ApplicantDate != null && SqlFunc.ToDateShort(audit.ApplicantDate) <= SqlFunc.ToDateShort(getCustomerAuditRecord_IN.ApplicantDateEnd))
                .Select((audit, customer, subcompany, user, reviewer) => new GetCustomerAuditRecord_OUT()
                {
                    Id = audit.Id,
                    CustomerId = customer.Id,
                    CustomerNum = customer.CustomerNum,
                    CustomerName = subcompany.CompanyName,
                    State = (EnumCustomerAuditState)audit.State,
                    Type = (int)EnumCustomerAuditType.AddSub,
                    MergeCustomerName = SqlFunc.Subqueryable<Db_crm_addsubcompany_audit_subcompany>().Where(s => s.AddSubCompanyAuditId == audit.Id && s.State != (int)EnumCustomerAddSubCompanyAuditState.Split).SelectStringJoin(s => s.CompanyName, ","),
                    ApplicantName = user.Name,
                    ApplicantDate = audit.ApplicantDate,
                    ApplicantOrg = user.OrganizationId,
                    ApplicantOrgName = user.OrgFullName,
                    Feedback = audit.Feedback,
                    ReviewerDate = audit.ReviewerDate,
                    RelationshipDescription = audit.RelationshipDescription,
                    ReviewerId = audit.ReviewerId,
                    ReviewerName = SqlFunc.IsNullOrEmpty(audit.ReviewerId) ? "" : reviewer.Name,
                    ApplicantId = audit.ApplicantId,
                    ApplicantAvatarImage = SqlFunc.IsNullOrEmpty(audit.ApplicantId) ? "" : user.AvatarImage,
                    ReviewerAvatarImage = SqlFunc.IsNullOrEmpty(audit.ReviewerId) ? "" : reviewer.AvatarImage
                }, true).Mapper(it =>
                {
                    it.CustomerSourceName = it.CustomerSource.GetEnumDescription();
                    it.CustomerLevelName = it.CustomerLevel.GetEnumDescription();
                });
            return q;
        }
        public int GetAuditCount(SearchCustomerAudit searchCustomerAudit_IN)
        {
            DateTime dt = DateTime.Now;
            var dtDay = DateTime.Parse(dt.ToString("yyyy-MM-dd"));
            var weekEnd = dt.AddDays(1 - Convert.ToInt32(dt.DayOfWeek.ToString("d"))).AddDays(6).ToString("yyyy-MM-dd");
            var weekStart = dt.AddDays(1 - Convert.ToInt32(dt.DayOfWeek.ToString("d"))).ToString("yyyy-MM-dd");
            var exp = Expressionable.Create<Db_crm_addsubcompany_audit, Db_crm_customer_copy, Db_v_userwithorg, Db_sys_user>();
            foreach (var word in searchCustomerAudit_IN.SplitCustomerName)
            {
                exp.And((audit, customerCopy, user, reviewer) => customerCopy.CompanyName.Contains(word));
            }
            return Queryable
                .LeftJoin<Db_crm_customer_copy>((audit, customerCopy) => audit.Id == customerCopy.AuditId && audit.CustomerId == customerCopy.CustomerId && customerCopy.AuditType == (int)EnumCustomerCopyAuditType.AddSub)
                //.InnerJoin<Db_crm_customer_subcompany>((audit, customerCopy) => audit.CustomerId == subcompany.CustomerId && subcompany.IsMain == (int)EnumCustomerCompanyMain.Main)
                .LeftJoin<Db_v_userwithorg>((audit, customerCopy, user) => user.Id == audit.ApplicantId)
                .LeftJoin<Db_sys_user>((audit, customerCopy, user, reviewer) => reviewer.Id == audit.ReviewerId)
                .WhereIF(!StringUtil.IsNullOrEmpty(searchCustomerAudit_IN.CustomerId), (audit, customerCopy, user, reviewer) => searchCustomerAudit_IN.CustomerId == audit.CustomerId)
                //.WhereIF(StringUtil.IsNotNullOrEmpty(searchCustomerAudit_IN.CustomerName), (audit, customerCopy, user, reviewer) => customerCopy.CompanyName.Contains(searchCustomerAudit_IN.CustomerName))
                //.WhereIF(StringUtil.IsNotNullOrEmpty(searchCustomerAudit_IN.Contacts), (audit, customerCopy, user, reviewer) => subcompany.Contacts.Contains(searchCustomerAudit_IN.Contacts))
                .Where(exp.ToExpression())
                .WhereIF(StringUtil.IsNotNullOrEmpty(searchCustomerAudit_IN.CustomerNum), (audit, customerCopy, user, reviewer) => customerCopy.CustomerNum.Contains(searchCustomerAudit_IN.CustomerNum))
                .WhereIF(StringUtil.IsNotNullOrEmpty(searchCustomerAudit_IN.ApplicantName), (audit, customerCopy, user, reviewer) => user.Name.Contains(searchCustomerAudit_IN.ApplicantName))
                .Where((audit, customerCopy, user, reviewer) => audit.State == (int)EnumCustomerAuditState.InProcess)
                .WhereIF(!ArrayUtil.IsNullOrEmpty(searchCustomerAudit_IN.ApplicantId), (audit, customerCopy, user, reviewer) => SqlFunc.ContainsArray(searchCustomerAudit_IN.ApplicantId, audit.ApplicantId))
                .Where((audit, customerCopy, user, reviewer) => audit.Deleted == (int)EnumCustomerDel.NotDel)
                .WhereIF(searchCustomerAudit_IN.enumCustomerAuditQueryListType == EnumCustomerAuditQueryListType.Today, (audit, customerCopy, user, reviewer) => audit.CreateDate != null && SqlFunc.DateIsSame(audit.CreateDate.Value, dtDay))
                .WhereIF(searchCustomerAudit_IN.enumCustomerAuditQueryListType == EnumCustomerAuditQueryListType.Week, (audit, customerCopy, user, reviewer) => audit.CreateDate != null && SqlFunc.Between(audit.CreateDate.Value, weekStart, weekEnd))
                .WhereIF(searchCustomerAudit_IN.ApplicantDateStart != null, (audit, customerCopy, user, reviewer) => audit.ApplicantDate != null && SqlFunc.ToDateShort(audit.ApplicantDate) >= SqlFunc.ToDateShort(searchCustomerAudit_IN.ApplicantDateStart))
                .WhereIF(searchCustomerAudit_IN.ApplicantDateEnd != null, (audit, customerCopy, user, reviewer) => audit.ApplicantDate != null && SqlFunc.ToDateShort(audit.ApplicantDate) <= SqlFunc.ToDateShort(searchCustomerAudit_IN.ApplicantDateEnd))
                .Select((audit, customerCopy, user, reviewer) => audit)
                .Count();
        }

    }
}
