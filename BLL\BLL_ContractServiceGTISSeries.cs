using CRM2_API.BLL.Common;
using CRM2_API.BLL.GtisOpe;
using CRM2_API.Common.AppSetting;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.BusinessModel.BM_GtisOpe;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using iText.StyledXmlParser.Css.Resolve.Shorthand.Impl;
using LumiSoft.Net.Media;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SqlSugar;
using System.Collections.Generic;
using System.Diagnostics.Contracts;
using System.IO;
using System.Text.Json.Nodes;
using static CRM2_API.Model.BLLModel.Enum.CouponEnumOption;
using CRM2_API.Model.BLLModel.Enum;
using static CRM2_API.Model.BLLModel.Enum.MessageCenterEnumOption;
using static CRM2_API.Model.BLLModel.Enum.PrivateServiceEnumOption;
using static CRM2_API.Model.ControllersViewModel.VM_Contract;
using static CRM2_API.Model.ControllersViewModel.VM_ContractInvoice;
using static CRM2_API.Model.ControllersViewModel.VM_MessageCenter;
using CRM2_API.DAL.DbModel.Gtis;
using CRM2_API.BLL.ServiceOpening;
using CRM2_API.Model.BLLModel.ServiceOpening;
using CRM2_API.DAL.DbCommon;
using static CRM2_API.Model.ControllersViewModel.VM_ContractServiceChange;
using CRM2_API.Model.BLLModel.ServiceChange;

namespace CRM2_API.BLL
{
    public partial class BLL_ContractService : BaseBLL<BLL_ContractService>
    {

        /// <summary>
        /// 根据合同Id获取合同产品信息，支持服务申请和服务变更两种场景
        /// 原方法是BLL_Contract的 GetContractProductInfoByContractId 方法
        /// </summary>
        /// <param name="contractId">合同ID</param>
        /// <param name="isForServiceChange">是否用于服务变更场景，默认false（服务申请）</param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        public GetProductInfoByContractId4ServeApply_Out GetProductInfoByContractId4ServeApply(string contractId, bool isForServiceChange = false)
        {
            var result = new GetProductInfoByContractId4ServeApply_Out();
            var contractInfo = DbOpe_crm_contract.Instance.QueryByPrimaryKey(contractId);

            if (isForServiceChange)
            {
                // 服务变更场景：支持增项合同，获取已开通服务 + 增项合同中未申请产品
                result = GetProductInfoForServiceChange(contractId, contractInfo);
                //服务变更的被变更编码只返回当前编码
                result.WitsProduct.RenewableContractNumList = new List<string>() { contractInfo.ContractNum };
                return result;
            }
            else
            {
                // 服务申请场景：保持原有逻辑
                //检查是否为增项合同
                if (contractInfo.ContractType == EnumContractType.AddItem.ToInt())
                    throw new ApiException("当前合同为增项合同，无法进行服务申请");
                //获取当前合同包含的所有产品
                List<ProductInfo4ServeApply> productList = DbOpe_crm_contract_productinfo.Instance.GetProductInfoByContractId4ServeApply(contractId, true);

                return ProcessProductListForServiceApply(contractId, contractInfo, productList);
            }
        }

        /// <summary>
        /// 服务申请场景的产品处理逻辑（原有逻辑）
        /// </summary>
        private GetProductInfoByContractId4ServeApply_Out ProcessProductListForServiceApply(string contractId, Db_crm_contract contractInfo, List<ProductInfo4ServeApply> productList)
        {
            var result = new GetProductInfoByContractId4ServeApply_Out();
            var couldApplyWitsProduct = true;
            foreach (var item in productList)
            {
                //wits产品包含的产品类型列表
                var witsProductList = new List<EnumProductType>() { EnumProductType.Gtis, EnumProductType.Vip, EnumProductType.Global, EnumProductType.GlobalWitsSchool, EnumProductType.SalesWits, EnumProductType.AdditionalResource, EnumProductType.AddCredit };
                //如果当前数据的申请状态不为空 且 不是服务申请的拒绝数据，则不可以进入返回结果集中,continue到下一个item
                if (item.ProductServiceInfoApplState != null && !(item.ProductServiceInfoApplState == (int)EnumProcessStatus.Refuse && item.ProductServiceInfoApplProcessingType == (int)EnumProcessingType.Add))
                {
                    //如果存在不可申请的慧思产品，清空现有WitsProduct结果集（未申请的慧思产品走服务变更流程）
                    if (witsProductList.Contains(item.ProductType) && couldApplyWitsProduct)
                    {
                        couldApplyWitsProduct = false;
                        result.WitsProduct.ProductList = new List<ProductInfo4ServeApply>();
                    }
                    continue;
                }
                //邓白氏
                if (item.ProductType == EnumProductType.DandB)
                {
                    result.DBProduct.Add(item);
                }
                //其他数据
                else if (item.ProductType == EnumProductType.Other)
                {
                    result.OtherDataProduct.Add(item);
                }
                else if (couldApplyWitsProduct)
                {
                    //Gtis产品，需要计算子账号国家授权次数，返回在 SuperSubAccountNum（超级子账号个数） 值
                    if (item.ProductType == EnumProductType.Gtis)
                    {
                        result.WitsProduct.SuperSubAccountNum = item.SuperSubAccountNum.GetValueOrDefault(0);
                        //获取单独购买的超级子账号信息
                        List<ProductInfo_Out> SuperSubAccountList = DbOpe_crm_product_supersubaccount.Instance.GetProductSuperSubAccountByContractId(contractId, true);
                        //累加单独购买的超级子账号数
                        if (SuperSubAccountList.Count > 0)
                            result.WitsProduct.SuperSubAccountNum += SuperSubAccountList.First().SuperSubAccountNum.GetValueOrDefault(0);
                        //同时包含Vip零售国家的处理
                        if (productList.Any(r => r.ProductType == EnumProductType.Vip))
                        {
                            var vip = productList.Where(r => r.ProductType == EnumProductType.Vip).First();
                            item.Countries = vip.Countries;
                            item.IsGtisHasVip = true;
                            item.ProductName += "(定制零售国家)";
                        }
                        //数据填充
                        result.WitsProduct.ProductList.Add(item);
                        result.WitsProduct.GtisAccountPermissionList.Add(EnumGtisSeriesPermission.Gtis);
                    }
                    //如果当前产品类型是vip零售国家 && 当前合同不包含Gtis产品，需要填充数据
                    else if (item.ProductType == EnumProductType.Vip && !productList.Any(r => r.ProductType == EnumProductType.Gtis))
                    {
                        //数据填充
                        result.WitsProduct.ProductList.Add(item);
                        result.WitsProduct.GtisAccountPermissionList.Add(EnumGtisSeriesPermission.Vip);
                    }
                    //环球搜，只填充数据
                    else if (item.ProductType == EnumProductType.Global)
                    {
                        //数据填充
                        result.WitsProduct.ProductList.Add(item);
                        result.WitsProduct.GtisAccountPermissionList.Add(EnumGtisSeriesPermission.GlobalSearch);
                    }
                    //慧思学院，只填充数据
                    else if (item.ProductType == EnumProductType.GlobalWitsSchool)
                    {
                        //数据填充
                        result.WitsProduct.ProductList.Add(item);
                        result.WitsProduct.GtisAccountPermissionList.Add(EnumGtisSeriesPermission.College);
                    }
                    //SalesWits，根据是否有新增账户产品和充值产品，计算子账号数或充值金额
                    else if (item.ProductType == EnumProductType.SalesWits)
                    {
                        //SalesWits新增账户数
                        int addAccountNum = 0;
                        if (productList.Any(e => e.ProductType == EnumProductType.AdditionalResource))
                            addAccountNum = productList.Where(e => e.ProductType == EnumProductType.AdditionalResource).Select(e => e.SubAccountsNum).First().GetValueOrDefault(0);
                        //SalesWits充值金额
                        decimal addCredit = 0;
                        if (productList.Any(e => e.ProductType == EnumProductType.AddCredit))
                            addCredit = productList.Where(e => e.ProductType == EnumProductType.AddCredit).Select(e => e.ContractProductinfoPriceTotal).First();
                        item.SubAccountsNum += addAccountNum;
                        item.SalesWitsAddCredit = addCredit;
                        //数据填充
                        result.WitsProduct.ProductList.Add(item);
                        result.WitsProduct.GtisAccountPermissionList.Add(EnumGtisSeriesPermission.SalesWits);
                    }
                }
                //增加合同签约产品内容返回
                result.ProductList.Add(item.MappingTo<ContractSignProduct>());
            }

            //可使用的优惠信息
            if (result.WitsProduct != null && result.WitsProduct.ProductList.Count > 0 && result.WitsProduct.ProductList.Any(e => e.ProductType == EnumProductType.Gtis))
            {
                if (!contractInfo.IsMerged.Value)
                {
                    result.WitsProduct.CouponList = DbOpe_crm_customer_coupon.Instance.GetCouponDetailByContractId(contractId);
                    if (result.WitsProduct.CouponList != null && result.WitsProduct.CouponList.Count > 0)
                        result.WitsProduct.CouldUseCoupon = true;
                }
                result.WitsProduct.PrivateDaysInfo = DbOpe_crm_privateservice.Instance.GetDuration();
                if (result.WitsProduct.PrivateDaysInfo.Duration > 0 || result.WitsProduct.PrivateDaysInfo.ReleaseChargeDay > 0)
                    result.WitsProduct.CouldUsePrivateDays = true;
            }

            //慧思产品合同产品表Id
            if (result.WitsProduct != null && result.WitsProduct.ProductList.Count > 0)
                result.WitsProduct.ContractProductInfoSeriesId = result.WitsProduct.ProductList[0].ContractProductInfoSeriesId;

            //续约合同需要获取账号信息
            if (couldApplyWitsProduct && contractInfo.ContractType == EnumContractType.ReNew.ToInt())
            {
                //根据合同Id获取相同客户下的所有通过合同的客户编码
                var svCodeList = DbOpe_crm_contract.Instance.GetAllSameCustomerContractNumByContractId(contractId);
                //获取可续约的客户编码列表
                result.WitsProduct.RenewableContractNumList = BLL_GtisOpe.Instance.GetUserInfo(svCodeList).Result.Select(e => e.SvCode).ToList();
            }
            return result;
        }

        /// <summary>
        /// 服务变更场景的产品处理逻辑
        /// </summary>
        /// <param name="contractId">合同ID</param>
        /// <param name="contractInfo">合同信息</param>
        /// <returns></returns>
        private GetProductInfoByContractId4ServeApply_Out GetProductInfoForServiceChange(string contractId, Db_crm_contract contractInfo)
        {
            try
            {
                var result = new GetProductInfoByContractId4ServeApply_Out();

                // 1. 获取主合同ID和所有相关产品
                var mainContractId = BLL_ContractServiceChangeReason.Instance.GetMainContractId(contractId);
                var mainContractProducts = DbOpe_crm_contract_productinfo.Instance.GetProductInfoByContractId4ServeApply(mainContractId, true);
                var addItemProducts = GetAddItemProductsBySeriesId(mainContractId);

                // 2. 合并所有产品
                var allProducts = new List<ProductInfo4ServeApply>();
                allProducts.AddRange(mainContractProducts);
                allProducts.AddRange(addItemProducts);

                // 3. 检查GTIS系列产品是否有可以变更的（已开通且无审核中申请的可以变更）
                var witsProductTypes = new List<EnumProductType>() {
                    EnumProductType.Gtis, EnumProductType.Vip, EnumProductType.Global,
                    EnumProductType.GlobalWitsSchool, EnumProductType.SalesWits,
                    EnumProductType.AdditionalResource, EnumProductType.AddCredit
                };

                // 检查GTIS系列产品是否有已开通的
                var hasPassedWitsProduct = allProducts.Any(p =>
                    witsProductTypes.Contains(p.ProductType) &&
                    p.ProductServiceInfoApplState == (int)EnumProcessStatus.Pass); // 有通过的

                // 检查GTIS系列产品是否有审核中的申请
                var hasPendingWitsProduct = allProducts.Any(p =>
                    witsProductTypes.Contains(p.ProductType) &&
                    p.ProductServiceInfoApplState == (int)EnumProcessStatus.Submit); // 有审核中的

                // 只有有通过的且没有审核中的才能变更
                var hasChangeableWitsProduct = hasPassedWitsProduct && !hasPendingWitsProduct;

                // 4. 处理产品分类
                foreach (var item in allProducts)
                {
                    // 邓白氏产品
                    if (item.ProductType == EnumProductType.DandB)
                    {
                        // 修复：检查当前具体产品是否可以变更
                        // 条件：当前产品已开通 且 当前产品没有审核中的申请
                        if (IsProductChangeableForServiceChange(item, allProducts))
                        {
                            result.DBProduct.Add(item);
                        }
                    }
                    // 其他数据产品
                    else if (item.ProductType == EnumProductType.Other)
                    {
                        // 修复：检查当前具体产品是否可以变更
                        if (IsProductChangeableForServiceChange(item, allProducts))
                        {
                            result.OtherDataProduct.Add(item);
                        }
                    }
                    // GTIS系列产品
                    else if (witsProductTypes.Contains(item.ProductType) && hasChangeableWitsProduct)
                    {
                        ProcessWitsProductForServiceChange(result, item, allProducts);
                    }

                    // 添加到合同签约产品列表
                    result.ProductList.Add(item.MappingTo<ContractSignProduct>());
                }

                // 5. 处理GTIS系列的优惠券和个人服务天数
                if (hasChangeableWitsProduct && result.WitsProduct.ProductList.Count > 0)
                {
                    ProcessWitsProductExtras(result, contractInfo, contractId);
                }

                LogUtil.AddLog($"服务变更场景获取产品信息成功，合同ID: {contractId}, " +
                              $"主合同ID: {mainContractId}, GTIS系列可变更: {hasChangeableWitsProduct}");

                return result;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"服务变更场景获取产品信息异常，合同ID: {contractId}, 错误: {ex}");
                throw;
            }
        }

        /// <summary>
        /// 检查合同产品是否可以提交服务变更
        /// </summary>
        /// <param name="contractProductId">合同产品ID</param>
        /// <returns>可以提交返回true，否则返回false并抛出异常说明原因</returns>
        public bool CanSubmitServiceChange(string contractProductId)
        {
            try
            {
                // 1. 获取合同产品信息
                var contractProduct = DbOpe_crm_contract_productinfo.Instance.GetContractProductInfoById(contractProductId);
                if (contractProduct == null)
                {
                    throw new ApiException("合同产品信息不存在");
                }

                // 2. 获取合同信息
                var contract = DbOpe_crm_contract.Instance.GetContractBasicInfoById(contractProduct.ContractId, true);
                if (contract == null)
                {
                    throw new ApiException("合同信息不存在");
                }

                // 3. 检查用户数据权限 - 简化处理，由调用方确保权限
                // 这里可以根据实际需要添加权限检查逻辑

                // 4. 检查合同状态
                if (contract.ContractStatus != EnumContractStatus.Pass.ToInt())
                {
                    throw new ApiException("当前合同状态下，不可以进行服务变更申请");
                }

                // 5. 获取所有产品信息用于判断
                var allProducts = DbOpe_crm_contract_productinfo.Instance.GetProductInfoByContractId4ServeApply(contractProduct.ContractId, true);
                if (allProducts == null || allProducts.Count == 0)
                {
                    throw new ApiException("合同产品信息不存在");
                }

                // 修复：检查具体的合同产品是否可以变更（不是按类型统一判断）
                var productType = contractProduct.ProductType.ToEnum<EnumProductType>();
                var witsProductTypes = new List<EnumProductType>() {
                    EnumProductType.Gtis, EnumProductType.Vip, EnumProductType.Global,
                    EnumProductType.GlobalWitsSchool, EnumProductType.SalesWits,
                    EnumProductType.AdditionalResource, EnumProductType.AddCredit
                };

                bool canChange = false;
                if (witsProductTypes.Contains(productType))
                {
                    // GTIS系列产品：检查当前具体产品是否已开通且没有审核中申请
                    var hasPassedWits = allProducts.Any(p =>
                        p.ContractProductInfoId == contractProductId && // 具体产品ID
                        p.ProductServiceInfoApplState == (int)EnumProcessStatus.Pass);
                    var hasPendingWits = allProducts.Any(p =>
                        p.ContractProductInfoId == contractProductId && // 具体产品ID
                        p.ProductServiceInfoApplState == (int)EnumProcessStatus.Submit);
                    canChange = hasPassedWits && !hasPendingWits;
                }
                else if (productType == EnumProductType.DandB)
                {
                    // 邓白氏产品：检查当前具体产品
                    var hasPassedDB = allProducts.Any(p =>
                        p.ContractProductInfoId == contractProductId && // 具体产品ID
                        p.ProductServiceInfoApplState == (int)EnumProcessStatus.Pass);
                    var hasPendingDB = allProducts.Any(p =>
                        p.ContractProductInfoId == contractProductId && // 具体产品ID
                        p.ProductServiceInfoApplState == (int)EnumProcessStatus.Submit);
                    canChange = hasPassedDB && !hasPendingDB;
                }
                else if (productType == EnumProductType.Other)
                {
                    // 其他数据产品：检查当前具体产品
                    var hasPassedOther = allProducts.Any(p =>
                        p.ContractProductInfoId == contractProductId && // 具体产品ID
                        p.ProductServiceInfoApplState == (int)EnumProcessStatus.Pass);
                    var hasPendingOther = allProducts.Any(p =>
                        p.ContractProductInfoId == contractProductId && // 具体产品ID
                        p.ProductServiceInfoApplState == (int)EnumProcessStatus.Submit);
                    canChange = hasPassedOther && !hasPendingOther;
                }

                if (!canChange)
                {
                    throw new ApiException("当前产品状态不允许服务变更：需要有已开通的服务且没有审核中的申请");
                }
                return true;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"检查合同产品服务变更权限异常，合同产品ID: {contractProductId}, 错误: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 检查具体产品是否可以进行服务变更
        /// </summary>
        /// <param name="currentProduct">当前产品</param>
        /// <param name="allProducts">所有产品列表</param>
        /// <returns>是否可以变更</returns>
        private bool IsProductChangeableForServiceChange(ProductInfo4ServeApply currentProduct, List<ProductInfo4ServeApply> allProducts)
        {
            try
            {
                // 检查当前产品是否已开通（有通过的申请）
                var hasPassedApplication = allProducts.Any(p => 
                    p.ContractProductInfoId == currentProduct.ContractProductInfoId && // 同一个合同产品
                    p.ProductServiceInfoApplState == (int)EnumProcessStatus.Pass); // 已通过

                // 检查当前产品是否有审核中的申请
                var hasPendingApplication = allProducts.Any(p => 
                    p.ContractProductInfoId == currentProduct.ContractProductInfoId && // 同一个合同产品
                    p.ProductServiceInfoApplState == (int)EnumProcessStatus.Submit); // 审核中

                // 只有已开通且没有审核中申请的产品才能变更
                var canChange = hasPassedApplication && !hasPendingApplication;

                LogUtil.AddLog($"产品变更权限检查 - 产品ID: {currentProduct.ContractProductInfoId}, " +
                              $"产品类型: {currentProduct.ProductType}, " +
                              $"已开通: {hasPassedApplication}, " +
                              $"审核中: {hasPendingApplication}, " +
                              $"可变更: {canChange}");

                return canChange;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"检查产品变更权限异常，产品ID: {currentProduct.ContractProductInfoId}, 错误: {ex.Message}");
                return false; // 异常情况下不允许变更
            }
        }

        /// <summary>
        /// 处理GTIS系列产品的服务变更逻辑
        /// </summary>
        /// <param name="result">返回结果</param>
        /// <param name="item">当前产品</param>
        /// <param name="allProducts">所有产品列表</param>
        private void ProcessWitsProductForServiceChange(GetProductInfoByContractId4ServeApply_Out result, ProductInfo4ServeApply item, List<ProductInfo4ServeApply> allProducts)
        {
            // 注意：进入此方法时已经确认GTIS系列产品组合可以变更（有通过的且没有审核中的）

            // GTIS产品处理
            if (item.ProductType == EnumProductType.Gtis)
            {
                result.WitsProduct.SuperSubAccountNum = item.SuperSubAccountNum.GetValueOrDefault(0);
                // 计算超级子账号数
                var SuperSubAccountList = DbOpe_crm_product_supersubaccount.Instance.GetProductSuperSubAccountByContractId(item.ContractId, true);
                if (SuperSubAccountList.Count > 0)
                    result.WitsProduct.SuperSubAccountNum += SuperSubAccountList.First().SuperSubAccountNum.GetValueOrDefault(0);

                // 处理VIP零售国家
                if (allProducts.Any(r => r.ProductType == EnumProductType.Vip))
                {
                    var vip = allProducts.Where(r => r.ProductType == EnumProductType.Vip).First();
                    item.Countries = vip.Countries;
                    item.IsGtisHasVip = true;
                    item.ProductName += "(定制零售国家)";
                }

                result.WitsProduct.ProductList.Add(item);
                result.WitsProduct.GtisAccountPermissionList.Add(EnumGtisSeriesPermission.Gtis);
            }
            // VIP产品处理
            else if (item.ProductType == EnumProductType.Vip && !allProducts.Any(r => r.ProductType == EnumProductType.Gtis))
            {
                result.WitsProduct.ProductList.Add(item);
                result.WitsProduct.GtisAccountPermissionList.Add(EnumGtisSeriesPermission.Vip);
            }
            // 环球搜产品处理
            else if (item.ProductType == EnumProductType.Global)
            {
                result.WitsProduct.ProductList.Add(item);
                result.WitsProduct.GtisAccountPermissionList.Add(EnumGtisSeriesPermission.GlobalSearch);
            }
            // 慧思学院产品处理
            else if (item.ProductType == EnumProductType.GlobalWitsSchool)
            {
                result.WitsProduct.ProductList.Add(item);
                result.WitsProduct.GtisAccountPermissionList.Add(EnumGtisSeriesPermission.College);
            }
            // SalesWits产品处理
            else if (item.ProductType == EnumProductType.SalesWits)
            {
                // 计算新增账户数和充值金额
                int addAccountNum = 0;
                if (allProducts.Any(e => e.ProductType == EnumProductType.AdditionalResource))
                    addAccountNum = allProducts.Where(e => e.ProductType == EnumProductType.AdditionalResource).Select(e => e.SubAccountsNum).First().GetValueOrDefault(0);

                decimal addCredit = 0;
                if (allProducts.Any(e => e.ProductType == EnumProductType.AddCredit))
                    addCredit = allProducts.Where(e => e.ProductType == EnumProductType.AddCredit).Select(e => e.ContractProductinfoPriceTotal).First();

                item.SubAccountsNum += addAccountNum;
                item.SalesWitsAddCredit = addCredit;

                result.WitsProduct.ProductList.Add(item);
                result.WitsProduct.GtisAccountPermissionList.Add(EnumGtisSeriesPermission.SalesWits);
            }
        }

        /// <summary>
        /// 处理GTIS系列产品的额外信息（优惠券、个人服务天数等）
        /// </summary>
        /// <param name="result">返回结果</param>
        /// <param name="contractInfo">合同信息</param>
        /// <param name="contractId">合同ID</param>
        private void ProcessWitsProductExtras(GetProductInfoByContractId4ServeApply_Out result, Db_crm_contract contractInfo, string contractId)
        {
            // 优惠券信息
            if (result.WitsProduct.ProductList.Any(e => e.ProductType == EnumProductType.Gtis))
            {
                if (!contractInfo.IsMerged.GetValueOrDefault(false))
                {
                    result.WitsProduct.CouponList = DbOpe_crm_customer_coupon.Instance.GetCouponDetailByContractId(contractId);
                    if (result.WitsProduct.CouponList != null && result.WitsProduct.CouponList.Count > 0)
                        result.WitsProduct.CouldUseCoupon = true;
                }

                // 个人服务天数信息
                result.WitsProduct.PrivateDaysInfo = DbOpe_crm_privateservice.Instance.GetDuration();
                if (result.WitsProduct.PrivateDaysInfo.Duration > 0 || result.WitsProduct.PrivateDaysInfo.ReleaseChargeDay > 0)
                    result.WitsProduct.CouldUsePrivateDays = true;
            }

            // 设置SeriesId
            if (result.WitsProduct.ProductList.Count > 0)
                result.WitsProduct.ContractProductInfoSeriesId = result.WitsProduct.ProductList[0].ContractProductInfoSeriesId;
        }

        /// <summary>
        /// 通过SeriesId获取增项合同中未申请的产品
        /// </summary>
        /// <param name="mainContractId">主合同ID</param>
        /// <returns>增项合同中未申请的产品列表</returns>
        private List<ProductInfo4ServeApply> GetAddItemProductsBySeriesId(string mainContractId)
        {
            try
            {
                // 1. 获取主合同的所有产品SeriesId
                var mainContractSeriesIds = DbOpe_crm_contract_productinfo.Instance
                    .GetDataList(cp => cp.ContractId == mainContractId && cp.Deleted != true)
                    .Select(cp => cp.SeriesId)
                    .Where(sid => !string.IsNullOrEmpty(sid))
                    .Distinct()
                    .ToList();

                if (!mainContractSeriesIds.Any()) return new List<ProductInfo4ServeApply>();

                var addItemProducts = new List<ProductInfo4ServeApply>();

                // 2. 通过SeriesId查找所有相关的增项合同产品
                foreach (var seriesId in mainContractSeriesIds)
                {
                    // 查找该SeriesId对应的所有已通过的增项合同
                    var relatedAddItemContracts = DbContext.Crm2Db.Queryable<Db_crm_contract>()
                        .LeftJoin<Db_crm_contract_productinfo>((c, cp) => c.Id == cp.ContractId)
                        .Where((c, cp) => cp.SeriesId == seriesId &&
                                         c.ContractType == (int)EnumContractType.AddItem &&
                                         c.Deleted != true && cp.Deleted != true &&
                                         (c.ContractStatus == (int)EnumContractStatus.Pass ||
                                          c.ContractStatus == (int)EnumContractStatus.AutoPass))
                        .Select((c, cp) => c.Id)
                        .Distinct()
                        .ToList();

                    // 获取这些增项合同中未申请的产品
                    foreach (var addItemContractId in relatedAddItemContracts)
                    {
                        var products = DbOpe_crm_contract_productinfo.Instance
                            .GetProductInfoByContractId4ServeApply(addItemContractId, true)
                            .Where(p => p.ProductServiceInfoApplState == null ||
                                       p.ProductServiceInfoApplState == 0 ||
                                       p.ProductServiceInfoApplState == (int)EnumProcessStatus.Refuse ||
                                       p.ProductServiceInfoApplState == (int)EnumProcessStatus.Void)
                            .ToList();

                        addItemProducts.AddRange(products);
                    }
                }

                return addItemProducts;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取增项合同产品异常，主合同ID: {mainContractId}, 错误: {ex}");
                return new List<ProductInfo4ServeApply>();
            }
        }

        /// <summary>
        /// 续约申请时获取账号信息并同步数据
        /// </summary>
        /// <param name="contractNum"></param>
        /// <returns></returns>
        public OriginUserGroupInfo GetAndSynchroUserInfo(string contractNum)
        {
            var retValue = new OriginUserGroupInfo();
            DbOpe_crm_contract.Instance.TransDeal(() =>
            {
                //合同信息
                var contractInfo = DbOpe_crm_contract.Instance.GetContractByContractNum(contractNum);
                //可获取 开通、锁定、没有使用者、账户管理员停用、过期 状态的账号
                List<int> validStateList = new List<int>() { EnumGtisUserOpeningStatus.Ok.ToInt(), EnumGtisUserOpeningStatus.Lock.ToInt(), EnumGtisUserOpeningStatus.NoUser.ToInt(), EnumGtisUserOpeningStatus.UserManagerStop.ToInt(), EnumGtisUserOpeningStatus.Expired.ToInt() };
                //获取同步的账号信息列表
                var gtisOpeUserList = BLL_GtisOpe.Instance.GetUserInfo(new string[] { contractNum }).Result;
                //获取主账号信息
                var primaryUser = gtisOpeUserList.Where(e => e.AccountType == 0 && e.State != BM_Enum_GtisUserSta.BackManageStop).First();
                //查找当前客户编码对应的慧思服务信息，这里取正常、过期、失效 3种状态的服务中复核时间最近的一条
                var hisWitsServe = DbOpe_crm_contract_serviceinfo_wits.Instance.GetOnceOpendServiceByContractNum(contractNum);
                if (hisWitsServe == null)
                {
                    hisWitsServe = new Db_crm_contract_serviceinfo_wits();
                    hisWitsServe.Id = Guid.NewGuid().ToString();
                    hisWitsServe.ContractId = contractInfo.Id;
                    hisWitsServe.ContractNum = contractNum;
                    hisWitsServe.ShareUsageNum = gtisOpeUserList.Where(e => e.State != BM_Enum_GtisUserSta.BackManageStop).Sum(e => e.SharingTimesUse);
                    hisWitsServe.ServiceCycleStart = primaryUser.StartServerDate;
                    hisWitsServe.ServiceCycleEnd = primaryUser.EndServerDate;
                    hisWitsServe.State = DateTime.Now > primaryUser.EndServerDate ? EnumContractServiceState.VALID : EnumContractServiceState.OUT;
                    hisWitsServe.HasGtisApp = gtisOpeUserList.Any(e => e.apps.Contains("Gtis6"));
                    hisWitsServe.HasGlobalSearchApp = gtisOpeUserList.Any(e => e.apps.Contains("HQS"));
                    hisWitsServe.HasCollegeApp = gtisOpeUserList.Any(e => e.apps.Contains("College"));
                    hisWitsServe.HasSalesWitsApp = gtisOpeUserList.Any(e => e.apps.Contains("CRM"));
                    DbOpe_crm_contract_serviceinfo_wits.Instance.Insert(hisWitsServe);
                }
                //如果返回的账号中包含有Gtis权限的账号，需要查看Gtis服务的详情
                if (hisWitsServe.HasGtisApp == true)
                {
                    //获取有Gtis权限的账号列表
                    var userList = gtisOpeUserList.Where(e => e.apps.Contains("Gtis6")).ToList();
                    //获取在服信息
                    Db_crm_contract_serviceinfo_gtis? hisGtisServe = null;
                    //先根据wits服务中的CurrentGtisServiceId查找gtis服务数据
                    if (!string.IsNullOrEmpty(hisWitsServe.CurrentGtisServiceId))
                        hisGtisServe = DbOpe_crm_contract_serviceinfo_gtis.Instance.QueryByPrimaryKey(hisWitsServe.CurrentGtisServiceId);
                    //再根据客户编码查找服务信息
                    if (hisGtisServe == null)
                        hisGtisServe = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetOnceOpendGtisServiceByContractNum(contractNum);
                    //如果没找到服务数据，根据同步回的账号信息，补充服务数据
                    if (hisGtisServe == null)
                    {
                        AppOptions priUserOpt = new AppOptions();
                        primaryUser.appsOptions.TryGetValue("Gtis6", out priUserOpt);
                        //补充服务表数据
                        hisGtisServe = new Db_crm_contract_serviceinfo_gtis()
                        {
                            Id = Guid.NewGuid().ToString(),
                            WitsApplId = hisWitsServe.WitsApplId,
                            ContractNum = contractNum,
                            PrimaryAccountsNum = userList.Count(e => e.AccountType == 0 && e.State != BM_Enum_GtisUserSta.BackManageStop),
                            SubAccountsNum = userList.Count(e => e.AccountType != 0 && e.State != BM_Enum_GtisUserSta.BackManageStop),
                            ShareUsageNum = userList.Where(e => e.State != BM_Enum_GtisUserSta.BackManageStop).Sum(e => e.SharingTimesUse),
                            ServiceCycleStart = priUserOpt.StartTime,
                            ServiceCycleEnd = priUserOpt.EndTime,
                            IsProcessed = (int)EnumGtisServiceIsProcess.Processed,
                            ProcessedTime = DateTime.Now,
                            ForbidSearchExport = primaryUser.ForbidSearchExport,
                            WordRptMaxTimes = primaryUser.WordRptMaxTimes,
                            WordRptPermissions = primaryUser.WordRptPermissions,
                            IsGtisOldCustomer = primaryUser.IsHistory.Value == 1
                        };
                        DbOpe_crm_contract_serviceinfo_gtis.Instance.Insert(hisGtisServe);
                    }
                    //补充wits_service中gtis部分的数据
                    hisWitsServe.CurrentGtisServiceId = hisGtisServe.Id;
                    hisWitsServe.GtisAuthorizationNum = hisGtisServe.AuthorizationNum;
                    hisWitsServe.GtisPrimaryAccountsNum = hisGtisServe.PrimaryAccountsNum;
                    hisWitsServe.GtisSubAccountsNum = hisGtisServe.SubAccountsNum;
                    hisWitsServe.GtisServiceCycleStart = hisGtisServe.ServiceCycleStart;
                    hisWitsServe.GtisServiceCycleEnd = hisGtisServe.ServiceCycleEnd;
                    //返回的在服信息
                    retValue.GtisOnServiceInfo = hisGtisServe.MappingTo<GtisServiceInfo>();
                    retValue.GtisOnServiceInfo.ServiceId = hisGtisServe.Id;
                    retValue.GtisOnServiceInfo.GtisApplCountry = DbOpe_crm_contract_serviceinfo_gtis_country.Instance.GetGtisCountryListByServeId(hisGtisServe.Id).ToList().MappingTo<List<GtisApplCountry>>();

                    #region  同步gtis_user的账号信息
                    var hisGtisUserList = DbOpe_crm_contract_serviceinfo_gtis_user.Instance.GetGtisUsersByServeId(hisGtisServe.Id);
                    var hisGtisUserIds = hisGtisUserList.Select(e => e.UserId).ToList();
                    var sysGtisUserIDs = userList.Select(e => e.SysUserID).ToList();
                    //交集--需要刷新的用户信息
                    var intersectGtisUserIds = hisGtisUserIds.Intersect(sysGtisUserIDs).ToList();
                    intersectGtisUserIds.ForEach(sysUserId =>
                    {
                        //g5返回的用户信息
                        var gtisOpeUser = gtisOpeUserList.Find(e => e.SysUserID == sysUserId);
                        //当前crm保存的用户信息
                        var hisGtisUser = hisGtisUserList.Find(e => e.UserId == sysUserId);
                        //修改crmGtisUser的属性
                        hisGtisUser.OpeningStatus = (int)DbOpe_crm_contract_productserviceinfo_gtis_appl_user.Instance.TransGtisUserStates(gtisOpeUser.State);
                        hisGtisUser.StartDate = gtisOpeUser.StartServerDate;
                        hisGtisUser.EndDate = gtisOpeUser.EndServerDate;
                        hisGtisUser.LoginIP = gtisOpeUser.LastLoginPlace;
                        hisGtisUser.LastLoginTime = gtisOpeUser.LastLoginDate;
                        hisGtisUser.SharePeopleNum = gtisOpeUser.SharingTimesUse;
                        hisGtisUser.PhoneUser = (gtisOpeUser.AllPhoneUserInfo.Count > 0) ? gtisOpeUser.AllPhoneUserInfo.First().LinkMan : "";
                        hisGtisUser.PhoneUserNum = gtisOpeUser.AllPhoneUserInfo.Count;
                        hisGtisUser.AuthorizationNum = gtisOpeUser.AuthorizationNum;
                        hisGtisUser.GlobalSearchCode = gtisOpeUser.GlobalSearchCode;
                        DbOpe_crm_contract_serviceinfo_gtis_user.Instance.UpdateDataQueue(hisGtisUser);
                    });
                    //sysUserIDs 对 crmGtisUserIds 的差集--需要添加的用户信息
                    var tobeAddGtisUserIds = sysGtisUserIDs.Except(hisGtisUserIds).ToList();
                    tobeAddGtisUserIds.ForEach(sysUserId =>
                    {
                        //g5返回的用户信息
                        var gtisOpeUser = gtisOpeUserList.Find(e => e.SysUserID == sysUserId);
                        //当前crm保存的用户信息
                        var crmGtisUser = new Db_crm_contract_serviceinfo_gtis_user();
                        //填充crmGtisUser的属性
                        crmGtisUser.ContractServiceInfoGtisId = hisGtisServe.Id;
                        crmGtisUser.UserId = gtisOpeUser.SysUserID;
                        crmGtisUser.AccountNumber = gtisOpeUser.AccountNumber;
                        crmGtisUser.PassWord = gtisOpeUser.PassWord;
                        crmGtisUser.AccountType = (int)(gtisOpeUser.AccountType == 0 ? EnumGtisAccountType.Main : EnumGtisAccountType.Sub);
                        crmGtisUser.IsProcessed = 0;
                        crmGtisUser.OpeningStatus = (int)DbOpe_crm_contract_productserviceinfo_gtis_appl_user.Instance.TransGtisUserStates(gtisOpeUser.State);
                        crmGtisUser.StartDate = gtisOpeUser.StartServerDate;
                        crmGtisUser.EndDate = gtisOpeUser.EndServerDate;
                        crmGtisUser.LoginIP = gtisOpeUser.LastLoginPlace;
                        crmGtisUser.LastLoginTime = gtisOpeUser.LastLoginDate;
                        crmGtisUser.SharePeopleNum = gtisOpeUser.SharingTimesUse;
                        crmGtisUser.PhoneUser = (gtisOpeUser.AllPhoneUserInfo.Count > 0) ? gtisOpeUser.AllPhoneUserInfo.First().LinkMan : "";
                        crmGtisUser.PhoneUserNum = gtisOpeUser.AllPhoneUserInfo.Count;
                        crmGtisUser.AuthorizationNum = gtisOpeUser.AuthorizationNum;
                        crmGtisUser.GlobalSearchCode = gtisOpeUser.GlobalSearchCode;
                        DbOpe_crm_contract_serviceinfo_gtis_user.Instance.InsertDataQueue(crmGtisUser);
                    });
                    //crmGtisUserIds 对 sysUserIDs 的差集--需要删除的用户信息
                    var tobeDelGtisUserIds = hisGtisUserIds.Except(sysGtisUserIDs).ToList();
                    var tobeDelGtisUserList = hisGtisUserList.Where(e => tobeDelGtisUserIds.Contains(e.UserId)).ToList();
                    tobeDelGtisUserList.ForEach(user =>
                    {
                        user.OpeningStatus = (int)EnumGtisUserOpeningStatus.NotFound;
                        DbOpe_crm_contract_serviceinfo_gtis_user.Instance.UpdateDataQueue(user);
                    });
                    #endregion
                }
                //如果返回的账号中包含有环球搜权限的账号，需要查看环球搜服务的详情
                if (hisWitsServe.HasGlobalSearchApp == true)
                {
                    //获取有环球搜权限的账号列表
                    var userList = gtisOpeUserList.Where(e => e.apps.Contains("HQS")).ToList();
                    //获取同步到的主账号,为了获取Options和GlobalsearchList
                    AppOptions priUserOpt = new AppOptions();
                    primaryUser.appsOptions.TryGetValue("HQS", out priUserOpt);
                    var codeList = primaryUser.GlobalSearchList;
                    //如果账户中存在codeList之外的环球搜码，抛出错误
                    if (userList.Any(e => e.State != BM_Enum_GtisUserSta.BackManageStop && !string.IsNullOrEmpty(e.GlobalSearchCode) && !codeList.Contains(e.GlobalSearchCode)))
                        throw new ApiException("当前客户编码存在多套环球搜码，请联系后台人员核对修正数据后再申请");

                    //获取在服信息
                    Db_crm_contract_serviceinfo_globalsearch? hisGlobalsearchServe = null;
                    //先根据wits服务中的CurrentGlobalSearchServiceId查找环球搜服务数据
                    if (!string.IsNullOrEmpty(hisWitsServe.CurrentGlobalSearchServiceId))
                        hisGlobalsearchServe = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.QueryByPrimaryKey(hisWitsServe.CurrentGlobalSearchServiceId);
                    //再根据客户编码查找服务信息
                    if (hisGlobalsearchServe == null)
                        hisGlobalsearchServe = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetOnceOpendServiceByContractNum(contractNum);
                    //如果没找到服务数据，根据同步回的账号信息，补充服务数据
                    if (hisGlobalsearchServe == null)
                    {
                        //补充服务表数据
                        hisGlobalsearchServe = new Db_crm_contract_serviceinfo_globalsearch()
                        {
                            Id = Guid.NewGuid().ToString(),
                            WitsApplId = hisWitsServe.WitsApplId,
                            ContractNum = contractNum,
                            ServiceCycleStart = priUserOpt.StartTime,
                            ServiceCycleEnd = priUserOpt.EndTime,
                            ExecuteServiceCycleStart = priUserOpt.StartTime,
                            ExecuteServiceCycleEnd = priUserOpt.StartTime,
                            IsProcessed = (int)EnumGtisServiceIsProcess.Processed,
                            ProcessedTime = DateTime.Now,
                        };
                        DbOpe_crm_contract_serviceinfo_globalsearch.Instance.Insert(hisGlobalsearchServe);
                    }

                    //添加的环球搜码列表
                    var globalsearchUserList = DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.GetGlobalSearchUserByServiceId(hisGlobalsearchServe.Id);
                    var url = string.Format(AppSettings.GlobalSearchAPI.CheckUserStatus, string.Join(';', codeList));
                    try
                    {
                        var ret = NetUtil.Post_ReturnString(url).DeserializeNewtonJson<JObject>();
                        if ("200".Equals(ret["status"].ToString()))
                        {
                            var results = ret["results"].ToList();
                            int primaryAccountsNum = 0;
                            int subAccountsNum = 0;
                            results.ForEach(result =>
                            {
                                var code = result["idCst"].ToString();
                                EnumContractServiceGlobalSearchUserState openingStatus = new EnumContractServiceGlobalSearchUserState();
                                if ("active".Equals(result["status"].ToString()))
                                    //返回正常，账号状态标记正常
                                    openingStatus = EnumContractServiceGlobalSearchUserState.Normal;
                                else if ("disabled".Equals(result["status"].ToString()))
                                    //返回过期，账号状态标记停用
                                    openingStatus = EnumContractServiceGlobalSearchUserState.Stop;
                                else if ("noexisted".Equals(result["status"].ToString()))
                                    //返回不存在，账号状态标记异常
                                    openingStatus = EnumContractServiceGlobalSearchUserState.AbNormal;
                                var startDate = result["begindate"].ToString();
                                var endDate = result["enddate"].ToString();
                                var accountType = result["acctType"].ToString() == "主账户" ? EnumContractServiceGlobalSearchAccountType.PrimaryAccount : EnumContractServiceGlobalSearchAccountType.SubAccount;
                                //计算主子账号数
                                if (accountType == EnumContractServiceGlobalSearchAccountType.PrimaryAccount)
                                    primaryAccountsNum += 1;
                                else
                                    subAccountsNum += 1;
                                //插入或更新globalsearch_user表数据
                                var globalsearch_user = globalsearchUserList.Where(e => e.AccountNumber == code).First();
                                if (globalsearch_user != null)
                                {
                                    globalsearch_user.AccountNumber = code;
                                    globalsearch_user.AccountType = accountType;
                                    globalsearch_user.OpeningStatus = openingStatus;
                                    globalsearch_user.StartDate = DateTime.Parse(startDate);
                                    globalsearch_user.EndDate = DateTime.Parse(endDate);
                                    DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.UpdateData(globalsearch_user);
                                }
                                else
                                {
                                    globalsearch_user = new Db_crm_contract_serviceinfo_globalsearch_user();
                                    globalsearch_user.ContractServiceInfoGlobalSearchId = hisGlobalsearchServe.Id;
                                    globalsearch_user.AccountNumber = code;
                                    globalsearch_user.AccountType = accountType;
                                    globalsearch_user.OpeningStatus = openingStatus;
                                    globalsearch_user.StartDate = DateTime.Parse(startDate);
                                    globalsearch_user.EndDate = DateTime.Parse(endDate);
                                    DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.InsertData(globalsearch_user);
                                }
                            });
                            if (primaryAccountsNum == 0)
                                throw new ApiException("当前客户编码没有找到对应的环球搜主账号码，请联系后台人员核对修正数据后再申请");
                            else if (primaryAccountsNum > 1)
                                throw new ApiException("当前客户编码存在多套环球搜码，请联系后台人员核对修正数据后再申请");

                            //根据同步信息得到环球搜主子账号数量，更新服务表数据
                            hisGlobalsearchServe.PrimaryAccountsNum = primaryAccountsNum;
                            hisGlobalsearchServe.SubAccountsNum = subAccountsNum;
                            DbOpe_crm_contract_serviceinfo_globalsearch.Instance.UpdateData(hisGlobalsearchServe);
                        }
                        else
                        {
                            throw new ApiException("环球搜数据同步异常");
                        }
                    }
                    catch (Exception ex)
                    {
                        throw new ApiException(ex.Message);
                    }
                    //删除没出现在同步codelist中的环球搜码数据
                    globalsearchUserList.Where(e => !codeList.Contains(e.AccountNumber)).ToList().ForEach(globalsearch_user =>
                    {
                        globalsearch_user.Deleted = true;
                        DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.UpdateData(globalsearch_user);
                    });
                    //补充wits_service中环球搜部分的数据
                    hisWitsServe.CurrentGlobalSearchServiceId = hisGlobalsearchServe.Id;
                    hisWitsServe.GlobalSearchAccountsNum = hisGlobalsearchServe.PrimaryAccountsNum + hisGlobalsearchServe.SubAccountsNum;
                    hisWitsServe.GlobalSearchServiceCycleStart = hisGlobalsearchServe.ServiceCycleStart;
                    hisWitsServe.GlobalSearchServiceCycleEnd = hisGlobalsearchServe.ServiceCycleEnd;
                    //返回的在服信息
                    retValue.GlobalSearchOnServiceInfo = hisGlobalsearchServe.MappingTo<GlobalSearchServiceInfo>();
                    retValue.GlobalSearchOnServiceInfo.ServiceId = hisGlobalsearchServe.Id;
                    retValue.GlobalSearchOnServiceInfo.ServiceCycleStart = hisGlobalsearchServe.ExecuteServiceCycleStart;
                    retValue.GlobalSearchOnServiceInfo.ServiceCycleEnd = hisGlobalsearchServe.ExecuteServiceCycleEnd;
                    retValue.GlobalSearchOnServiceInfo.ServiceMonth = hisGlobalsearchServe.ExecuteServiceMonth;
                    retValue.GlobalSearchOnServiceInfo.AccountCount = hisGlobalsearchServe.PrimaryAccountsNum + hisGlobalsearchServe.SubAccountsNum;
                    retValue.GlobalSearchOnServiceInfo.PaymentPeriod = hisGlobalsearchServe.SettlementCount;
                    retValue.GlobalSearchOnServiceInfo.AccountLevel = hisGlobalsearchServe.SettlementLevel;
                }
                //如果返回的账号中包含有慧思学院权限的账号，需要查看慧思学院服务的详情
                if (hisWitsServe.HasCollegeApp == true)
                {
                    //获取在服信息
                    Db_crm_contract_serviceinfo_college? hisCollegeServe = null;
                    //先根据wits服务中的CurrentCollegeServiceId查找服务数据
                    if (!string.IsNullOrEmpty(hisWitsServe.CurrentCollegeServiceId))
                        hisCollegeServe = DbOpe_crm_contract_serviceinfo_college.Instance.QueryByPrimaryKey(hisWitsServe.CurrentCollegeServiceId);
                    //再根据客户编码查找服务信息
                    if (hisCollegeServe == null)
                        hisCollegeServe = DbOpe_crm_contract_serviceinfo_college.Instance.GetOnceOpendServiceByContractNum(contractNum);
                    //如果没找到服务数据，根据同步回的账号信息，补充服务数据
                    if (hisCollegeServe == null)
                    {
                        AppOptions priUserOpt = new AppOptions();
                        primaryUser.appsOptions.TryGetValue("College", out priUserOpt);
                        //补充服务表数据
                        hisCollegeServe = new Db_crm_contract_serviceinfo_college()
                        {
                            Id = Guid.NewGuid().ToString(),
                            WitsApplId = hisWitsServe.WitsApplId,
                            ContractNum = contractNum,
                            PrimaryAccountsNum = priUserOpt.MaxAccount,
                            ServiceCycleStart = priUserOpt.StartTime,
                            ServiceCycleEnd = priUserOpt.EndTime,
                            IsProcessed = (int)EnumGtisServiceIsProcess.Processed,
                            ProcessedTime = DateTime.Now,
                        };
                        DbOpe_crm_contract_serviceinfo_college.Instance.Insert(hisCollegeServe);
                    }

                    //补充wits_service中慧思学院部分的数据
                    hisWitsServe.CurrentCollegeServiceId = hisCollegeServe.Id;
                    hisWitsServe.CollegeAccountsNum = hisCollegeServe.PrimaryAccountsNum;
                    hisWitsServe.CollegeServiceCycleStart = hisCollegeServe.ServiceCycleStart;
                    hisWitsServe.CollegeServiceCycleEnd = hisCollegeServe.ServiceCycleEnd;
                    //返回的在服信息
                    retValue.CollegeOnServiceInfo = hisCollegeServe.MappingTo<CollegeServiceInfo>();
                    retValue.CollegeOnServiceInfo.ServiceId = hisCollegeServe.Id;
                    retValue.CollegeOnServiceInfo.AccountCount = hisCollegeServe.PrimaryAccountsNum;
                }
                //如果返回的账号中包含有SalesWits权限的账号，需要查看SalesWits服务的详情
                if (hisWitsServe.HasSalesWitsApp == true)
                {
                    //获取在服信息
                    Db_crm_contract_serviceinfo_saleswits? hisSalesWitsServe = null;
                    //先根据wits服务中的CurrentSalesWitsServiceId查找saleswits服务数据
                    if (!string.IsNullOrEmpty(hisWitsServe.CurrentSalesWitsServiceId))
                        hisSalesWitsServe = DbOpe_crm_contract_serviceinfo_saleswits.Instance.QueryByPrimaryKey(hisWitsServe.CurrentSalesWitsServiceId);
                    //再根据客户编码查找服务信息
                    if (!string.IsNullOrEmpty(hisWitsServe.CurrentSalesWitsServiceId))
                        hisSalesWitsServe = DbOpe_crm_contract_serviceinfo_saleswits.Instance.GetOnceOpendServiceByContractNum(contractNum);
                    //如果没找到服务数据，根据同步回的账号信息，补充服务数据
                    if (hisSalesWitsServe == null)
                    {
                        AppOptions priUserOpt = new AppOptions();
                        primaryUser.appsOptions.TryGetValue("CRM", out priUserOpt);
                        //补充服务表数据
                        hisSalesWitsServe = new Db_crm_contract_serviceinfo_saleswits()
                        {
                            Id = Guid.NewGuid().ToString(),
                            WitsApplId = hisWitsServe.WitsApplId,
                            ContractNum = contractNum,
                            AccountsNum = priUserOpt.MaxAccount,
                            ServiceCycleStart = priUserOpt.StartTime,
                            ServiceCycleEnd = priUserOpt.EndTime,
                            IsProcessed = (int)EnumGtisServiceIsProcess.Processed,
                            ProcessedTime = DateTime.Now,
                        };
                        DbOpe_crm_contract_serviceinfo_saleswits.Instance.Insert(hisSalesWitsServe);
                    }
                    //补充wits_service中saleswits部分的数据
                    hisWitsServe.CurrentSalesWitsServiceId = hisSalesWitsServe.Id;
                    hisWitsServe.SalesWitsAccountsNum = hisSalesWitsServe.AccountsNum;
                    hisWitsServe.SalesWitsServiceCycleStart = hisSalesWitsServe.ServiceCycleStart;
                    hisWitsServe.SalesWitsServiceCycleEnd = hisSalesWitsServe.ServiceCycleEnd;
                    //返回的在服信息
                    retValue.SalesWitsOnServiceInfo = hisSalesWitsServe.MappingTo<SalesWitsServiceInfo>();
                    retValue.SalesWitsOnServiceInfo.ServiceId = hisSalesWitsServe.Id;
                    retValue.SalesWitsOnServiceInfo.AccountCount = hisSalesWitsServe.AccountsNum;
                    retValue.SalesWitsOnServiceInfo.GiftResourceMonths = hisSalesWitsServe.CurrentGiftMonths;
                    retValue.SalesWitsOnServiceInfo.EmailCount = hisSalesWitsServe.CurrentGiftEmails;
                    retValue.SalesWitsOnServiceInfo.TokenCount = hisSalesWitsServe.CurrentGiftTokens;
                }
                //更新wits服务表
                DbOpe_crm_contract_serviceinfo_wits.Instance.UpdateData(hisWitsServe);

                //获取当前服务中的账号信息
                var crmWitsUserList = DbOpe_crm_contract_serviceinfo_wits_user.Instance.GetUsersByServelId(hisWitsServe.Id);
                //同步得到的账号List
                var sysUserIDs = gtisOpeUserList.Select(e => e.SysUserID).ToList();
                //当前服务的账号List
                var crmWitsUserIds = crmWitsUserList.Select(e => e.SysUserId).ToList();
                //交集--需要刷新的用户信息
                var intersectWitsUserIds = crmWitsUserIds.Intersect(sysUserIDs).ToList();
                intersectWitsUserIds.ForEach(sysUserId =>
                {
                    //g5返回的用户信息
                    var gtisOpeUser = gtisOpeUserList.Find(e => e.SysUserID == sysUserId);
                    //当前crm保存的用户信息
                    var crmWitsUser = crmWitsUserList.Find(e => e.SysUserId == sysUserId);
                    //修改crmGtisUser的属性
                    crmWitsUser.OpeningStatus = (int)DbOpe_crm_contract_productserviceinfo_gtis_appl_user.Instance.TransGtisUserStates(gtisOpeUser.State);
                    crmWitsUser.StartDate = gtisOpeUser.StartServerDate;
                    crmWitsUser.EndDate = gtisOpeUser.EndServerDate;
                    crmWitsUser.LoginIP = gtisOpeUser.LastLoginPlace;
                    crmWitsUser.LastLoginTime = gtisOpeUser.LastLoginDate;
                    crmWitsUser.SharePeopleNum = gtisOpeUser.SharingTimesUse;
                    crmWitsUser.PhoneUser = (gtisOpeUser.AllPhoneUserInfo.Count > 0) ? gtisOpeUser.AllPhoneUserInfo.First<BM_GtisOpe_UserPhoneInfo>().LinkMan : "";
                    crmWitsUser.PhoneUserNum = gtisOpeUser.AllPhoneUserInfo.Count;
                    crmWitsUser.AuthorizationNum = gtisOpeUser.AuthorizationNum;
                    crmWitsUser.GlobalSearchCode = gtisOpeUser.GlobalSearchCode;
                    crmWitsUser.GtisPermission = gtisOpeUser.apps.Contains("Gtis6");
                    crmWitsUser.GlobalSearchPermission = gtisOpeUser.apps.Contains("HQS");
                    crmWitsUser.CollegePermission = gtisOpeUser.apps.Contains("College");
                    crmWitsUser.SalesWitsPermission = gtisOpeUser.apps.Contains("CRM");
                    crmWitsUser.SalesWitsBindPhoneId = crmWitsUser.SalesWitsPermission ? (gtisOpeUser.AllPhoneUserInfo.Any(s => s.apps.Contains("CRM")) ? gtisOpeUser.AllPhoneUserInfo.Where(s => s.apps.Contains("CRM")).Select(s => s.SysUserPhoneID).First() : gtisOpeUser.AllPhoneUserInfo.Select(s => s.SysUserPhoneID).First()) : string.Empty;
                    DbOpe_crm_contract_serviceinfo_wits_user.Instance.UpdateDataQueue(crmWitsUser);
                    //填充此阶段的返回值
                    if (validStateList.Contains(crmWitsUser.OpeningStatus.GetValueOrDefault(0)))
                    {
                        var retOriUser = crmWitsUser.MappingTo<OriginUser>();
                        gtisOpeUser.AllPhoneUserInfo.ForEach(item =>
                        {
                            var phoneUser = new OriginPhoneUser();
                            phoneUser.SysUserPhoneID = item.SysUserPhoneID;
                            phoneUser.LinkMan = item.LinkMan;
                            phoneUser.SalesWitsPermission = item.apps.Contains("CRM");
                            retOriUser.OriPhoneUsers.Add(phoneUser);
                        });
                        retValue.OriginUserList.Add(retOriUser);
                    }
                });
                var tobeAddWitsUserIds = sysUserIDs.Except(crmWitsUserIds).ToList();
                tobeAddWitsUserIds.ForEach(sysUserId =>
                {
                    //gtis返回的用户信息
                    var gtisOpeUser = gtisOpeUserList.Find(e => e.SysUserID == sysUserId);
                    //当前crm保存的用户信息
                    var crmWitsUser = new Db_crm_contract_serviceinfo_wits_user();
                    //填充crmGtisUser的属性
                    crmWitsUser.WitsServeId = hisWitsServe.Id;
                    crmWitsUser.SysUserId = gtisOpeUser.SysUserID;
                    crmWitsUser.AccountNumber = gtisOpeUser.AccountNumber;
                    crmWitsUser.PassWord = gtisOpeUser.PassWord;
                    crmWitsUser.AccountType = (int)(gtisOpeUser.AccountType == 0 ? EnumGtisAccountType.Main : EnumGtisAccountType.Sub);
                    //crmWitsUser.IsProcessed = 0;
                    crmWitsUser.OpeningStatus = (int)DbOpe_crm_contract_productserviceinfo_gtis_appl_user.Instance.TransGtisUserStates(gtisOpeUser.State);
                    crmWitsUser.StartDate = gtisOpeUser.StartServerDate;
                    crmWitsUser.EndDate = gtisOpeUser.EndServerDate;
                    crmWitsUser.LoginIP = gtisOpeUser.LastLoginPlace;
                    crmWitsUser.LastLoginTime = gtisOpeUser.LastLoginDate;
                    crmWitsUser.SharePeopleNum = gtisOpeUser.SharingTimesUse;
                    crmWitsUser.PhoneUser = (gtisOpeUser.AllPhoneUserInfo.Count > 0) ? gtisOpeUser.AllPhoneUserInfo.First().LinkMan : "";
                    crmWitsUser.PhoneUserNum = gtisOpeUser.AllPhoneUserInfo.Count;
                    crmWitsUser.AuthorizationNum = gtisOpeUser.AuthorizationNum;
                    crmWitsUser.GlobalSearchCode = gtisOpeUser.GlobalSearchCode;
                    crmWitsUser.GtisPermission = gtisOpeUser.apps.Contains("Gtis6");
                    crmWitsUser.GlobalSearchPermission = gtisOpeUser.apps.Contains("HQS");
                    crmWitsUser.CollegePermission = gtisOpeUser.apps.Contains("College");
                    crmWitsUser.SalesWitsPermission = gtisOpeUser.apps.Contains("CRM");
                    crmWitsUser.SalesWitsBindPhoneId = gtisOpeUser.AllPhoneUserInfo.Where(s => s.apps.Contains("CRM")).Select(s => s.SysUserPhoneID).First();
                    DbOpe_crm_contract_serviceinfo_wits_user.Instance.InsertDataQueue(crmWitsUser);
                    //填充此阶段的返回值
                    if (validStateList.Contains(crmWitsUser.OpeningStatus.GetValueOrDefault(0)))
                    {
                        var retOriUser = crmWitsUser.MappingTo<OriginUser>();
                        gtisOpeUser.AllPhoneUserInfo.ForEach(item =>
                        {
                            var phoneUser = new OriginPhoneUser();
                            phoneUser.SysUserPhoneID = item.SysUserPhoneID;
                            phoneUser.LinkMan = item.LinkMan;
                            phoneUser.SalesWitsPermission = item.apps.Contains("CRM");
                            retOriUser.OriPhoneUsers.Add(phoneUser);
                        });
                        retValue.OriginUserList.Add(retOriUser);
                    }
                });
                //crmWitsUserIds 对 sysUserIDs 的差集--需要删除的用户信息
                var tobeDelWitsUserIds = crmWitsUserIds.Except(sysUserIDs).ToList();
                var tobeDelWitsUserList = crmWitsUserList.Where(e => tobeDelWitsUserIds.Contains(e.SysUserId)).ToList();
                tobeDelWitsUserList.ForEach(user =>
                {
                    user.OpeningStatus = (int)EnumGtisUserOpeningStatus.NotFound;
                    DbOpe_crm_contract_serviceinfo_wits_user.Instance.UpdateDataQueue(user);
                });
                //填充此阶段的返回值
                retValue.ContractNum = contractNum;
                retValue.WitsServeId = hisWitsServe.Id;
                retValue.OriginUserList = retValue.OriginUserList.OrderBy(e => e.AccountNumber).ToList();
            });
            return retValue;
        }

        /// <summary>
        /// 使用原有账号时处理账号信息
        /// </summary>
        /// <param name="addApply_In"></param>
        public void DealApplyUserInfo(AddWitsAppl_In addApply_In)
        {
            var ori_witsServeUser_list = DbOpe_crm_contract_serviceinfo_wits_user.Instance.GetUsersByServelId(addApply_In.OriWitsServiceId);
            //如果存在未申请的服务，且账号列表中存在原有账号，则针对未申请的服务，账号权限取原来的权限
            addApply_In.UserList.ForEach(user =>
            {
                //根据是否有主键Id判断是否为现有账号
                if (!string.IsNullOrEmpty(user.SysUserId))
                {
                    //查找到现有账号数据
                    var ori_witsUser = ori_witsServeUser_list.Find(e => e.SysUserId == user.SysUserId);
                    //如果当前申请中不包括gtis服务，账号的Gtis权限应取当前数据中账号的Gtis权限
                    if (addApply_In.GtisApply == null)
                        user.GtisPermission = ori_witsUser.GtisPermission;
                    //如果当前申请中不包括环球搜服务，账号的环球搜权限应取当前数据中账号的环球搜权限
                    if (addApply_In.GlobalSearchApply == null)
                        user.GlobalSearchPermission = ori_witsUser.GlobalSearchPermission;
                    //如果当前申请中不包括慧思学院服务，账号的慧思学院权限应取当前数据中账号的慧思学院权限
                    if (addApply_In.CollegeApply == null)
                        user.CollegePermission = ori_witsUser.CollegePermission;
                    //如果当前申请中不包括SalesWits服务，账号的SalesWits权限应取当前数据中账号的SalesWits权限
                    if (addApply_In.SalesWitsApply == null)
                        user.SalesWitsPermission = ori_witsUser.SalesWitsPermission;
                }
            });

        }

        /// <summary>
        /// 验证当前申请是否可以提交
        /// </summary>
        /// <param name="addApply_In"></param>
        /// <param name="contract"></param>
        /// <exception cref="ApiException"></exception>
        public void CheckWitsAppl(AddWitsAppl_In addApply_In, Db_crm_contract contract)
        {
            if (contract == null)
                throw new ApiException("该用户没有数据权限");
            //合同审核通过后，才可申请服务，并只可申请合同内签约产品的服务项目。
            if (contract.ContractStatus != EnumContractStatus.Pass.ToInt())
                throw new ApiException("当前合同状态下，不可以添加合同服务信息-环球慧思申请信息");
            //判断是否会重复申请。若不存在有效的提交或通过状态的申请，则可以进行申请
            if (DbOpe_crm_contract_productserviceinfo_wits_appl.Instance.IsForbidApplyService(addApply_In.ContractProductInfoSeriesId))
                throw new ApiException("Wits产品的申请已存在，不可重复申请");
            //续约合同服务申请的验证
            if (contract.ContractType == (int)EnumContractType.ReNew)
            {
                if (string.IsNullOrEmpty(addApply_In.RenewContractNum))
                    throw new ApiException("续约客户编码不能为空");
                if (string.IsNullOrEmpty(addApply_In.RenewFirstParty))
                    throw new ApiException("续约公司不能为空");
                if (DbOpe_crm_contract_productserviceinfo_wits_appl.Instance.IsExistInReviewApplyByFirstParty(contract.FirstParty))
                    throw new ApiException("当前甲方公司存在未审核的申请，不可以申请续约");
                if (DbOpe_crm_contract_productserviceinfo_wits_appl.Instance.IsExistInReviewApplyByFirstParty(addApply_In.RenewFirstParty))
                    throw new ApiException("当前被续约公司存在未审核的申请，不可以申请续约");
            }
            //声明优惠券使用个数和个人服务使用天数(借用此处验证的if条件来赋值,每个业务的服务时间验证时使用)
            var couponCount = 0; var perlongServiceDays = 0;
            #region 优惠券和个人服务天数使用验证
            //优惠券使用验证
            if (addApply_In.DiscountType == EnumGtisDiscountType.Coupon && addApply_In.CounponDetailIdList != null && addApply_In.CounponDetailIdList.Count > 0)
            {
                if (addApply_In.GtisApply != null)
                    throw new ApiException("申请内容中未包含Gtis产品，无法使用优惠券");
                if (DbOpe_crm_customer_coupon.Instance.CheckCouponListExistExpire(addApply_In.CounponDetailIdList))
                    throw new ApiException("优惠券失效，请重新申请");
                if (DbOpe_crm_customer_coupon.Instance.ChangeCouponType(addApply_In.CounponDetailIdList, EnumCouponType.Using).state != 1)
                    throw new ApiException("优惠券无法使用，请重新核对");
                //赋值，服务时间验证时使用
                couponCount = addApply_In.CounponDetailIdList.Count;
            }

            if (addApply_In.PerlongServiceDays.GetValueOrDefault(0) > 0)
            {
                //当前申请了Gtis时才可以使用个人服务天数
                if (addApply_In.GtisApply == null)
                    throw new ApiException("申请内容中未包含Gtis产品，无法使用个人服务天数无法使用");
                //获取销售经理当月可用个人服务时间
                var duration = BLL_PrivateService.Instance.GetDuration();
                if (addApply_In.PrivateServiceDays.GetValueOrDefault(0) > duration.Duration)
                    throw new ApiException("个人服务天数超出当月可使用上限");
                //计算超额服务天数
                var overServiceDays = addApply_In.PerlongServiceDays.GetValueOrDefault(0) - addApply_In.PrivateServiceDays.GetValueOrDefault(0);
                //计算的超额服务天数大于当前人员可使用的超额服务天数上限，弹出异常
                if (overServiceDays > duration.ReleaseChargeDay)
                    throw new ApiException("超额服务天数超出当月可使用上限");
                //判断续约合同的服务是否已经中断超过6个月(此处服务单指Gtis服务，如果以前)
                if (contract.ContractType == EnumContractType.ReNew.ToInt())
                {
                    var renewService = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetGtisServiceInfoByContractNum(contract.RenewalContractNum);
                    //如果被续约合同存在Gtis服务且服务已中断超过6个月，无法使用个人服务天数
                    if (renewService != null && renewService.ServiceCycleEnd.Value.AddMonths(6) < DateTime.Now.Date)
                        throw new ApiException("服务中断时间已超过6个月，无法使用个人服务天数");
                }
                //若计算的自费天数与传入的不符，弹出异常
                if (overServiceDays != addApply_In.OverServiceDays)
                    throw new ApiException("超额服务天数与实际情况不符，请重新填写");
                //赋值，服务时间验证时使用
                perlongServiceDays = addApply_In.PerlongServiceDays.GetValueOrDefault(0);
            }
            #endregion
            #region 每个业务对服务月份进行验证
            if (addApply_In.GtisApply != null)
                CheckWitsAppl_Sub(addApply_In.GtisApply.ServiceCycleStartAfterDiscount, addApply_In.GtisApply.ServiceCycleEndAfterDiscount, addApply_In.GtisApply.ServiceMonthAfterDiscount, couponCount, perlongServiceDays, "Gits");
            if (addApply_In.GlobalSearchApply != null)
                CheckWitsAppl_Sub(addApply_In.GlobalSearchApply.ServiceCycleStartAfterDiscount, addApply_In.GlobalSearchApply.ServiceCycleEndAfterDiscount, addApply_In.GlobalSearchApply.ServiceMonthAfterDiscount, couponCount, perlongServiceDays, "环球搜");
            if (addApply_In.CollegeApply != null)
                CheckWitsAppl_Sub(addApply_In.CollegeApply.ServiceCycleStartAfterDiscount, addApply_In.CollegeApply.ServiceCycleEndAfterDiscount, addApply_In.CollegeApply.ServiceMonthAfterDiscount, couponCount, perlongServiceDays, "慧思学院");
            if (addApply_In.SalesWitsApply != null)
                CheckWitsAppl_Sub(addApply_In.SalesWitsApply.ServiceCycleStartAfterDiscount, addApply_In.SalesWitsApply.ServiceCycleEndAfterDiscount, addApply_In.SalesWitsApply.ServiceMonthAfterDiscount, couponCount, perlongServiceDays, "SalesWits");
            #endregion
            #region 账号逻辑验证
            //使用原有账号时必须传入在服的WitsService表Id
            if (addApply_In.AccountGenerationMethod == EnumGtisAccountGenerationMethod.Generate)
            {
                if (string.IsNullOrEmpty(addApply_In.OriWitsServiceId))
                    throw new ApiException("需要指定原有账号");
                var ori_witsServeUser_list = DbOpe_crm_contract_serviceinfo_wits_user.Instance.GetUsersByServelId(addApply_In.OriWitsServiceId);
                //如果存在未申请的服务，且账号列表中存在原有账号，则针对未申请的服务，账号权限取原来的权限
                addApply_In.UserList.ForEach(user =>
                {
                    //根据是否有主键Id判断是否为现有账号
                    if (!string.IsNullOrEmpty(user.SysUserId))
                    {
                        //查找到现有账号数据
                        var ori_witsUser = ori_witsServeUser_list.Find(e => e.SysUserId == user.SysUserId);
                        //如果当前申请中不包括gtis服务，账号的Gtis权限应取当前数据中账号的Gtis权限
                        if (addApply_In.GtisApply == null)
                            user.GtisPermission = ori_witsUser.GtisPermission;
                        //如果当前申请中不包括环球搜服务，账号的环球搜权限应取当前数据中账号的环球搜权限
                        if (addApply_In.GlobalSearchApply == null)
                            user.GlobalSearchPermission = ori_witsUser.GlobalSearchPermission;
                        //如果当前申请中不包括慧思学院服务，账号的慧思学院权限应取当前数据中账号的慧思学院权限
                        if (addApply_In.CollegeApply == null)
                            user.CollegePermission = ori_witsUser.CollegePermission;
                        //如果当前申请中不包括SalesWits服务，账号的SalesWits权限应取当前数据中账号的SalesWits权限
                        if (addApply_In.SalesWitsApply == null)
                        {
                            user.SalesWitsPermission = ori_witsUser.SalesWitsPermission;
                            if (ori_witsUser.SalesWitsPermission)
                                user.SalesWitsBindPhoneId = ori_witsUser.SalesWitsBindPhoneId;
                        }
                        if(user.SalesWitsPermission.GetValueOrDefault(false) && !string.IsNullOrEmpty(user.SalesWitsBindPhoneId))
                            throw new ApiException("使用原有账号方式开通SalesWits时，账号必须指定SalesWits使用者");
                    }
                });
            }

            #region 验证账号数量与每个账号勾选的权限数量的逻辑是否相符
            int maxAccountNum = 0;
            if (addApply_In.GtisApply != null)
            {
                //申请的账号数量
                var applyAccountNum = addApply_In.GtisApply.SubAccountsNum + 1;
                if (addApply_In.UserList.Count(e => e.GtisPermission == true) != applyAccountNum)
                    throw new ApiException("选择了Gtis权限的账号数量必须等于Gtis申请的账号数量");
                //记录可申请最大账号数量
                maxAccountNum = addApply_In.GtisApply.SubAccountsNum + 1;
            }
            if (addApply_In.GlobalSearchApply != null)
            {
                //申请的账号数量
                var applyAccountNum = addApply_In.GlobalSearchApply.AccountCount.GetValueOrDefault(0);
                if (addApply_In.UserList.Count(e => e.GlobalSearchPermission == true) > applyAccountNum)
                    throw new ApiException("选择了环球搜权限的账号数量超过Gtis可申请最大账号数量");
                //记录可申请最大账号数量
                maxAccountNum = maxAccountNum > applyAccountNum ? maxAccountNum : applyAccountNum;
            }
            if (addApply_In.CollegeApply != null)
            {
                //申请的账号数量
                var applyAccountNum = addApply_In.CollegeApply.AccountCount.GetValueOrDefault(0);
                if (addApply_In.UserList.Count(e => e.CollegePermission == true) > applyAccountNum)
                    throw new ApiException("选择了慧思学院权限的账号数量超过Gtis可申请最大账号数量");
                //记录可申请最大账号数量
                maxAccountNum = maxAccountNum > applyAccountNum ? maxAccountNum : applyAccountNum;
            }
            if (addApply_In.SalesWitsApply != null)
            {
                //申请的账号数量
                var applyAccountNum = addApply_In.SalesWitsApply.AccountCount.GetValueOrDefault(0);
                if (addApply_In.UserList.Count(e => e.SalesWitsPermission == true) > applyAccountNum)
                    throw new ApiException("选择了SalesWits权限的账号数量超过Gtis可申请最大账号数量");
                //记录可申请最大账号数量
                maxAccountNum = maxAccountNum > applyAccountNum ? maxAccountNum : applyAccountNum;
            }
            if (addApply_In.UserList.Count() > maxAccountNum)
                throw new ApiException("申请的账号数量超过允许申请的账号数量");
            if (addApply_In.UserList.Any(e => e.SalesWitsPermission == true && !e.GtisPermission == true) && addApply_In.UserList.Any(e => e.GtisPermission == true && !e.SalesWitsPermission == true))
                throw new ApiException("SalesWits权限须优先绑定在选择了Gtis权限的账号上");
            #endregion
            #endregion
        }

        /// <summary>
        /// 验证申请中的服务月份、服务周期是否合法
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="month"></param>
        /// <param name="couponCount"></param>
        /// <param name="perlongServiceDays"></param>
        /// <param name="errStrFront"></param>
        /// <exception cref="ApiException"></exception>
        private void CheckWitsAppl_Sub(DateTime? startDate, DateTime? endDate, int? month, int couponCount, int perlongServiceDays, string errStrFront)
        {
            if (startDate == null || endDate == null || month == null)
                throw new ApiException(errStrFront + "产品服务时间信息不完整");
            /*
            //根据服务月份计算结束时间
            var actEndDate = startDate.Value.AddMonths(month.GetValueOrDefault(0));
            //累加续约服务券
            actEndDate = actEndDate.AddMonths(couponCount);
            //累加个人服务天数
            actEndDate.AddDays(perlongServiceDays);
            if (endDate != actEndDate)
                throw new ApiException(errStrFront + "产品服务月份设置不正确");
            */
        }

        /// <summary>
        /// 验证服务变更申请是否可以提交
        /// </summary>
        /// <param name="changeApply_In">服务变更申请输入参数</param>
        /// <param name="contract">合同信息</param>
        /// <exception cref="ApiException"></exception>
        public void CheckWitsChangeAppl(VM_ContractServiceChange.AddWitsUpdateAppl_In changeApply_In, Db_crm_contract contract)
        {
            if (contract == null)
                throw new ApiException("该用户没有数据权限");

            // 合同审核通过后，才可申请服务变更
            if (contract.ContractStatus != EnumContractStatus.Pass.ToInt())
                throw new ApiException("当前合同状态下，不可以进行服务变更申请");

            // 验证变更原因不能为空
            if (changeApply_In.ChangeReasonDetails == null || changeApply_In.ChangeReasonDetails.Count == 0)
                throw new ApiException("变更原因不能为空");

            // 验证每个变更原因的合法性
            foreach (var reasonDetail in changeApply_In.ChangeReasonDetails)
            {
                // 验证变更原因枚举值的合法性
                if (!Enum.IsDefined(typeof(EnumGtisServiceChangeProject), reasonDetail.ChangeReason))
                    throw new ApiException($"无效的变更原因：{reasonDetail.ChangeReason}");
            }

            // 获取当前在服的服务信息，用于变更验证
            var currentWitsService = DbOpe_crm_contract_serviceinfo_wits.Instance.GetDataById(changeApply_In.OriWitsServiceId);
            if (currentWitsService == null)
                throw new ApiException("未找到当前在服的服务信息，无法进行变更");

            // 声明优惠券使用个数和个人服务使用天数
            var couponCount = 0;
            var perlongServiceDays = 0;

            #region 优惠券和个人服务天数使用验证
            // 优惠券使用验证（变更时的逻辑与新申请类似）
            if (changeApply_In.DiscountType == EnumGtisDiscountType.Coupon && changeApply_In.CounponDetailIdList != null && changeApply_In.CounponDetailIdList.Count > 0)
            {
                if (changeApply_In.GtisApply == null)
                    throw new ApiException("申请内容中未包含Gtis产品，无法使用优惠券");
                if (DbOpe_crm_customer_coupon.Instance.CheckCouponListExistExpire(changeApply_In.CounponDetailIdList))
                    throw new ApiException("优惠券失效，请重新申请");
                if (DbOpe_crm_customer_coupon.Instance.ChangeCouponType(changeApply_In.CounponDetailIdList, EnumCouponType.Using).state != 1)
                    throw new ApiException("优惠券无法使用，请重新核对");
                couponCount = changeApply_In.CounponDetailIdList.Count;
            }

            // 个人服务天数验证
            if (changeApply_In.PerlongServiceDays.GetValueOrDefault(0) > 0)
            {
                if (changeApply_In.GtisApply == null)
                    throw new ApiException("申请内容中未包含Gtis产品，无法使用个人服务天数");

                var duration = BLL_PrivateService.Instance.GetDuration();
                if (changeApply_In.PrivateServiceDays.GetValueOrDefault(0) > duration.Duration)
                    throw new ApiException("个人服务天数超出当月可使用上限");

                var overServiceDays = changeApply_In.PerlongServiceDays.GetValueOrDefault(0) - changeApply_In.PrivateServiceDays.GetValueOrDefault(0);
                if (overServiceDays > duration.ReleaseChargeDay)
                    throw new ApiException("超额服务天数超出当月可使用上限");

                if (overServiceDays != changeApply_In.OverServiceDays)
                    throw new ApiException("超额服务天数与实际情况不符，请重新填写");

                perlongServiceDays = changeApply_In.PerlongServiceDays.GetValueOrDefault(0);
            }
            #endregion

            #region 每个业务对服务月份进行验证（变更时需要考虑新增月数）
            if (changeApply_In.GtisApply != null)
            {
                if(!changeApply_In.GtisApply.ServiceCycleStart.HasValue||!changeApply_In.GtisApply.ServiceCycleEnd.HasValue)
                    throw new ApiException("Gtis服务时间信息不完整");
            }
            if (changeApply_In.GlobalSearchApply != null)
            {
                if(!changeApply_In.GlobalSearchApply.ServiceCycleStart.HasValue||!changeApply_In.GlobalSearchApply.ServiceCycleEnd.HasValue)
                    throw new ApiException("环球搜服务时间信息不完整");
            }
            if (changeApply_In.CollegeApply != null)
            {
                if(!changeApply_In.CollegeApply.ServiceCycleStart.HasValue||!changeApply_In.CollegeApply.ServiceCycleEnd.HasValue)
                    throw new ApiException("慧思学院服务时间信息不完整");
            }
            if (changeApply_In.SalesWitsApply != null)
            {
                if(!changeApply_In.SalesWitsApply.ServiceCycleStart.HasValue||!changeApply_In.SalesWitsApply.ServiceCycleEnd.HasValue)
                    throw new ApiException("SalesWits服务时间信息不完整");
            }
            #endregion

            #region 账号变更逻辑验证
            // 服务变更时，账号信息的验证逻辑
            if (changeApply_In.UserList != null && changeApply_In.UserList.Count > 0)
            {
                // 获取当前在服的用户信息
                var currentUsers = DbOpe_crm_contract_serviceinfo_wits_user.Instance.GetUsersByServelId(currentWitsService.Id);

                // 验证用户变更的合法性
                ValidateUserChanges(changeApply_In.UserList, currentUsers, changeApply_In);
            }
            #endregion

            #region 服务变更特有的业务逻辑验证
            // 验证变更内容的合理性
            ValidateChangeContent(changeApply_In, currentWitsService);
            #endregion
        }

        /// <summary>
        /// 验证用户变更的合法性
        /// </summary>
        /// <param name="changeUserList">变更申请中的用户列表</param>
        /// <param name="currentUsers">当前在服的用户列表</param>
        /// <param name="changeApply_In">变更申请输入参数</param>
        /// <exception cref="ApiException"></exception>
        private void ValidateUserChanges(List<VM_ContractServiceChange.UpdateGtisSeriesAppl_In_UserList> changeUserList,
            List<Db_crm_contract_serviceinfo_wits_user> currentUsers,
            VM_ContractServiceChange.AddWitsUpdateAppl_In changeApply_In)
        {
            // 验证用户变更的基本逻辑
            foreach (var changeUser in changeUserList)
            {
                // 如果是现有用户（有UserId），验证权限变更的合法性
                if (!string.IsNullOrEmpty(changeUser.SysUserId))
                {
                    var currentUser = currentUsers.FirstOrDefault(u => u.SysUserId == changeUser.SysUserId);
                    if (currentUser == null)
                        throw new ApiException($"用户{changeUser.SysUserId}不存在于当前服务中，无法进行变更");

                    // 验证权限变更的合理性
                    ValidateUserPermissionChanges(changeUser, currentUser, changeApply_In);
                }

                // 验证SalesWits权限必须绑定使用者
                if (changeUser.SalesWitsPermission == true && string.IsNullOrEmpty(changeUser.SalesWitsBindPhoneId))
                    throw new ApiException($"账号{changeUser.SysUserId}开通SalesWits权限时必须指定使用者");
            }

            // 验证账号数量与权限数量的匹配性
            ValidateAccountPermissionCounts(changeUserList, changeApply_In);
        }

        /// <summary>
        /// 验证用户权限变更的合理性
        /// </summary>
        /// <param name="changeUser">变更申请中的用户</param>
        /// <param name="currentUser">当前在服的用户</param>
        /// <param name="changeApply_In">变更申请输入参数</param>
        /// <exception cref="ApiException"></exception>
        private void ValidateUserPermissionChanges(VM_ContractServiceChange.UpdateGtisSeriesAppl_In_UserList changeUser,
            Db_crm_contract_serviceinfo_wits_user currentUser,
            VM_ContractServiceChange.AddWitsUpdateAppl_In changeApply_In)
        {
            // 如果当前申请中不包括某个服务，用户的该服务权限不应该发生变更
            if (changeApply_In.GtisApply == null && changeUser.GtisPermission != currentUser.GtisPermission)
                throw new ApiException($"未申请Gtis服务变更，账号{changeUser.SysUserId}的Gtis权限不应发生变更");

            if (changeApply_In.GlobalSearchApply == null && changeUser.GlobalSearchPermission != currentUser.GlobalSearchPermission)
                throw new ApiException($"未申请环球搜服务变更，账号{changeUser.SysUserId}的环球搜权限不应发生变更");

            if (changeApply_In.CollegeApply == null && changeUser.CollegePermission != currentUser.CollegePermission)
                throw new ApiException($"未申请慧思学院服务变更，账号{changeUser.SysUserId}的慧思学院权限不应发生变更");

            if (changeApply_In.SalesWitsApply == null && changeUser.SalesWitsPermission != currentUser.SalesWitsPermission)
                throw new ApiException($"未申请SalesWits服务变更，账号{changeUser.SysUserId}的SalesWits权限不应发生变更");
        }

        /// <summary>
        /// 验证账号数量与权限数量的匹配性
        /// </summary>
        /// <param name="userList">用户列表</param>
        /// <param name="changeApply_In">变更申请输入参数</param>
        /// <exception cref="ApiException"></exception>
        private void ValidateAccountPermissionCounts(List<VM_ContractServiceChange.UpdateGtisSeriesAppl_In_UserList> userList,
            VM_ContractServiceChange.AddWitsUpdateAppl_In changeApply_In)
        {
            int maxAccountNum = 0;

            // 验证各服务的账号数量限制
            if (changeApply_In.GtisApply != null)
            {
                var gtisPermissionCount = userList.Count(u => u.GtisPermission == true);
                var applyAccountNum = changeApply_In.GtisApply.SubAccountsNum + 1;
                if (gtisPermissionCount != applyAccountNum)
                    throw new ApiException("选择了Gtis权限的账号数量必须等于Gtis申请的账号数量");
                maxAccountNum = applyAccountNum;
            }

            if (changeApply_In.GlobalSearchApply != null)
            {
                var globalSearchPermissionCount = userList.Count(u => u.GlobalSearchPermission == true);
                var applyAccountNum = changeApply_In.GlobalSearchApply.AccountCount.GetValueOrDefault(0);
                if (globalSearchPermissionCount > applyAccountNum)
                    throw new ApiException("选择了环球搜权限的账号数量超过申请的最大账号数量");
                maxAccountNum = Math.Max(maxAccountNum, applyAccountNum);
            }

            if (changeApply_In.CollegeApply != null)
            {
                var collegePermissionCount = userList.Count(u => u.CollegePermission == true);
                var applyAccountNum = changeApply_In.CollegeApply.AccountCount.GetValueOrDefault(0);
                if (collegePermissionCount > applyAccountNum)
                    throw new ApiException("选择了慧思学院权限的账号数量超过申请的最大账号数量");
                maxAccountNum = Math.Max(maxAccountNum, applyAccountNum);
            }

            if (changeApply_In.SalesWitsApply != null)
            {
                var salesWitsPermissionCount = userList.Count(u => u.SalesWitsPermission == true);
                var applyAccountNum = changeApply_In.SalesWitsApply.AccountCount.GetValueOrDefault(0);
                if (salesWitsPermissionCount > applyAccountNum)
                    throw new ApiException("选择了SalesWits权限的账号数量超过申请的最大账号数量");
                maxAccountNum = Math.Max(maxAccountNum, applyAccountNum);
            }

            if (userList.Count > maxAccountNum)
                throw new ApiException("申请的账号数量超过允许申请的账号数量");

            // 验证SalesWits权限必须优先绑定在选择了Gtis权限的账号上
            if (userList.Any(u => u.SalesWitsPermission == true && u.GtisPermission != true) &&
                userList.Any(u => u.GtisPermission == true && u.SalesWitsPermission != true))
                throw new ApiException("SalesWits权限须优先绑定在选择了Gtis权限的账号上");
        }

        /// <summary>
        /// 验证变更内容的合理性
        /// </summary>
        /// <param name="changeApply_In">变更申请输入参数</param>
        /// <param name="currentWitsService">当前在服的服务信息</param>
        /// <exception cref="ApiException"></exception>
        private void ValidateChangeContent(VM_ContractServiceChange.AddWitsUpdateAppl_In changeApply_In,
            Db_crm_contract_serviceinfo_wits currentWitsService)
        {
            // 验证变更原因与变更内容的一致性
            var changeReasons = changeApply_In.ChangeReasonDetails.Select(r => r.ChangeReason).ToList();

            // 如果包含增加SalesWits账户数的变更原因，验证账户数确实有增加
            if (changeReasons.Contains(EnumGtisServiceChangeProject.SalesWitsAddAccount))
            {
                if (changeApply_In.SalesWitsApply == null)
                    throw new ApiException("选择了增加SalesWits账户数变更原因，但未提供SalesWits变更申请信息");

                // 这里可以添加更多的账户数增加验证逻辑
                // 比如对比当前服务的账户数与变更申请的账户数
            }

            // 如果包含SalesWits充值的变更原因，验证充值信息
            if (changeReasons.Contains(EnumGtisServiceChangeProject.SalesWitsRecharge))
            {
                if (changeApply_In.SalesWitsApply == null)
                    throw new ApiException("选择了SalesWits充值变更原因，但未提供SalesWits变更申请信息");

                // 验证充值相关信息（检查是否有充值相关的配置）
                // 注意：具体的充值金额字段需要根据实际的SalesWits变更申请模型来确定
                LogUtil.AddLog("检测到SalesWits充值变更原因，验证充值配置");
            }

        }


        /// <summary>
        /// Gtis系列产品服务申请
        /// </summary>
        /// <param name="addApply_In"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        public Db_crm_contract_productserviceinfo_wits_appl AddWitsAppl(AddWitsAppl_In addApply_In)
        {
            //将当前合同的旧服务申请(被拒绝)置为失效
            DbOpe_crm_contract_productserviceinfo_wits_appl.Instance.InvalidOldApply(addApply_In.ContractProductInfoSeriesId);
            //根据传入参数匹配声明申请数据
            var apply = addApply_In.MappingTo<Db_crm_contract_productserviceinfo_wits_appl>();
            //补充未能匹配的数据
            apply.ApplicantId = UserId;
            apply.ApplicantDate = DateTime.Now;
            apply.State = EnumProcessStatus.Submit;
            apply.ProcessingType = EnumProcessingType.Add;
            apply.IsInvalid = EnumIsInvalid.Effective;
            apply.IsGtisApply = addApply_In.GtisApply != null;
            apply.IsGlobalSearchApply = addApply_In.GlobalSearchApply != null;
            apply.IsCollegeApply = addApply_In.CollegeApply != null;
            apply.IsSalesWitsApply = addApply_In.SalesWitsApply != null;
            apply.Remark4List = addApply_In.Remark;//列表中显示的备注信息，暂时保留
            //如果使用了优惠券，转换优惠券参数并保存
            if (apply.DiscountType == EnumGtisDiscountType.Coupon && addApply_In.CounponDetailIdList != null && addApply_In.CounponDetailIdList.Count > 0)
                apply.CounponDetailIds = String.Join(',', addApply_In.CounponDetailIdList);
            #region 计算账号的开通周期：所有服务的最早开始时间--所有服务的最晚结束时间
            var startDateList = new List<DateTime>(); var endDateList = new List<DateTime>();
            if (addApply_In.GtisApply != null)
            {//如果申请包含Gits服务，取提交信息中的开始结束时间
                startDateList.Add(addApply_In.GtisApply.ServiceCycleStartAfterDiscount.Value);
                endDateList.Add(addApply_In.GtisApply.ServiceCycleEndAfterDiscount.Value);
            }
            else if (!string.IsNullOrEmpty(apply.RenewContractNum))
            {//如果未申请Gtis服务且当前合同是续约合同，则需要查找当前在服信息的开始结束时间
                var service = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetValidGtisServiceInfoByContractNum(apply.RenewContractNum);
                if (service != null)
                {
                    startDateList.Add(service.ServiceCycleStart.Value);
                    endDateList.Add(service.ServiceCycleEnd.Value);
                }
            }
            if (addApply_In.GlobalSearchApply != null)
            {//如果申请包含环球搜服务，取提交信息中的开始结束时间
                startDateList.Add(addApply_In.GlobalSearchApply.ServiceCycleStartAfterDiscount.Value);
                endDateList.Add(addApply_In.GlobalSearchApply.ServiceCycleEndAfterDiscount.Value);
            }
            else if (!string.IsNullOrEmpty(apply.RenewContractNum))
            {//如果未申请环球搜服务且当前合同是续约合同，则需要查找当前在服信息的开始结束时间
                var service = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetValidGlobalSearchServiceByContractNum(apply.RenewContractNum);
                if (service != null)
                {
                    startDateList.Add(service.ServiceCycleStart.Value);
                    endDateList.Add(service.ServiceCycleEnd.Value);
                }
            }
            if (addApply_In.CollegeApply != null)
            {//如果申请包含慧思学院服务，取提交信息中的开始结束时间
                startDateList.Add(addApply_In.CollegeApply.ServiceCycleStartAfterDiscount.Value);
                endDateList.Add(addApply_In.CollegeApply.ServiceCycleEndAfterDiscount.Value);
            }
            else if (!string.IsNullOrEmpty(apply.RenewContractNum))
            {//如果未申请慧思学院服务且当前合同是续约合同，则需要查找当前在服信息的开始结束时间
                var service = DbOpe_crm_contract_serviceinfo_college.Instance.GetValidColegeServiceByContractNum(apply.RenewContractNum);
                if (service != null)
                {
                    startDateList.Add(service.ServiceCycleStart.Value);
                    endDateList.Add(service.ServiceCycleEnd.Value);
                }
            }
            if (addApply_In.SalesWitsApply != null)
            {//如果申请包含SalesWits服务，取提交信息中的开始结束时间
                startDateList.Add(addApply_In.SalesWitsApply.ServiceCycleStartAfterDiscount.Value);
                endDateList.Add(addApply_In.SalesWitsApply.ServiceCycleEndAfterDiscount.Value);
            }
            else if (!string.IsNullOrEmpty(apply.RenewContractNum))
            {//如果未申请SalesWits服务且当前合同是续约合同，则需要查找当前在服信息的开始结束时间
                //此处还未建表
            }
            #endregion
            apply.ServiceCycleStart = startDateList.Min();
            apply.ServiceCycleEnd = endDateList.Max();
            //插入申请数据，并记录返回的主键Id
            apply.Id = DbOpe_crm_contract_productserviceinfo_wits_appl.Instance.InsertDataReturnId(apply).ToString();
            //记录个人服务天数使用情况
            if (apply.PerlongServiceDays.GetValueOrDefault(0) > 0)
            {
                //登记使用个人使用时间,将对应天数改为使用中
                BLL_PrivateService.Instance.UsingPrivateServiceForContractService(apply.ContractId, EnumPrivateServiceType.Using, apply.Id, apply.PrivateServiceDays.GetValueOrDefault(0), apply.OverServiceDays.GetValueOrDefault(0));
            }
            #region 写入各个服务的申请数据
            if (addApply_In.GtisApply != null)//写入Gtis申请数据
                AddGtisAppl(addApply_In.GtisApply, apply);
            if (addApply_In.GlobalSearchApply != null)//写入环球搜申请数据
                AddGlobalSearchAppl(addApply_In.GlobalSearchApply, apply);
            if (addApply_In.CollegeApply != null)//写入慧思学院申请数据
                AddCollegeAppl(addApply_In.CollegeApply, apply);
            if (addApply_In.SalesWitsApply != null) //写入SalesWits申请数据
                AddSalesWitsAppl(addApply_In.SalesWitsApply, apply);
            #endregion
            #region 写入账号信息
            addApply_In.UserList.ForEach(user =>
            {
                var userData = user.MappingTo<Db_crm_contract_productserviceinfo_wits_appl_user>();
                userData.WitsApplId = apply.Id;
                userData.StartDate = startDateList.Min();
                userData.EndDate = endDateList.Max();
                userData.SalesWitsPhoneId = user.SalesWitsBindPhoneId;
                DbOpe_crm_contract_productserviceinfo_wits_appl_user.Instance.InsertData(userData);
            });
            #endregion

            // 更新相关合同产品申请状态为待审核
            ContractProductApplStateHelper.UpdateWitsContractProductApplState(
                apply.Id,
                EnumProcessStatus.Submit,
                UserId,
                null,
                "");

            return apply;
        }
        /// <summary>
        /// 合并服务变更方法的内部实现
        /// </summary>
        /// <param name="updateApply_In"></param>
        /// <returns></returns>
        public Db_crm_contract_productserviceinfo_wits_appl _UpdateWitsAppl(AddWitsUpdateAppl_In updateApply_In)
        {
            //验证变更原因
            if (updateApply_In.ChangeReasonDetails == null || updateApply_In.ChangeReasonDetails.Count == 0)
            {
                throw new ApiException("请选择变更原因");
            }

            // 合同权限和状态验证
            var contract = BLL_Contract.Instance.GetContractBasicInfoById(updateApply_In.ContractId);
            if (contract == null)
            {
                throw new ApiException("该用户没有数据权限");
            }
            if (contract.ContractStatus != EnumContractStatus.Pass.ToInt())
            {
                throw new ApiException("当前合同状态下，不可以进行服务变更申请");
            }

            // 检查是否可以提交服务变更 todo
            // 申请前置验证 (移除CheckAppl调用，该方法在Wits申请中不存在)

            //将当前合同的旧服务申请(被拒绝)置为失效
            DbOpe_crm_contract_productserviceinfo_wits_appl.Instance.InvalidOldApply(updateApply_In.ContractProductInfoSeriesId);

            //根据传入参数匹配声明申请数据
            var apply = updateApply_In.MappingTo<Db_crm_contract_productserviceinfo_wits_appl>();

            // 业务规则验证
            if (updateApply_In.UserList != null && updateApply_In.UserList.Count > 0)
            {
                // 账号数量一致性验证
                var mainAccountCount = updateApply_In.UserList.Count(u => u.AccountType == (int)EnumGtisAccountType.Main);
                if (mainAccountCount != 1)
                {
                    throw new ApiException("必须且只能有一个主账号");
                }

                // 验证子账号数量与申请的账号数是否一致
                var subAccountCount = updateApply_In.UserList.Count(u => u.AccountType != (int)EnumGtisAccountType.Main);

                // 验证服务时间合理性（跳过字段验证，因为UserList模型中没有这些字段）
                // 这些验证将在具体的用户信息写入时进行
            }

            //补充未能匹配的数据
            apply.ApplicantId = UserId;
            apply.ApplicantDate = DateTime.Now;
            apply.State = EnumProcessStatus.Submit;
            apply.ProcessingType = EnumProcessingType.Change; // 变更为Change类型
            apply.IsInvalid = EnumIsInvalid.Effective;
            apply.IsGtisApply = updateApply_In.GtisApply != null;
            apply.IsGlobalSearchApply = updateApply_In.GlobalSearchApply != null;
            apply.IsCollegeApply = updateApply_In.CollegeApply != null;
            apply.IsSalesWitsApply = updateApply_In.SalesWitsApply != null;
            apply.Remark4List = updateApply_In.Remark;

            //保存变更原因 - JSON格式
            if (updateApply_In.ChangeReasonDetails != null && updateApply_In.ChangeReasonDetails.Count > 0)
            {
                // 将变更原因存储为JSON格式
                var changeReasonJson = new
                {
                    ChangeReasons = updateApply_In.ChangeReasonDetails.Select(r => new
                    {
                        ChangeReason = r.ChangeReason.ToInt(),
                        ContractProductId = r.ContractProductId
                    }).ToList()
                };
                apply.ChangeReasonEnums = System.Text.Json.JsonSerializer.Serialize(changeReasonJson);
            }
            else
            {
                throw new ApiException("请选择变更原因");
            }

            //如果使用了优惠券，转换优惠券参数并保存
            if (apply.DiscountType == EnumGtisDiscountType.Coupon && updateApply_In.CounponDetailIdList != null && updateApply_In.CounponDetailIdList.Count > 0)
                apply.CounponDetailIds = String.Join(',', updateApply_In.CounponDetailIdList);

            // 个人服务天数验证逻辑
            if (updateApply_In.PerlongServiceDays.GetValueOrDefault(0) > 0)
            {
                //获取销售经理当月可用个人服务时间
                var duration = BLL_PrivateService.Instance.GetDuration();
                if (updateApply_In.PrivateServiceDays.GetValueOrDefault(0) > duration.Duration)
                    throw new ApiException("个人服务天数超出当月可使用上限");

                //计算超额服务天数
                var overServiceDays = updateApply_In.PerlongServiceDays.GetValueOrDefault(0) - updateApply_In.PrivateServiceDays.GetValueOrDefault(0);

                //计算的超额服务天数大于当前人员可使用的超额服务天数上限，弹出异常
                if (overServiceDays > duration.ReleaseChargeDay)
                    throw new ApiException("超额服务天数超出当月可使用上限");

                //若计算的自费天数与传入的不符，弹出异常
                if (overServiceDays != updateApply_In.OverServiceDays.GetValueOrDefault(0))
                    throw new ApiException("超额服务天数与实际情况不符，请重新填写");

                // 检查服务中断时间（需要原有服务信息）
                if (!string.IsNullOrEmpty(updateApply_In.OriWitsServiceId))
                {
                    try
                    {
                        var currentServiceResponse = BLL_WitsService.Instance.GetWitsServiceInfoByServiceId(updateApply_In.OriWitsServiceId);
                        if (currentServiceResponse?.ServiceInfo != null)
                        {
                            // 检查服务结束时间，判断是否停服已超过6个月
                            var serviceEndDate = currentServiceResponse.ServiceInfo.ServiceCycleEnd;
                            if (serviceEndDate.HasValue && serviceEndDate.Value.AddMonths(6) < DateTime.Now.Date)
                                throw new ApiException("服务中断时间已超过6个月，无法使用个人服务天数");
                        }
                    }
                    catch (Exception ex)
                    {
                        // 获取原服务信息失败不影响个人服务天数验证，记录日志
                        LogUtil.AddLog($"获取原服务信息失败，无法验证服务中断时间: {ex.Message}");
                    }
                }
            }

            #region 计算账号的开通周期：所有服务的最早开始时间--所有服务的最晚结束时间
            var startDateList = new List<DateTime>(); var endDateList = new List<DateTime>();
            if (updateApply_In.GtisApply != null)
            {//如果申请包含Gits服务，取提交信息中的开始结束时间
                startDateList.Add(updateApply_In.GtisApply.ServiceCycleStartAfterDiscount.Value);
                endDateList.Add(updateApply_In.GtisApply.ServiceCycleEndAfterDiscount.Value);
            }
            else if (!string.IsNullOrEmpty(apply.RenewContractNum))
            {//如果未申请Gtis服务且当前合同是续约合同，则需要查找当前在服信息的开始结束时间
                var service = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetValidGtisServiceInfoByContractNum(apply.RenewContractNum);
                if (service != null)
                {
                    startDateList.Add(service.ServiceCycleStart.Value);
                    endDateList.Add(service.ServiceCycleEnd.Value);
                }
            }
            if (updateApply_In.GlobalSearchApply != null)
            {//如果申请包含环球搜服务，取提交信息中的开始结束时间
                startDateList.Add(updateApply_In.GlobalSearchApply.ServiceCycleStartAfterDiscount.Value);
                endDateList.Add(updateApply_In.GlobalSearchApply.ServiceCycleEndAfterDiscount.Value);
            }
            else if (!string.IsNullOrEmpty(apply.RenewContractNum))
            {//如果未申请环球搜服务且当前合同是续约合同，则需要查找当前在服信息的开始结束时间
                var service = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetValidGlobalSearchServiceByContractNum(apply.RenewContractNum);
                if (service != null)
                {
                    startDateList.Add(service.ServiceCycleStart.Value);
                    endDateList.Add(service.ServiceCycleEnd.Value);
                }
            }
            if (updateApply_In.CollegeApply != null)
            {//如果申请包含慧思学院服务，取提交信息中的开始结束时间
                startDateList.Add(updateApply_In.CollegeApply.ServiceCycleStartAfterDiscount.Value);
                endDateList.Add(updateApply_In.CollegeApply.ServiceCycleEndAfterDiscount.Value);
            }
            else if (!string.IsNullOrEmpty(apply.RenewContractNum))
            {//如果未申请慧思学院服务且当前合同是续约合同，则需要查找当前在服信息的开始结束时间
                var service = DbOpe_crm_contract_serviceinfo_college.Instance.GetValidColegeServiceByContractNum(apply.RenewContractNum);
                if (service != null)
                {
                    startDateList.Add(service.ServiceCycleStart.Value);
                    endDateList.Add(service.ServiceCycleEnd.Value);
                }
            }
            if (updateApply_In.SalesWitsApply != null)
            {//如果申请包含SalesWits服务，取提交信息中的开始结束时间
                startDateList.Add(updateApply_In.SalesWitsApply.ServiceCycleStartAfterDiscount.Value);
                endDateList.Add(updateApply_In.SalesWitsApply.ServiceCycleEndAfterDiscount.Value);
            }
            else if (!string.IsNullOrEmpty(apply.RenewContractNum))
            {//如果未申请SalesWits服务且当前合同是续约合同，则需要查找当前在服信息的开始结束时间
                //此处还未建表
            }
            #endregion
            apply.ServiceCycleStart = startDateList.Min();
            apply.ServiceCycleEnd = endDateList.Max();

            //插入申请数据，并记录返回的主键Id
            apply.Id = DbOpe_crm_contract_productserviceinfo_wits_appl.Instance.InsertDataReturnId(apply).ToString();

            //字段比对和存储变更内容
            if (!string.IsNullOrEmpty(updateApply_In.OriWitsServiceId))
            {
                try
                {
                    // 获取在服服务信息
                    var currentServiceResponse = BLL_WitsService.Instance.GetWitsServiceInfoByServiceId(updateApply_In.OriWitsServiceId);
                    if (currentServiceResponse != null && currentServiceResponse.ServiceInfo != null)
                    {
                        // 获取字段配置权限
                        var changeReasonEnums = updateApply_In.ChangeReasonDetails.Select(r => r.ChangeReason).Distinct().ToList();
                        var fieldConfigs = BLL_ServiceChangeFieldPermission.Instance.GetApplyFieldPermissions(changeReasonEnums);

                        // 进行字段比对并生成变更记录
                        var fieldRecords = new List<Db_crm_service_change_field_record>();

                        var currentService = currentServiceResponse.ServiceInfo;

                        // GTIS服务字段比对
                        if (updateApply_In.GtisApply != null && currentService.GtisService != null)
                        {
                            var gtisRecords = BLL_ServiceChangeFieldPermission.Instance.CompareGtisFieldRecords(
                                apply.Id,
                                currentService.GtisService,
                                updateApply_In.GtisApply,
                                changeReasonEnums.Select(c => c.ToInt()).ToList(),
                                UserId
                            );
                            fieldRecords.AddRange(gtisRecords);
                        }

                        // GlobalSearch服务字段比对
                        if (updateApply_In.GlobalSearchApply != null && currentService.GlobalSearchService != null)
                        {
                            var globalSearchRecords = BLL_ServiceChangeFieldPermission.Instance.CompareGlobalSearchFieldRecords(
                                apply.Id,
                                currentService.GlobalSearchService,
                                updateApply_In.GlobalSearchApply,
                                changeReasonEnums.Select(c => c.ToInt()).ToList(),
                                UserId
                            );
                            fieldRecords.AddRange(globalSearchRecords);
                        }

                        // College服务字段比对
                        if (updateApply_In.CollegeApply != null && currentService.CollegeService != null)
                        {
                            var collegeRecords = BLL_ServiceChangeFieldPermission.Instance.CompareCollegeFieldRecords(
                                apply.Id,
                                currentService.CollegeService,
                                updateApply_In.CollegeApply,
                                changeReasonEnums.Select(c => c.ToInt()).ToList(),
                                UserId
                            );
                            fieldRecords.AddRange(collegeRecords);
                        }

                        // SalesWits服务字段比对
                        if (updateApply_In.SalesWitsApply != null && currentService.SalesWitsService != null)
                        {
                            var salesWitsRecords = BLL_ServiceChangeFieldPermission.Instance.CompareSalesWitsFieldRecords(
                                apply.Id,
                                currentService.SalesWitsService,
                                updateApply_In.SalesWitsApply,
                                changeReasonEnums.Select(c => c.ToInt()).ToList(),
                                UserId
                            );
                            fieldRecords.AddRange(salesWitsRecords);
                        }

                        // 通用字段比对
                        var commonRecords = BLL_ServiceChangeFieldPermission.Instance.CompareAndGenerateFieldRecords(
                            apply.Id,
                            currentService,
                            updateApply_In,
                            5, // 通用字段
                            changeReasonEnums.Select(c => c.ToInt()).ToList(),
                            UserId
                        );
                        fieldRecords.AddRange(commonRecords);

                        // 保存字段变更记录
                        if (fieldRecords.Count > 0)
                        {
                            DbOpe_crm_service_change_field_record.Instance.SaveFieldRecords(fieldRecords, apply.Id, UserId);
                        }
                    }
                }
                catch (Exception ex)
                {
                    // 字段比对失败不影响主流程，记录日志即可
                    LogUtil.AddLog($"服务变更字段比对失败: {ex.Message}");
                }
            }

            //记录个人服务天数使用情况
            if (apply.PerlongServiceDays.GetValueOrDefault(0) > 0)
            {
                //登记使用个人使用时间,将对应天数改为使用中
                BLL_PrivateService.Instance.UsingPrivateServiceForContractService(apply.ContractId, EnumPrivateServiceType.Using, apply.Id, apply.PrivateServiceDays.GetValueOrDefault(0), apply.OverServiceDays.GetValueOrDefault(0));
            }

            #region 写入各个服务的申请数据
            if (updateApply_In.GtisApply != null)//写入Gtis申请数据
                UpdateGtisAppl(updateApply_In.GtisApply, apply);
            if (updateApply_In.GlobalSearchApply != null)//写入环球搜申请数据
                UpdateGlobalSearchAppl(updateApply_In.GlobalSearchApply, apply);
            if (updateApply_In.CollegeApply != null)//写入慧思学院申请数据
                UpdateCollegeAppl(updateApply_In.CollegeApply, apply);
            if (updateApply_In.SalesWitsApply != null) //写入SalesWits申请数据
                UpdateSalesWitsAppl(updateApply_In.SalesWitsApply, apply);
            #endregion

            #region 写入账号信息
            if (updateApply_In.UserList != null && updateApply_In.UserList.Count > 0)
            {
                updateApply_In.UserList.ForEach(user =>
                {
                    var userData = user.MappingTo<Db_crm_contract_productserviceinfo_wits_appl_user>();
                    userData.WitsApplId = apply.Id;
                    userData.StartDate = startDateList.Min();
                    userData.EndDate = endDateList.Max();
                    userData.SalesWitsPhoneId = user.SalesWitsBindPhoneId;
                    DbOpe_crm_contract_productserviceinfo_wits_appl_user.Instance.InsertData(userData);
                });
            }
            #endregion

            // 更新合同产品服务状态
            DbOpe_crm_contract_product_serviceinfo_status.Instance.UpdateContractProductServiceInfoStatus(contract.Id);

            // 更新相关合同产品申请状态为待审核
            ContractProductApplStateHelper.UpdateWitsContractProductApplState(
                apply.Id,
                EnumProcessStatus.Submit,
                UserId,
                updateApply_In.ChangeReasonDetails,
                "");


            //记录流转
            string dataState = Dictionary.ProcessStatus.First(e => e.Value == apply.State.ToInt().ToString()).Name;
            BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_productserviceinfo_wits_appl, ContractBasicInfo_Out>("慧思产品(Gtis系列)服务审批流程", apply.Id, apply, contract, updateApply_In.Remark, "待变更", "申请");//dataState, "申请");


            return apply;
        }

        #region 补充各个单项服务申请数据
        /// <summary>
        /// 补充Gits申请表数据
        /// </summary>
        /// <param name="gtis"></param>
        /// <param name="apply"></param>
        private void AddGtisAppl(AddGtisSeriesAppl_In_Gtis gtis, Db_crm_contract_productserviceinfo_wits_appl apply)
        {
            //将当前合同的旧服务申请(被拒绝)置位失效
            DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.InvalidOldApply(gtis.ContractProductInfoId);
            //根据传入参数匹配声明申请数据
            var gtis_appl = gtis.MappingTo<Db_crm_contract_productserviceinfo_gtis_appl>();
            apply.MappingTo(gtis_appl);
            //补充未能匹配的数据
            gtis_appl.PrimaryAccountsNum = 1;
            gtis_appl.WitsApplId = apply.Id;
            gtis_appl.ContractProductInfoSeriesId = apply.ContractProductInfoSeriesId;
            gtis_appl.AuthorizationNum = apply.SuperSubAccountNum == 0 ? 1 : apply.SuperSubAccountNum;

            /* 观察一下，没问题再删
            gtis_appl.IsAutoGeneration = true;
            gtis_appl.ContractId = apply.ContractId;
            gtis_appl.ApplicantId = apply.ApplicantId;
            gtis_appl.ApplicantDate = apply.ApplicantDate;
            gtis_appl.State = apply.State.ToInt();
            gtis_appl.ProcessingType = apply.ProcessingType.ToInt();
            gtis_appl.IsInvalid = apply.IsInvalid.ToInt();
            gtis_appl.Remark4List = apply.Remark4List;
            gtis_appl.RenewContractNum = apply.RenewContractNum;
            gtis_appl.RenewFirstParty = apply.RenewFirstParty;
            gtis_appl.AccountGenerationMethod = apply.AccountGenerationMethod;*/


            //插入Gtis申请数据，并记录返回的主键Id
            gtis_appl.Id = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.InsertDataReturnId(gtis_appl).ToString();
            //记录Gtis开通国家
            List<Db_crm_contract_productserviceinfo_gtis_appl_country> CountryList = new List<Db_crm_contract_productserviceinfo_gtis_appl_country>();
            foreach (GtisApplCountry g in gtis.GtisApplCountry)
            {
                Db_crm_contract_productserviceinfo_gtis_appl_country Gtis_Appl_Country = new Db_crm_contract_productserviceinfo_gtis_appl_country();
                Gtis_Appl_Country.Sid = g.Sid;
                Gtis_Appl_Country.ProductServiceInfoGtisApplId = gtis_appl.Id;
                CountryList.Add(Gtis_Appl_Country);
            }
            DbOpe_crm_contract_productserviceinfo_gtis_appl_country.Instance.InsertListData(CountryList);
        }

        /// <summary>
        /// 补充Gits变更申请表数据
        /// </summary>
        /// <param name="gtis"></param>
        /// <param name="apply"></param>
        private void UpdateGtisAppl(VM_ContractServiceChange.UpdateGtisSeriesAppl_In_Gtis gtis, Db_crm_contract_productserviceinfo_wits_appl apply)
        {
            // 申请验证
            DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.CheckAppl(gtis.ContractProductInfoId, true);

            //将当前合同的旧服务申请(被拒绝)置位失效
            DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.InvalidOldApply(gtis.ContractProductInfoId);
            //根据传入参数匹配声明申请数据
            var gtisAppl = gtis.MappingTo<Db_crm_contract_productserviceinfo_gtis_appl>();
            apply.MappingTo(gtisAppl);
            // 备注：用户数据聚合计算需要从UserList中获取，但apply.UserList不存在
            // 这些计算应该从updateApply_In.UserList中进行，并在后续用户信息写入时处理

            // 服务月数计算
            if (gtisAppl.ServiceAddMonth.HasValue)
            {
                // 从原有服务获取基础服务月数，然后加上新增月数
                var originalServiceMonths = 0; // 这里需要从原服务获取，暂时设为0
                gtisAppl.ServiceMonth = originalServiceMonths + gtisAppl.ServiceAddMonth.Value;
            }

            //补充未能匹配的数据
            gtisAppl.PrimaryAccountsNum = 1;
            gtisAppl.WitsApplId = apply.Id;
            gtisAppl.ApplicantId = UserId;
            gtisAppl.ApplicantDate = DateTime.Now;
            gtisAppl.State = (int?)EnumProcessStatus.Submit;
            gtisAppl.ProcessingType = (int?)EnumProcessingType.Change; // 变更为Change类型
            gtisAppl.IsInvalid = (int?)EnumIsInvalid.Effective;
            gtisAppl.Remark4List = apply.Remark;
            //如果使用了优惠券，转换优惠券参数并保存
            if (apply.DiscountType == EnumGtisDiscountType.Coupon && apply.CounponDetailIds != null && apply.CounponDetailIds.Length > 0)
                gtisAppl.CouponIds = apply.CounponDetailIds;
            //插入申请数据，并记录返回的主键Id
            gtisAppl.Id = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.InsertDataReturnId(gtisAppl).ToString();
            //写入国家信息
            if (gtis.GtisApplCountry != null && gtis.GtisApplCountry.Count > 0)
            {
                gtis.GtisApplCountry.ForEach(country =>
                {
                    var countryData = country.MappingTo<Db_crm_contract_productserviceinfo_gtis_appl_country>();
                    countryData.ProductServiceInfoGtisApplId = gtisAppl.Id;
                    DbOpe_crm_contract_productserviceinfo_gtis_appl_country.Instance.InsertData(countryData);
                });
            }
        }

        /// <summary>
        /// 补充环球搜申请表数据
        /// </summary>
        /// <param name="globalsearch"></param>
        /// <param name="apply"></param>
        private void AddGlobalSearchAppl(AddGtisSeriesAppl_In_GlobalSearch globalsearch, Db_crm_contract_productserviceinfo_wits_appl apply)
        {
            //将当前合同的旧服务申请(被拒绝)置位失效
            DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.InvalidOldApply(globalsearch.ContractProductInfoId);
            //根据传入参数匹配声明申请数据
            var globalsearch_appl = globalsearch.MappingTo<Db_crm_contract_productserviceinfo_globalsearch_appl>();
            apply.MappingTo(globalsearch_appl);
            //补充未能匹配的数据
            globalsearch_appl.PrimaryAccountsNum = 1;
            globalsearch_appl.SubAccountsNum = globalsearch.AccountCount - 1;
            globalsearch_appl.IsGtisAutoGeneration = true;
            globalsearch_appl.WitsApplId = apply.Id;
            globalsearch_appl.ExecuteServiceCycleStart = globalsearch.ServiceCycleStartAfterDiscount;
            globalsearch_appl.ExecuteServiceCycleEnd = globalsearch.ServiceCycleEndAfterDiscount;
            globalsearch_appl.ExecuteServiceMonth = globalsearch.ServiceMonthAfterDiscount;
            /* 观察一下，没问题再删
            globalsearch_appl.ContractId = apply.ContractId;
            globalsearch_appl.ApplicantId = apply.ApplicantId;
            globalsearch_appl.ApplicantDate = apply.ApplicantDate;
            globalsearch_appl.State = apply.State.ToInt();
            globalsearch_appl.ProcessingType = apply.ProcessingType.ToInt();
            globalsearch_appl.IsInvalid = apply.IsInvalid.ToInt();
            globalsearch_appl.Remark4List = apply.Remark4List;
            globalsearch_appl.RenewContractNum = apply.RenewContractNum;
            globalsearch_appl.ContractProductInfoSeriesId = apply.ContractProductInfoSeriesId;
            globalsearch_appl.AccountGenerationMethod = apply.AccountGenerationMethod;*/
            //插入环球搜申请数据
            DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.InsertData(globalsearch_appl);
        }

        /// <summary>
        /// 补充环球搜变更申请表数据
        /// </summary>
        /// <param name="globalsearch"></param>
        /// <param name="apply"></param>
        private void UpdateGlobalSearchAppl(VM_ContractServiceChange.UpdateGtisSeriesAppl_In_GlobalSearch globalsearch, Db_crm_contract_productserviceinfo_wits_appl apply)
        {
            // 申请验证
            DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.CheckAppl(globalsearch.ContractProductInfoId, "", true);

            //将当前合同的旧服务申请(被拒绝)置位失效
            DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.InvalidOldApply(globalsearch.ContractProductInfoId);
            //根据传入参数匹配声明申请数据
            var globalsearchAppl = globalsearch.MappingTo<Db_crm_contract_productserviceinfo_globalsearch_appl>();
            apply.MappingTo(globalsearchAppl);

            // 服务月数计算（处理ServiceAddMonth字段）
            if (globalsearch.ServiceAddMonth.HasValue)
            {
                // 从原有服务获取基础服务月数，然后加上新增月数
                var originalServiceMonths = globalsearchAppl.ServiceMonth.GetValueOrDefault(0);
                globalsearchAppl.ServiceMonth = originalServiceMonths + globalsearch.ServiceAddMonth.Value;
            }

            //补充未能匹配的数据
            globalsearchAppl.PrimaryAccountsNum = 1;
            globalsearchAppl.SubAccountsNum = globalsearch.AccountCount - 1;
            globalsearchAppl.WitsApplId = apply.Id;
            globalsearchAppl.ApplicantId = UserId;
            globalsearchAppl.ApplicantDate = DateTime.Now;
            globalsearchAppl.State = EnumProcessStatus.Submit.ToInt();
            globalsearchAppl.ProcessingType = EnumProcessingType.Change.ToInt(); // 变更为Change类型
            globalsearchAppl.IsInvalid = EnumIsInvalid.Effective.ToInt();
            globalsearchAppl.Remark4List = apply.Remark;
            globalsearchAppl.ExecuteServiceCycleStart = globalsearch.ServiceCycleStartAfterDiscount;
            globalsearchAppl.ExecuteServiceCycleEnd = globalsearch.ServiceCycleEndAfterDiscount;
            globalsearchAppl.ExecuteServiceMonth = globalsearch.ServiceMonthAfterDiscount;
            //插入申请数据，并记录返回的主键Id
            globalsearchAppl.Id = DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.InsertDataReturnId(globalsearchAppl).ToString();
        }

        /// <summary>
        /// 补充慧思学院申请表数据
        /// </summary>
        /// <param name="college"></param>
        /// <param name="apply"></param>
        private void AddCollegeAppl(AddGtisSeriesAppl_In_College college, Db_crm_contract_productserviceinfo_wits_appl apply)
        {
            //将当前合同的旧服务申请(被拒绝)置位失效
            DbOpe_crm_contract_productserviceinfo_college_appl.Instance.InvalidOldApply(college.ContractProductInfoId);
            //根据传入参数匹配声明申请数据
            var college_appl = college.MappingTo<Db_crm_contract_productserviceinfo_college_appl>();
            apply.MappingTo(college_appl);
            //补充未能匹配的数据
            college_appl.PrimaryAccountsNum = college.AccountCount;
            college_appl.IsAutoGeneration = true;
            college_appl.WitsApplId = apply.Id;

            /* 观察一下，没问题再删
            college_appl.ContractId = apply.ContractId;
            college_appl.ApplicantId = apply.ApplicantId;
            college_appl.ApplicantDate = apply.ApplicantDate;
            college_appl.State = apply.State.ToInt();
            college_appl.ProcessingType = apply.ProcessingType.ToInt();
            college_appl.IsInvalid = apply.IsInvalid.ToInt();
            college_appl.ContractProductInfoSeriesId = apply.ContractProductInfoSeriesId;
            college_appl.Remark = apply.Remark;
            college_appl.AccountGenerationMethod = apply.AccountGenerationMethod;*/
            //插入慧思学院申请数据
            DbOpe_crm_contract_productserviceinfo_college_appl.Instance.InsertData(college_appl);
        }

        /// <summary>
        /// 补充慧思学院变更申请表数据
        /// </summary>
        /// <param name="college"></param>
        /// <param name="apply"></param>
        private void UpdateCollegeAppl(VM_ContractServiceChange.UpdateGtisSeriesAppl_In_College college, Db_crm_contract_productserviceinfo_wits_appl apply)
        {
            //将当前合同的旧服务申请(被拒绝)置位失效
            DbOpe_crm_contract_productserviceinfo_college_appl.Instance.InvalidOldApply(college.ContractProductInfoId);
            //根据传入参数匹配声明申请数据
            var collegeAppl = college.MappingTo<Db_crm_contract_productserviceinfo_college_appl>();
            apply.MappingTo(collegeAppl);

            // 服务月数计算（处理ServiceAddMonth字段）
            if (college.ServiceAddMonth.HasValue)
            {
                // 从原有服务获取基础服务月数，然后加上新增月数
                var originalServiceMonths = collegeAppl.ServiceMonth.GetValueOrDefault(0);
                collegeAppl.ServiceMonth = originalServiceMonths + college.ServiceAddMonth.Value;
            }

            //补充未能匹配的数据
            collegeAppl.PrimaryAccountsNum = college.AccountCount;
            collegeAppl.WitsApplId = apply.Id;
            collegeAppl.ApplicantId = UserId;
            collegeAppl.ApplicantDate = DateTime.Now;
            collegeAppl.State = (int?)EnumProcessStatus.Submit;
            collegeAppl.ProcessingType = (int?)EnumProcessingType.Change; // 变更为Change类型
            collegeAppl.IsInvalid = (int?)EnumIsInvalid.Effective;
            collegeAppl.Remark = apply.Remark;
            //插入申请数据，并记录返回的主键Id
            collegeAppl.Id = DbOpe_crm_contract_productserviceinfo_college_appl.Instance.InsertDataReturnId(collegeAppl).ToString();
        }

        /// <summary>
        /// 补充SalesWits申请表数据
        /// </summary>
        /// <param name="saleswits"></param>
        /// <param name="apply"></param>
        private void AddSalesWitsAppl(AddGtisSeriesAppl_In_SalesWits saleswits, Db_crm_contract_productserviceinfo_wits_appl apply)
        {
            //将当前合同的旧服务申请(被拒绝)置位失效
            DbOpe_crm_contract_productserviceinfo_saleswits_appl.Instance.InvalidOldApply(saleswits.ContractProductInfoId);
            //根据传入参数匹配声明申请数据
            var saleswits_appl = saleswits.MappingTo<Db_crm_contract_productserviceinfo_saleswits_appl>();
            apply.MappingTo(saleswits_appl);
            //补充未能匹配的数据
            saleswits_appl.AccountsNum = saleswits.AccountCount;
            saleswits_appl.IsAutoGeneration = true;
            saleswits_appl.WitsApplId = apply.Id;
            saleswits_appl.GiftResourceMonths = 12;
            saleswits_appl.EmailCount = DbOpe_sys_systemparameter.Instance.GetValueByKey("EmailCount").ToInt();
            saleswits_appl.TokenCount = DbOpe_sys_systemparameter.Instance.GetValueByKey("TokenCount").ToInt();

            /* 观察一下，没问题再删
            saleswits_appl.ContractId = apply.ContractId;
            saleswits_appl.ApplicantId = apply.ApplicantId;
            saleswits_appl.ApplicantDate = apply.ApplicantDate;
            saleswits_appl.State = apply.State.ToInt();
            saleswits_appl.ProcessingType = apply.ProcessingType.ToInt();
            saleswits_appl.IsInvalid = apply.IsInvalid.ToInt();
            saleswits_appl.ContractProductInfoSeriesId = apply.ContractProductInfoSeriesId;
            saleswits_appl.Remark = apply.Remark;
            saleswits_appl.AccountGenerationMethod = apply.AccountGenerationMethod;*/
            //插入SalesWits申请数据
            DbOpe_crm_contract_productserviceinfo_saleswits_appl.Instance.InsertData(saleswits_appl);
        }

        /// <summary>
        /// 补充SalesWits变更申请表数据
        /// </summary>
        /// <param name="saleswits"></param>
        /// <param name="apply"></param>
        private void UpdateSalesWitsAppl(VM_ContractServiceChange.UpdateGtisSeriesAppl_In_SalesWits saleswits, Db_crm_contract_productserviceinfo_wits_appl apply)
        {

            //将当前合同的旧服务申请(被拒绝)置位失效
            DbOpe_crm_contract_productserviceinfo_saleswits_appl.Instance.InvalidOldApply(saleswits.ContractProductInfoId);
            //根据传入参数匹配声明申请数据
            var saleswitsAppl = saleswits.MappingTo<Db_crm_contract_productserviceinfo_saleswits_appl>();
            apply.MappingTo(saleswitsAppl);

            // 服务月数计算（处理ServiceAddMonth字段）
            if (saleswits.ServiceAddMonth.HasValue)
            {
                // 从原有服务获取基础服务月数，然后加上新增月数
                var originalServiceMonths = saleswitsAppl.ServiceMonth.GetValueOrDefault(0);
                saleswitsAppl.ServiceMonth = originalServiceMonths + saleswits.ServiceAddMonth.Value;
            }

            // 检查是否有增加SalesWits子账号的变更原因，计算补发资源（支持产品级别检查）
            if (HasProductSpecificChangeReason(apply.ChangeReasonEnums, saleswits.ContractProductInfoId, EnumGtisServiceChangeProject.SalesWitsAddAccount))
            {
                LogUtil.AddLog($"检测到增加SalesWits子账号变更，开始计算补发资源");
                CalculateAndSetSupplementaryResources(saleswits, saleswitsAppl, apply);
            }

            //补充未能匹配的数据
            saleswitsAppl.AccountsNum = saleswits.AccountCount;
            saleswitsAppl.WitsApplId = apply.Id;
            saleswitsAppl.ApplicantId = UserId;
            saleswitsAppl.ApplicantDate = DateTime.Now;
            saleswitsAppl.State = (int?)EnumProcessStatus.Submit;
            saleswitsAppl.ProcessingType = (int?)EnumProcessingType.Change; // 变更为Change类型
            saleswitsAppl.IsInvalid = (int?)EnumIsInvalid.Effective;
            saleswitsAppl.Remark = apply.Remark;
            //插入申请数据，并记录返回的主键Id
            saleswitsAppl.Id = DbOpe_crm_contract_productserviceinfo_saleswits_appl.Instance.InsertDataReturnId(saleswitsAppl).ToString();
        }

        /// <summary>
        /// 计算并设置增加子账号的补发资源
        /// </summary>
        /// <param name="saleswits">SalesWits变更申请输入</param>
        /// <param name="saleswitsAppl">SalesWits申请数据</param>
        /// <param name="apply">主申请数据，包含原服务ID</param>
        private void CalculateAndSetSupplementaryResources(VM_ContractServiceChange.UpdateGtisSeriesAppl_In_SalesWits saleswits, Db_crm_contract_productserviceinfo_saleswits_appl saleswitsAppl, Db_crm_contract_productserviceinfo_wits_appl apply)
        {
            try
            {
                // 检查原Wits服务ID
                if (string.IsNullOrEmpty(apply.OriWitsServiceId))
                {
                    LogUtil.AddLog("OriWitsServiceId为空，无法获取被变更的Wits服务信息");
                    return;
                }

                LogUtil.AddLog($"开始检查原Wits服务{apply.OriWitsServiceId}是否开通了SalesWits");

                // 根据原Wits服务ID直接查找Wits服务
                var originalWitsService = DbOpe_crm_contract_serviceinfo_wits.Instance
                    .GetData(x => x.Id == apply.OriWitsServiceId);
                
                if (originalWitsService == null)
                {
                    LogUtil.AddLog($"未找到原Wits服务，服务ID: {apply.OriWitsServiceId}");
                    return;
                }

                // 检查原Wits服务是否有SalesWits
                if (string.IsNullOrEmpty(originalWitsService.CurrentSalesWitsServiceId))
                {
                    LogUtil.AddLog($"原Wits服务{originalWitsService.Id}未开通SalesWits，无需计算补发资源");
                    return;
                }

                // 根据CurrentSalesWitsServiceId获取SalesWits服务详情
                var currentSalesWitsService = DbOpe_crm_contract_serviceinfo_saleswits.Instance
                    .GetData(x => x.Id == originalWitsService.CurrentSalesWitsServiceId);

                if (currentSalesWitsService == null)
                {
                    LogUtil.AddLog($"未找到SalesWits服务{originalWitsService.CurrentSalesWitsServiceId}");
                    return;
                }

                LogUtil.AddLog($"找到原SalesWits服务，ID: {currentSalesWitsService.Id}，当前账号数: {currentSalesWitsService.AccountsNum}");

                // 使用服务开通BLL获取下次下发时间
                var serviceOpeningBll = new ServiceOpening.BLL_ServiceOpening();
                var nextDistributionDate = serviceOpeningBll.GetNextDistributionDate(currentSalesWitsService.Id);

                if (!nextDistributionDate.HasValue)
                {
                    throw new ApiException($"无法获取SalesWits服务{currentSalesWitsService.Id}的下次下发时间");
                }

                // 计算补发月数：从增项合同签约时间到下次定期下发时间
                // 通过apply.ContractId获取合同的签约时间
                var contract = DbOpe_crm_contract.Instance.GetData(x => x.Id == apply.ContractId);
                if (contract == null)
                {
                    throw new ApiException($"未找到合同{apply.ContractId}");
                }
                
                var contractSignDate = contract.SigningDate?.Date ?? DateTime.Now.Date;
                var remainingMonths = CalculateRemainingMonths(contractSignDate, nextDistributionDate.Value);
                
                LogUtil.AddLog($"增项合同签约时间: {contractSignDate:yyyy-MM-dd}, 下次下发时间: {nextDistributionDate.Value:yyyy-MM-dd}, 补发月数: {remainingMonths}");

                if (remainingMonths <= 0)
                {
                    LogUtil.AddLog($"剩余月数{remainingMonths}不大于0，无需补发资源");
                    return;
                }

                // 计算新增账号数
                var currentAccountCount = currentSalesWitsService.AccountsNum ?? 0;
                var newAccountCount = saleswits.AccountCount ?? 0;
                var addedAccountCount = newAccountCount - currentAccountCount;

                if (addedAccountCount <= 0)
                {
                    LogUtil.AddLog($"新增账号数{addedAccountCount}不大于0，无需补发资源");
                    return;
                }

                var defaultEmailCount = DbOpe_sys_systemparameter.Instance.GetValueByKey("EmailCount");
                var defaultTokenCount = DbOpe_sys_systemparameter.Instance.GetValueByKey("TokenCount");
                if(string.IsNullOrEmpty(defaultEmailCount) || string.IsNullOrEmpty(defaultTokenCount)
                || !int.TryParse(defaultEmailCount, out var defaultEmailCountInt)
                || !int.TryParse(defaultTokenCount, out var defaultTokenCountInt))
                {
                    throw new CRM2_API.Model.System.ApiException("系统参数配置异常");
                }

                // 计算补发资源：新增账号数 × 每月标准 × 剩余月数
                var supplementaryEmailCount = addedAccountCount * defaultEmailCountInt * remainingMonths;
                var supplementaryTokenCount = addedAccountCount * defaultTokenCountInt * remainingMonths; // 10万Token，存储为万个单位

                // 设置补发资源到申请表中
                saleswitsAppl.GiftResourceMonths = remainingMonths;
                saleswitsAppl.EmailCount = supplementaryEmailCount;
                saleswitsAppl.TokenCount = supplementaryTokenCount;

                LogUtil.AddLog($"SalesWits子账号补发资源计算完成: " +
                    $"新增账号{addedAccountCount}个, 剩余月数{remainingMonths}个月, " +
                    $"补发邮件{supplementaryEmailCount}封, 补发Token{supplementaryTokenCount}万个");
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"计算SalesWits补发资源异常: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 计算从当前时间到目标时间的剩余月数
        /// </summary>
        /// <param name="currentDate">当前日期</param>
        /// <param name="targetDate">目标日期</param>
        /// <returns>剩余月数</returns>
        private int CalculateRemainingMonths(DateTime currentDate, DateTime targetDate)
        {
            if (targetDate <= currentDate)
            {
                return 0;
            }

            var yearDiff = targetDate.Year - currentDate.Year;
            var monthDiff = targetDate.Month - currentDate.Month;
            var totalMonths = yearDiff * 12 + monthDiff;

            // 如果目标日期的天数小于当前日期的天数，减去一个月
            if (targetDate.Day < currentDate.Day)
            {
                totalMonths--;
            }

            return Math.Max(0, totalMonths);
        }

        #endregion

        /// <summary>
        /// 初审/复核时，获取Wits产品的详细信息
        /// </summary>
        /// <param name="applId"></param>
        /// <returns></returns>
        public GetWitsApplyInfo4Audit_Out GetWitsApplyInfo4Audit(string applId)
        {
            var retValue = new GetWitsApplyInfo4Audit_Out();
            //申请表数据
            var apply = DbOpe_crm_contract_productserviceinfo_wits_appl.Instance.QueryByPrimaryKey(applId);
            //获取合同详细信息
            retValue.ContractInfo = DbOpe_crm_contract.Instance.GetContractAndReceiptByContractId(apply.ContractId);
            #region 产品信息
            var productInfoList = DbOpe_crm_contract_productinfo.Instance.GetContractProductInfoBySeiresId(apply.ContractProductInfoSeriesId);
            retValue.ProductInfo = productInfoList.MappingTo<List<GetWitsApplyInfo4Audit_Out_ProductInfo>>();
            //查看是否单独购买了超级子账号
            List<ProductInfo_Out> SuperSubAccountList = DbOpe_crm_product_supersubaccount.Instance.GetProductSuperSubAccountByContractId(apply.ContractId);
            if (SuperSubAccountList.Count > 0)
                retValue.ProductInfo.Add(SuperSubAccountList.First().MappingTo<GetWitsApplyInfo4Audit_Out_ProductInfo>());
            #endregion
            //申请信息
            retValue.ApplyInfo = DbOpe_crm_contract_productserviceinfo_wits_appl.Instance.GetWitsApplyBasicInfo(applId);
            // 解析服务变更原因
            if (!string.IsNullOrEmpty(apply.ChangeReasonEnums))
            {
                try
                {
                    // 尝试解析JSON格式
                    var changeReasonData = System.Text.Json.JsonSerializer.Deserialize<System.Text.Json.JsonElement>(apply.ChangeReasonEnums);
                    if (changeReasonData.ValueKind != System.Text.Json.JsonValueKind.Null &&
                        changeReasonData.TryGetProperty("ChangeReasons", out var changeReasonsElement) &&
                        changeReasonsElement.ValueKind == System.Text.Json.JsonValueKind.Array &&
                        changeReasonsElement.GetArrayLength() > 0)
                    {
                        foreach (var reason in changeReasonsElement.EnumerateArray())
                        {
                            if (reason.TryGetProperty("ChangeReason", out var changeReasonElement))
                            {
                                var changeReason = changeReasonElement.GetInt32();
                                var changeReasonEnum = changeReason.ToEnum<EnumGtisServiceChangeProject>();
                                retValue.ApplyInfo.ChangeReasons.Add(changeReasonEnum);
                                retValue.ApplyInfo.ChangeReasonNames.Add(changeReasonEnum.GetEnumDescription());
                            }
                        }
                    }
                }
                catch (System.Text.Json.JsonException)
                {
                    // JSON解析失败，尝试解析旧格式（兼容性处理）
                    var changeReasons = apply.ChangeReasonEnums.Split(',').Select(e => int.Parse(e)).ToList();
                    foreach (var changeReason in changeReasons)
                    {
                        var changeReasonEnum = changeReason.ToEnum<EnumGtisServiceChangeProject>();
                        retValue.ApplyInfo.ChangeReasons.Add(changeReasonEnum);
                        retValue.ApplyInfo.ChangeReasonNames.Add(changeReasonEnum.GetEnumDescription());
                    }
                }
            }
            //初审登记信息
            retValue.RegisterInfo = DbOpe_crm_contract_serviceinfo_wits.Instance.GetWitsServiceBasicInfo(applId);
            //审核备注与复核反馈列表
            retValue.ReviewFeedbacks = DbOpe_crm_contract_serviceinfo_wits.Instance.GetReviewFeedbacks(applId);
            //合同的所有到账信息
            retValue.ReceiptRegisterCollectionList = DbOpe_crm_contract_receipt_details.Instance.GetHistoryCollectionInfoItemsByContractReceiptRegisterId(apply.ContractId, String.Empty, EnumProductType.WitsProducts, retValue.RegisterInfo == null ? null : retValue.RegisterInfo.Id);
            //在服（原有账号）信息   （得看是否通过，通过了显示当前的，未通过显示原有的）
            if(apply.State == EnumProcessStatus.Pass)
            {
                var resService = BLL_WitsService.Instance.GetWitsServiceInfoByServiceId(retValue.RegisterInfo.Id); 
                retValue.WitsServiceInfo = resService.ServiceInfo;
            }
            else if(apply != null && apply.OriWitsServiceId != null)
            {
                var resService = BLL_WitsService.Instance.GetWitsServiceInfoByServiceId(apply.OriWitsServiceId); 
                retValue.WitsServiceInfo = resService.ServiceInfo;
            }
            //服务变更字段权限
            if(apply.ProcessingType == EnumProcessingType.Change)
            {
                var request = new ServiceChangeFieldPermissionsRequest();
                request.ContractProductIds = productInfoList.Select(e => e.Id).ToList();
                var changeReasons = new List<int>();
                try
                {
                    // 尝试解析JSON格式
                    var changeReasonData = System.Text.Json.JsonSerializer.Deserialize<System.Text.Json.JsonElement>(apply.ChangeReasonEnums);
                    if (changeReasonData.ValueKind != System.Text.Json.JsonValueKind.Null && 
                        changeReasonData.TryGetProperty("ChangeReasons", out var changeReasonsElement) &&
                        changeReasonsElement.ValueKind == System.Text.Json.JsonValueKind.Array &&
                        changeReasonsElement.GetArrayLength() > 0)
                    {
                        foreach (var reason in changeReasonsElement.EnumerateArray())
                        {
                            if (reason.TryGetProperty("ChangeReason", out var changeReasonElement))
                            {
                                var changeReason = changeReasonElement.GetInt32();
                                changeReasons.Add(changeReason);
                            }
                        }
                    }
                }
                catch (System.Text.Json.JsonException)
                {
                    // JSON解析失败，尝试解析旧格式（兼容性处理）
                    changeReasons = apply.ChangeReasonEnums.Split(',').Select(e => int.Parse(e)).ToList();
                }
                request.ChangeReasons = changeReasons;
                request.WitsApplyId = applId;
                var response = BLL_ServiceChangeFieldPermission.Instance.GetAuditFieldPermissions(request);
                retValue.FieldPermissions = response.FieldPermissions;
            }
            //返回
            return retValue;
        }

        /// <summary>
        /// 慧思产品初审
        /// </summary>
        /// <param name="audit_In"></param>
        public void AuditWitsAppl(AuditWitsAppl_In audit_In)
        {
            DbOpe_crm_contract_productserviceinfo_wits_appl.Instance.TransDeal(() =>
            {
                DateTime today = DateTime.Now;
                //获取服务申请信息
                var applData = DbOpe_crm_contract_productserviceinfo_wits_appl.Instance.QueryByPrimaryKey(audit_In.ApplyId);
                if (applData == null || applData.Deleted == true)
                    throw new ApiException("未找到对应服务产品申请信息");
                if (applData.State != EnumProcessStatus.Submit)
                    throw new ApiException("不能对服务产品申请信息重复审核");
                if (applData.IsInvalid != (int)EnumIsInvalid.Effective)
                    throw new ApiException("已失效的审核无法进行操作");
                //获取合同信息
                /*var contractInfo = DbOpe_crm_contract.Instance.GetContractAndReceiptByContractId(applData.ContractId);*/
                var contractInfo = DbOpe_crm_contract.Instance.QueryByPrimaryKey(applData.ContractId);
                if (contractInfo == null)
                    throw new ApiException("未找到合同信息");
                //获取登记信息  (如果是驳回的这里应该有历史信息，初审的没有)
                var hisServeInfo = DbOpe_crm_contract_serviceinfo_wits.Instance.GetRegisterInfoByApplId(audit_In.ApplyId);
                //如果有上一条登记审核信息 将之前驳回的申请置为历史
                if (hisServeInfo != null)
                {
                    if (hisServeInfo.State != EnumContractServiceState.RETURNED && hisServeInfo.State != EnumContractServiceState.TO_BE_OPENED)
                        throw new ApiException("未找到待开通/被驳回的服务产品");
                    hisServeInfo.IsHistory = true;
                    DbOpe_crm_contract_serviceinfo_wits.Instance.Update(hisServeInfo);
                    //被驳回服务重新初审时，删除被驳回服务的到账绑定关系
                    DbOpe_crm_contract_receiptregister_service.Instance.DeleteReturnServiceLinkData(hisServeInfo.Id);
                }
                var serveData = applData.MappingTo<Db_crm_contract_serviceinfo_wits>();
                //初审拒绝
                if (!audit_In.Pass)
                {//更改审核状态为拒绝,同时要加一条内容为空的登记信息
                    applData.State = EnumProcessStatus.Refuse;
                    applData.ReviewerDate = today;
                    applData.ReviewerId = currentUser;
                    applData.Feedback = audit_In.Remark;
                    applData.Remark4List = audit_In.Remark;
                    //服务变更数据拒绝后该数据状态为无效
                    if (applData.ProcessingType == EnumProcessingType.Change)
                        applData.IsInvalid = EnumIsInvalid.Invalid;
                    DbOpe_crm_contract_productserviceinfo_wits_appl.Instance.UpdateData(applData);

                    // 更新相关合同产品申请状态为拒绝
                    ContractProductApplStateHelper.UpdateWitsContractProductApplState(
                        applData.Id,
                        EnumProcessStatus.Refuse,
                        currentUser,
                        null,
                        applData.ChangeReasonEnums);

                    //加一条内容为空的登记拒绝信息
                    serveData.WitsApplId = applData.Id;
                    serveData.ContractNum = contractInfo.ContractNum;
                    serveData.State = EnumContractServiceState.REFUSE;
                    serveData.IsChanged = false;
                    serveData.Deleted = false;
                    serveData.IsProcessed = (int)EnumGtisServiceIsProcess.Not;
                    serveData.RegisteredId = currentUser;
                    serveData.RegisteredDate = today;
                    serveData.RegisteredRemark = audit_In.Remark;
                    serveData.Remark = null;
                    serveData.CounponDetailIds = String.Empty;
                    DbOpe_crm_contract_serviceinfo_wits.Instance.InsertData(serveData);
                    //如果存在优惠券，将状态改为未使用
                    if (!string.IsNullOrEmpty(applData.CounponDetailIds))
                    {
                        if (DbOpe_crm_customer_coupon.Instance.ChangeCouponType(applData.CounponDetailIds.Split(',').ToList(), EnumCouponType.Grant).state != 1)
                            throw new ApiException("优惠券退回失败");
                    }
                    //如果使用了个人服务天数，修改状态为拒绝
                    if (applData.PerlongServiceDays.GetValueOrDefault(0) > 0)
                        BLL_PrivateService.Instance.UsingPrivateServiceForContractService(applData.ContractId, EnumPrivateServiceType.Refuse, applData.Id, applData.PrivateServiceDays.GetValueOrDefault(0), applData.OverServiceDays.GetValueOrDefault(0));
                    //服务变更时存在的逻辑，将之前置位失效的申请数据恢复为有效，并获取reActApplId，便于处理申请时选择的慧思产品服务内容
                    if (applData.ProcessingType == EnumProcessingType.Change)
                        DbOpe_crm_contract_productserviceinfo_wits_appl.Instance.ReActiveCurrentWitsAppl(applData.ContractProductInfoSeriesId);
                    #region 拒绝每个服务的申请
                    if (applData.IsGtisApply)
                        RefuseGtisApply(audit_In.ApplyId, contractInfo.ContractNum, audit_In.Remark, today);
                    if (applData.IsGlobalSearchApply)
                        RefuseGlobalsearchApply(audit_In.ApplyId, contractInfo.ContractNum, audit_In.Remark, today);
                    if (applData.IsCollegeApply)
                        RefuseCollegeApply(audit_In.ApplyId, contractInfo.ContractNum, audit_In.Remark, today);
                    if (applData.IsSalesWitsApply)
                        RefuseSalesWitsApply(audit_In.ApplyId, contractInfo.ContractNum, audit_In.Remark, today);
                    #endregion
                    #region 服务拒绝的消息提醒 暂时注释 启用时需要修改
                    /*MessageMainInfo message = new MessageMainInfo();
                    message.Issuer = applData.CreateUser;
                    message.MessageTypeToId = applData.Id;
                    message.MessagemMainAboutDes = info.ContractName;
                    message.LocalFeedBack = applData.Feedback;
                    MessageGiveBack giveBack = BLL_MessageCenter.Instance.InsertMessageCenterDetail(message, EnumMessageLocalType.ServiceAbout, EnumMessageStepInfo.GtisService, EnumMessageStateInfo.Refus, applData.ContractId);
                    BLL_MessageCenter.Instance.RealTimeSend(giveBack);*/
                    #endregion
                }
                //初审通过
                else
                {
                    #region  参数验证
                    if (StringUtil.IsNullOrEmpty(contractInfo.ContractNum))
                        throw new ApiException("未到账合同不能开通服务产品");
                    if (audit_In.GtisAudit != null && (audit_In.GtisAudit.ServiceMonth == null || audit_In.GtisAudit.ServiceCycleStart == null || audit_In.GtisAudit.ServiceCycleEnd == null))
                        throw new ApiException("服务周期不能为空");
                    if (audit_In.GlobalSearchAudit != null && (audit_In.GlobalSearchAudit.ServiceMonth == null || audit_In.GlobalSearchAudit.ServiceCycleStart == null || audit_In.GlobalSearchAudit.ServiceCycleEnd == null))
                        throw new ApiException("服务周期不能为空");
                    if (audit_In.CollegeAudit != null && (audit_In.CollegeAudit.ServiceMonth == null || audit_In.CollegeAudit.ServiceCycleStart == null || audit_In.CollegeAudit.ServiceCycleEnd == null))
                        throw new ApiException("服务周期不能为空");
                    if (audit_In.SalesWitsAudit != null && (audit_In.SalesWitsAudit.ServiceMonth == null || audit_In.SalesWitsAudit.ServiceCycleStart == null || audit_In.SalesWitsAudit.ServiceCycleEnd == null))
                        throw new ApiException("服务周期不能为空");
                    #region 共享次数的验证，待完成
                    //判断共享使用次数 每个账号共享使用人数之和 不能大于总的共享使用次数（ 这里的sum求和之前验了账号状态，可能没用，先记下来  Where(e => e.OpeningStatus == 1)  ）
                    if (audit_In.UserList != null && audit_In.UserList.Count > 0 && audit_In.UserList.Sum(p => p.SharePeopleNum) > serveData.ShareUsageNum)
                        throw new ApiException("账号绑定人数之和不能大于绑定使用总数");
                    if (audit_In.UserList != null && audit_In.UserList.Count > 0 && audit_In.UserList.Any(e => e.SharePeopleNum == null))
                        throw new ApiException("账号绑定人数不可为空");
                    #endregion
                    #region 验证账号逻辑
                    if (applData.AccountGenerationMethod == EnumGtisAccountGenerationMethod.Generate)
                    {
                        if (string.IsNullOrEmpty(audit_In.OriWitsServeId))
                            throw new ApiException("需要指定原有账号");
                        var ori_witsServeUser_list = DbOpe_crm_contract_serviceinfo_wits_user.Instance.GetUsersByServelId(audit_In.OriWitsServeId);
                        audit_In.UserList.ForEach(user =>
                        {
                            if (!string.IsNullOrEmpty(user.SysUserId))
                            {
                                var ori_witsUser = ori_witsServeUser_list.Where(e => e.SysUserId == user.SysUserId).First();
                                //如果当前提交的初审中不包括gtis服务，账号的Gtis权限应取当前数据中账号的Gtis权限
                                if (audit_In.GtisAudit == null)
                                    user.GtisPermission = ori_witsUser.GtisPermission;
                                //如果当前提交的初审中不包括环球搜服务，账号的环球搜权限应取当前数据中账号的环球搜权限
                                if (audit_In.GlobalSearchAudit == null)
                                    user.GlobalSearchPermission = ori_witsUser.GlobalSearchPermission;
                                //如果当前提交的初审中不包括慧思学院服务，账号的慧思学院权限应取当前数据中账号的慧思学院权限
                                if (audit_In.CollegeAudit == null)
                                    user.CollegePermission = ori_witsUser.CollegePermission;
                                //如果当前提交的初审中不包括SalesWits服务，账号的SalesWits权限应取当前数据中账号的SalesWits权限
                                if (audit_In.SalesWitsAudit == null)
                                    user.SalesWitsPermission = ori_witsUser.SalesWitsPermission;
                            }
                        });
                    }
                    #region 验证账号数量与每个账号勾选的权限数量的逻辑是否相符
                    int maxAccountNum = 0;
                    if (audit_In.GtisAudit != null)
                    {
                        //初审提交的账号数量
                        var applyAccountNum = audit_In.GtisAudit.SubAccountsNum + 1;
                        if (audit_In.UserList.Count(e => e.GtisPermission == true) != applyAccountNum)
                            throw new ApiException("选择了Gtis权限的账号数量必须等于Gtis申请的账号数量");
                        //记录可申请最大账号数量1
                        maxAccountNum = applyAccountNum;
                    }
                    if (audit_In.GlobalSearchAudit != null)
                    {
                        //初审提交的账号数量
                        var applyAccountNum = audit_In.GlobalSearchAudit.AccountCount.GetValueOrDefault(0);
                        if (audit_In.UserList.Count(e => e.GlobalSearchPermission == true) > applyAccountNum)
                            throw new ApiException("选择了环球搜权限的账号数量超过Gtis可申请最大账号数量");
                        //记录可申请最大账号数量
                        maxAccountNum = maxAccountNum > applyAccountNum ? maxAccountNum : applyAccountNum;
                    }
                    if (audit_In.CollegeAudit != null)
                    {
                        //初审提交的账号数量
                        var applyAccountNum = audit_In.CollegeAudit.AccountCount.GetValueOrDefault(0);
                        if (audit_In.UserList.Count(e => e.CollegePermission == true) > applyAccountNum)
                            throw new ApiException("选择了慧思学院权限的账号数量超过Gtis可申请最大账号数量");
                        //记录可申请最大账号数量
                        maxAccountNum = maxAccountNum > applyAccountNum ? maxAccountNum : applyAccountNum;
                    }
                    if (audit_In.SalesWitsAudit != null)
                    {
                        //初审提交的账号数量
                        var applyAccountNum = audit_In.SalesWitsAudit.AccountCount.GetValueOrDefault(0);
                        if (audit_In.UserList.Count(e => e.SalesWitsPermission == true) > applyAccountNum)
                            throw new ApiException("选择了SalesWits权限的账号数量超过Gtis可申请最大账号数量");
                        //记录可申请最大账号数量
                        maxAccountNum = maxAccountNum > applyAccountNum ? maxAccountNum : applyAccountNum;
                    }
                    if (audit_In.UserList.Count() > maxAccountNum)
                        throw new ApiException("申请的账号数量超过允许申请的账号数量");
                    if (audit_In.UserList.Any(e => e.SalesWitsPermission == true && !e.GtisPermission == true) && audit_In.UserList.Any(e => e.GtisPermission == true && !e.SalesWitsPermission == true))
                        throw new ApiException("SalesWits权限须优先绑定在选择了Gtis权限的账号上");
                    #endregion
                    #endregion
                    #endregion
                    //申请数据只保存备注内容
                    applData.Remark4List = audit_In.Remark;
                    DbOpe_crm_contract_productserviceinfo_wits_appl.Instance.Update(applData);
                    //读前端输入的产品基础信息
                    audit_In.MappingTo(serveData);
                    serveData.Id = Guid.NewGuid().ToString();
                    serveData.WitsApplId = applData.Id;
                    serveData.ContractNum = contractInfo.ContractNum;
                    serveData.State = EnumContractServiceState.TO_BE_REVIEW;
                    serveData.IsChanged = false;
                    serveData.IsProcessed = (int)EnumGtisServiceIsProcess.Not;
                    serveData.RegisteredId = currentUser;
                    serveData.RegisteredDate = today;
                    serveData.RegisteredRemark = audit_In.Remark;
                    serveData.IsHistory = false;
                    serveData.IsGtisAudit = audit_In.GtisAudit != null;
                    serveData.IsGlobalSearchAudit = audit_In.GlobalSearchAudit != null;
                    serveData.IsCollegeAudit = audit_In.CollegeAudit != null;
                    serveData.IsSalesWitsAudit = audit_In.SalesWitsAudit != null;
                    #region 计算账号的开通周期：所有服务的最早开始时间--所有服务的最晚结束时间
                    var startDateList = new List<DateTime>(); var endDateList = new List<DateTime>();
                    if (serveData.IsGtisAudit)
                    {//如果申请包含Gits服务，取提交信息中的开始结束时间
                        startDateList.Add(audit_In.GtisAudit.ServiceCycleStart.Value);
                        endDateList.Add(audit_In.GtisAudit.ServiceCycleEnd.Value);
                    }
                    if (serveData.IsGlobalSearchAudit)
                    {//如果申请包含环球搜服务，取提交信息中的开始结束时间
                        startDateList.Add(audit_In.GlobalSearchAudit.ServiceCycleStart.Value);
                        endDateList.Add(audit_In.GlobalSearchAudit.ServiceCycleEnd.Value);
                    }
                    if (serveData.IsCollegeAudit)
                    {//如果申请包含慧思学院服务，取提交信息中的开始结束时间
                        startDateList.Add(audit_In.CollegeAudit.ServiceCycleStart.Value);
                        endDateList.Add(audit_In.CollegeAudit.ServiceCycleEnd.Value);
                    }
                    if (serveData.IsSalesWitsAudit)
                    {//如果申请包含SalesWits服务，取提交信息中的开始结束时间
                        startDateList.Add(audit_In.SalesWitsAudit.ServiceCycleStart.Value);
                        endDateList.Add(audit_In.SalesWitsAudit.ServiceCycleEnd.Value);
                    }
                    #endregion
                    serveData.ServiceCycleStart = startDateList.Min();
                    serveData.ServiceCycleEnd = endDateList.Max();
                    serveData.IsGtisAudit = audit_In.GtisAudit != null;
                    serveData.IsGlobalSearchAudit = audit_In.GlobalSearchAudit != null;
                    serveData.IsCollegeAudit = audit_In.CollegeAudit != null;
                    serveData.IsSalesWitsAudit = audit_In.SalesWitsAudit != null;
                    //开通gtis时候需要传的账号参数列表
                    var wits_user_list = new List<Db_crm_contract_serviceinfo_wits_user>();
                    //使用原有账号时会用到的原wits服务数据
                    var oriWitsServe = new Db_crm_contract_serviceinfo_wits();
                    //重新生成账号（可能是新增合同，也可能是续约合同）
                    if (applData.AccountGenerationMethod == EnumGtisAccountGenerationMethod.Remake)
                    {
                        //服务内容中真实存在的权限
                        serveData.HasGtisApp = serveData.IsGtisAudit;
                        serveData.HasGlobalSearchApp = serveData.IsGlobalSearchAudit;
                        serveData.HasCollegeApp = serveData.IsCollegeAudit;
                        serveData.HasSalesWitsApp = serveData.IsSalesWitsAudit;
                        //Gtis数据
                        if (serveData.HasGtisApp.GetValueOrDefault(false))
                        {
                            serveData.GtisPrimaryAccountsNum = 1;
                            serveData.GtisSubAccountsNum = audit_In.GtisAudit.SubAccountsNum;
                            serveData.GtisAuthorizationNum = audit_In.GtisAudit.AuthorizationNum;
                            serveData.GtisServiceCycleStart = audit_In.GtisAudit.ServiceCycleStart;
                            serveData.GtisServiceCycleEnd = audit_In.GtisAudit.ServiceCycleEnd;
                        }
                        //环球搜数据
                        if (serveData.HasGlobalSearchApp.GetValueOrDefault(false))
                        {
                            serveData.GlobalSearchAccountsNum = audit_In.GlobalSearchAudit.AccountCount;
                            serveData.GlobalSearchServiceCycleStart = audit_In.GlobalSearchAudit.ServiceCycleStart;
                            serveData.GlobalSearchServiceCycleEnd = audit_In.GlobalSearchAudit.ServiceCycleEnd;
                        }
                        //慧思学院数据
                        if (serveData.HasCollegeApp.GetValueOrDefault(false))
                        {
                            serveData.CollegeAccountsNum = audit_In.CollegeAudit.AccountCount;
                            serveData.CollegeServiceCycleStart = audit_In.CollegeAudit.ServiceCycleStart;
                            serveData.CollegeServiceCycleEnd = audit_In.CollegeAudit.ServiceCycleEnd;
                        }
                        //saleswits数据
                        if (serveData.HasSalesWitsApp.GetValueOrDefault(false))
                        {
                            serveData.SalesWitsAccountsNum = audit_In.SalesWitsAudit.AccountCount;
                            serveData.SalesWitsServiceCycleStart = audit_In.SalesWitsAudit.ServiceCycleStart;
                            serveData.SalesWitsServiceCycleEnd = audit_In.SalesWitsAudit.ServiceCycleEnd;
                        }
                    }
                    //使用原有账号（可能是服务变更，也可能是合同续约申请时选择了使用原有账号）
                    else if (applData.AccountGenerationMethod == EnumGtisAccountGenerationMethod.Generate)
                    {
                        oriWitsServe = DbOpe_crm_contract_serviceinfo_wits.Instance.QueryByPrimaryKey(audit_In.OriWitsServeId);
                        //服务内容中真实存在的权限
                        serveData.HasGtisApp = serveData.IsGtisAudit || oriWitsServe.HasGtisApp.GetValueOrDefault(false);
                        serveData.HasGlobalSearchApp = serveData.IsGlobalSearchAudit || oriWitsServe.HasGlobalSearchApp.GetValueOrDefault(false);
                        serveData.HasCollegeApp = serveData.IsCollegeAudit || oriWitsServe.HasCollegeApp.GetValueOrDefault(false);
                        serveData.HasSalesWitsApp = serveData.IsSalesWitsAudit || oriWitsServe.HasSalesWitsApp.GetValueOrDefault(false);
                        //定义OldContractNum
                        serveData.OldContractNum = oriWitsServe.ContractNum;
                        //如果存在Gtis权限，补充Gtis数据
                        if (serveData.HasGtisApp.GetValueOrDefault(false))
                        {
                            //判断如果当前申请中提交了新的gtis信息就使用新的，如果没提交新的则使用 oriWitsServe 的数据
                            serveData.GtisPrimaryAccountsNum = serveData.IsGtisAudit ? 1 : oriWitsServe.GtisPrimaryAccountsNum;
                            serveData.GtisSubAccountsNum = serveData.IsGtisAudit ? audit_In.GtisAudit.SubAccountsNum : oriWitsServe.GtisSubAccountsNum;
                            serveData.GtisAuthorizationNum = serveData.IsGtisAudit ? audit_In.GtisAudit.AuthorizationNum : oriWitsServe.GtisAuthorizationNum;
                            serveData.GtisServiceCycleStart = serveData.IsGtisAudit ? audit_In.GtisAudit.ServiceCycleStart : oriWitsServe.GtisServiceCycleStart;
                            serveData.GtisServiceCycleEnd = serveData.IsGtisAudit ? audit_In.GtisAudit.ServiceCycleEnd : oriWitsServe.GtisServiceCycleEnd;
                            serveData.CurrentGtisServiceId = oriWitsServe.CurrentGtisServiceId;
                        }
                        //如果存在环球搜权限，补充环球搜数据
                        if (serveData.HasGlobalSearchApp.GetValueOrDefault(false))
                        {
                            //判断如果当前申请中提交了新的环球搜信息就使用新的，如果没提交新的则使用 oriWitsServe 的数据
                            serveData.GlobalSearchAccountsNum = serveData.IsGlobalSearchAudit ? audit_In.GlobalSearchAudit.AccountCount : oriWitsServe.GlobalSearchAccountsNum;
                            serveData.GlobalSearchServiceCycleStart = serveData.IsGlobalSearchAudit ? audit_In.GlobalSearchAudit.ServiceCycleStart : oriWitsServe.GlobalSearchServiceCycleStart;
                            serveData.GlobalSearchServiceCycleEnd = serveData.IsGlobalSearchAudit ? audit_In.GlobalSearchAudit.ServiceCycleEnd : oriWitsServe.GlobalSearchServiceCycleEnd;
                            serveData.CurrentGlobalSearchServiceId = oriWitsServe.CurrentGlobalSearchServiceId;
                        }
                        //如果存在慧思学院权限，补充慧思学院数据
                        if (serveData.HasCollegeApp.GetValueOrDefault(false))
                        {
                            //判断如果当前申请中提交了新的慧思学院信息就使用新的，如果没提交新的则使用 oriWitsServe 的数据
                            serveData.CollegeAccountsNum = serveData.IsCollegeAudit ? audit_In.CollegeAudit.AccountCount : oriWitsServe.CollegeAccountsNum;
                            serveData.CollegeServiceCycleStart = serveData.IsCollegeAudit ? audit_In.CollegeAudit.ServiceCycleStart : oriWitsServe.CollegeServiceCycleStart;
                            serveData.CollegeServiceCycleEnd = serveData.IsCollegeAudit ? audit_In.CollegeAudit.ServiceCycleEnd : oriWitsServe.CollegeServiceCycleEnd;
                            serveData.CurrentCollegeServiceId = oriWitsServe.CurrentCollegeServiceId;
                        }
                        //如果存在saleswits权限，补充saleswits数据
                        if (serveData.HasSalesWitsApp.GetValueOrDefault(false))
                        {
                            serveData.SalesWitsAccountsNum = serveData.IsSalesWitsAudit ? audit_In.SalesWitsAudit.AccountCount : oriWitsServe.SalesWitsAccountsNum;
                            serveData.SalesWitsServiceCycleStart = serveData.IsSalesWitsAudit ? audit_In.SalesWitsAudit.ServiceCycleStart : oriWitsServe.SalesWitsServiceCycleStart;
                            serveData.SalesWitsServiceCycleEnd = serveData.IsSalesWitsAudit ? audit_In.SalesWitsAudit.ServiceCycleEnd : oriWitsServe.SalesWitsServiceCycleEnd;
                            serveData.CurrentSalesWitsServiceId = oriWitsServe.CurrentSalesWitsServiceId;
                        }
                        //如果是服务变更，记录HistoryId
                        if (serveData.ProcessingType == (int)EnumProcessingType.Change)
                            serveData.HistoryId = oriWitsServe.Id;
                    }
                    //保存服务信息
                    DbOpe_crm_contract_serviceinfo_wits.Instance.Insert(serveData);
                    #region 创建wits_user
                    //分配超级子账号，超级子账号数量不能大于子账号授权国家次数
                    int LeftSuperSubAccountTotalNum = Math.Min(applData.SuperSubAccountNum, serveData.GtisAuthorizationNum.GetValueOrDefault(0));
                    //账号信息
                    audit_In.UserList.ForEach(item =>
                    {
                        var user = item.MappingTo<Db_crm_contract_serviceinfo_wits_user>();
                        user.StartDate = serveData.ServiceCycleStart;
                        user.EndDate = serveData.ServiceCycleEnd;
                        user.WitsServeId = serveData.Id;
                        user.OpeningStatus = (int)EnumGtisUserOpeningStatus.INPROCESS;
                        user.AllCountrySubUser = false;
                        user.SalesWitsBindPhoneId = item.SalesWitsBindPhoneId;
                        //如果存在Gtis服务申请，处理超级子账号分配问题
                        if (serveData.HasGtisApp.GetValueOrDefault(false))
                        {
                            //情况1：如果子账号数量为1并且gtis选了子账号自动分配全部国家，那么这个子账号自动变为超级子账号
                            if (audit_In.GtisAudit.AllCountrySubUser == true && serveData.GtisSubAccountsNum == 1 && user.AccountType == EnumGtisAccountType.Sub.ToInt())
                            {
                                user.AllCountrySubUser = true;
                            }
                            //情况2 ：买了超级子账号且是子账号
                            if (LeftSuperSubAccountTotalNum > 0 && user.AccountType == EnumGtisAccountType.Sub.ToInt())
                            {
                                user.AllCountrySubUser = true;
                                LeftSuperSubAccountTotalNum--;
                            }
                        }
                        DbOpe_crm_contract_serviceinfo_wits_user.Instance.InsertData(user);
                        wits_user_list.Add(user);
                    });
                    #endregion
                    //绑定到账信息
                    if (audit_In.ReceiptRegisterIds != null && audit_In.ReceiptRegisterIds.Count > 0)
                        DbOpe_crm_contract_receiptregister_service.Instance.LinkReceiptData(audit_In.ReceiptRegisterIds, serveData.Id, EnumProductType.WitsProducts);
                    #region 初审通过每个服务的申请
                    //Gtis
                    if (serveData.IsGtisAudit)
                        AuditGtisApply(audit_In.GtisAudit, serveData, contractInfo, wits_user_list.Where(e => e.GtisPermission).ToList(), oriWitsServe.CurrentGtisServiceId, today);
                    else if (applData.IsGtisApply)//申请了但没有审核
                        RefuseGtisApply(audit_In.ApplyId, contractInfo.ContractNum, audit_In.Remark, today);
                    //环球搜
                    if (serveData.IsGlobalSearchAudit)
                        AuditGlobalSearchApply(audit_In.GlobalSearchAudit, serveData, contractInfo.ContractType.GetValueOrDefault(0), oriWitsServe.CurrentGlobalSearchServiceId, today);
                    else if (applData.IsGlobalSearchApply)//申请了但没有审核
                        RefuseGlobalsearchApply(audit_In.ApplyId, contractInfo.ContractNum, audit_In.Remark, today);
                    //慧思学院
                    if (serveData.IsCollegeAudit)
                        AuditCollegeApply(audit_In.CollegeAudit, serveData, contractInfo.ContractType.GetValueOrDefault(0), oriWitsServe.CurrentCollegeServiceId, today);
                    else if (applData.IsCollegeApply)//申请了但没有审核
                        RefuseCollegeApply(audit_In.ApplyId, contractInfo.ContractNum, audit_In.Remark, today);
                    //SalesWits
                    if (serveData.IsSalesWitsAudit)
                        AuditSalesWitsApply(audit_In.SalesWitsAudit, serveData, contractInfo.ContractType.GetValueOrDefault(0), oriWitsServe.CurrentSalesWitsServiceId, today);
                    else if (applData.IsSalesWitsApply)//申请了但没有审核
                        RefuseSalesWitsApply(audit_In.ApplyId, contractInfo.ContractNum, audit_In.Remark, today);
                    #endregion

                }
                //开通的workflow记录
                if (audit_In.Pass)
                    BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_serviceinfo_wits, Db_crm_contract>("慧思产品(Gtis系列)服务审批流程", applData.Id, serveData, contractInfo, serveData.RegisteredRemark, EnumContractServiceOpenState.ToBeReview.GetEnumDescription(), "初审");
                else
                    BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_productserviceinfo_wits_appl, Db_crm_contract>("慧思产品(Gtis系列)服务审批流程", applData.Id, applData, contractInfo, applData.ApplicantId, applData.Feedback, EnumContractServiceOpenState.Refuse.GetEnumDescription(), "初审");
            });
        }

        #region  各个产品的初审方法

        #region 各个服务初审拒绝方法
        /// <summary>
        /// 拒绝Gits申请表数据
        /// </summary>
        /// <param name="witsApplId"></param>
        /// <param name="contractNum"></param>
        /// <param name="remark"></param>
        /// <param name="today"></param>
        private void RefuseGtisApply(string witsApplId, string contractNum, string remark, DateTime today)
        {
            var applData = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.GetGtisApplByWitsId(witsApplId);
            var serveData = applData.MappingTo<Db_crm_contract_serviceinfo_gtis>();
            //拒绝的更改审核状态为拒绝  同时要加一条内容为空的登记信息
            applData.State = (int)EnumProcessStatus.Refuse;
            applData.ReviewerDate = today;
            applData.ReviewerId = currentUser;
            applData.Feedback = remark;
            applData.Remark4List = remark;
            //服务变更数据拒绝后该数据状态为无效
            if (applData.ProcessingType == (int)EnumProcessingType.Change)
                applData.IsInvalid = (int)EnumIsInvalid.Invalid;
            DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.Update(applData);
            //获取登记信息  (如果是驳回的这里应该有历史信息，初审的没有)
            var hisService = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetGtisServiceByApplyId(applData.Id);
            //如果有上一条登记审核信息 将之前驳回的申请置为历史
            if (hisService != null)
            {
                hisService.IsApplHistory = true;
                DbOpe_crm_contract_serviceinfo_gtis.Instance.UpdateData(hisService);
            }
            //加一条内容为空的登记拒绝信息
            serveData.Id = Guid.NewGuid().ToString();
            serveData.ProductServiceInfoGtisApplId = applData.Id;
            serveData.ContractNum = contractNum;
            serveData.State = (int)EnumContractServiceState.REFUSE;
            serveData.IsChanged = (int)EnumContractServiceInfoIsChange.Not;
            serveData.Deleted = false;
            serveData.IsProcessed = (int)EnumGtisServiceIsProcess.Not;
            serveData.RegisteredId = currentUser;
            serveData.RegisteredDate = today;
            serveData.RegisteredRemark = remark;
            serveData.Remark = null;
            serveData.CouponIds = String.Empty;
            DbOpe_crm_contract_serviceinfo_gtis.Instance.Insert(serveData);
            //将之前置位失效的申请数据恢复为有效
            DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.ReActiveCurrentGtisAppl(applData.ContractProductInfoId);


        }

        /// <summary>
        /// 拒绝环球搜申请表数据
        /// </summary>
        /// <param name="witsApplId"></param>
        /// <param name="contractNum"></param>
        /// <param name="remark"></param>
        /// <param name="today"></param>
        private void RefuseGlobalsearchApply(string witsApplId, string contractNum, string remark, DateTime today)
        {
            var applData = DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.GetGlobalsearchApplByWitsId(witsApplId);
            var serveData = applData.MappingTo<Db_crm_contract_serviceinfo_globalsearch>();
            //拒绝的更改审核状态为拒绝  同时要加一条内容为空的登记信息
            applData.State = (int)EnumProcessStatus.Refuse;
            applData.ReviewerDate = today;
            applData.ReviewerId = currentUser;
            applData.Feedback = remark;
            applData.Remark4List = remark;
            //服务变更数据拒绝后该数据状态为无效
            if (applData.ProcessingType == (int)EnumProcessingType.Change)
                applData.IsInvalid = (int)EnumIsInvalid.Invalid;
            DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.Update(applData);
            //获取登记信息  (如果是驳回的这里应该有历史信息，初审的没有)
            var hisService = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetSerivceDataByApplyId(applData.Id);
            //如果有上一条登记审核信息 将之前驳回的申请置为历史
            if (hisService != null)
            {
                hisService.IsHistory = true;
                DbOpe_crm_contract_serviceinfo_globalsearch.Instance.UpdateData(hisService);
            }
            //加一条内容为空的登记拒绝信息
            serveData.Id = Guid.NewGuid().ToString();
            serveData.ProductServiceInfoGlobalSearchApplId = applData.Id;
            serveData.ContractNum = contractNum;
            serveData.State = EnumContractServiceState.REFUSE;
            serveData.IsChanged = false;
            serveData.Deleted = false;
            serveData.CreateDate = DateTime.Now;
            serveData.CreateUser = UserId;
            serveData.RegisteredId = currentUser;
            serveData.RegisteredTime = today;
            serveData.RegisteredRemark = remark;
            serveData.Remark = remark;
            DbOpe_crm_contract_serviceinfo_globalsearch.Instance.Insert(serveData);
            //将之前置位失效的申请数据恢复为有效
            DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.ReActiveCurrentAppl(applData.ContractProductInfoId);
        }

        /// <summary>
        /// 拒绝慧思学院申请表数据
        /// </summary>
        /// <param name="witsApplId"></param>
        /// <param name="contractNum"></param>
        /// <param name="remark"></param>
        /// <param name="today"></param>
        private void RefuseCollegeApply(string witsApplId, string contractNum, string remark, DateTime today)
        {
            var applData = DbOpe_crm_contract_productserviceinfo_college_appl.Instance.GetCollegeApplByWitsId(witsApplId);
            var serveData = applData.MappingTo<Db_crm_contract_serviceinfo_college>();
            //拒绝的更改审核状态为拒绝  同时要加一条内容为空的登记信息
            applData.State = (int)EnumProcessStatus.Refuse;
            applData.ReviewerDate = today;
            applData.ReviewerId = currentUser;
            applData.Feedback = remark;
            //服务变更数据拒绝后该数据状态为无效
            if (applData.ProcessingType == (int)EnumProcessingType.Change)
                applData.IsInvalid = (int)EnumIsInvalid.Invalid;
            DbOpe_crm_contract_productserviceinfo_college_appl.Instance.Update(applData);
            //获取登记信息  (如果是驳回的这里应该有历史信息，初审的没有)
            var hisService = DbOpe_crm_contract_serviceinfo_college.Instance.GetSerivceDataByApplyId(applData.Id);
            //如果有上一条登记审核信息 将之前驳回的申请置为历史
            if (hisService != null)
            {
                hisService.IsHistory = true;
                DbOpe_crm_contract_serviceinfo_college.Instance.UpdateData(hisService);
            }
            //加一条内容为空的登记拒绝信息
            serveData.Id = Guid.NewGuid().ToString();
            serveData.ProductServiceInfoCollegeApplId = applData.Id;
            serveData.ContractNum = contractNum;
            serveData.State = EnumContractServiceState.REFUSE;
            serveData.IsChanged = false;
            serveData.Deleted = false;
            serveData.CreateDate = DateTime.Now;
            serveData.CreateUser = UserId;
            serveData.RegisteredId = currentUser;
            serveData.RegisteredTime = today;
            serveData.RegisteredRemark = remark;
            serveData.Remark = remark;
            DbOpe_crm_contract_serviceinfo_college.Instance.Insert(serveData);
            //将之前置位失效的申请数据恢复为有效
            DbOpe_crm_contract_productserviceinfo_college_appl.Instance.ReActiveCurrentAppl(applData.ContractProductInfoId);
        }

        /// <summary>
        /// 拒绝SalesWits申请表数据
        /// </summary>
        /// <param name="witsApplId"></param>
        /// <param name="contractNum"></param>
        /// <param name="remark"></param>
        /// <param name="today"></param>
        private void RefuseSalesWitsApply(string witsApplId, string contractNum, string remark, DateTime today)
        {
            var applData = DbOpe_crm_contract_productserviceinfo_saleswits_appl.Instance.GetSalesWitsApplByWitsId(witsApplId);
            var serveData = applData.MappingTo<Db_crm_contract_serviceinfo_saleswits>();
            //拒绝的更改审核状态为拒绝  同时要加一条内容为空的登记信息
            applData.State = (int)EnumProcessStatus.Refuse;
            applData.ReviewerDate = today;
            applData.ReviewerId = currentUser;
            applData.Feedback = remark;
            //服务变更数据拒绝后该数据状态为无效
            if (applData.ProcessingType == (int)EnumProcessingType.Change)
                applData.IsInvalid = (int)EnumIsInvalid.Invalid;
            DbOpe_crm_contract_productserviceinfo_saleswits_appl.Instance.Update(applData);
            //获取登记信息  (如果是驳回的这里应该有历史信息，初审的没有)
            var hisService = DbOpe_crm_contract_serviceinfo_saleswits.Instance.GetSerivceDataByApplyId(applData.Id);
            //如果有上一条登记审核信息 将之前驳回的申请置为历史
            if (hisService != null)
            {
                hisService.IsHistory = true;
                DbOpe_crm_contract_serviceinfo_saleswits.Instance.UpdateData(hisService);
            }
            //加一条内容为空的登记拒绝信息
            serveData.Id = Guid.NewGuid().ToString();
            serveData.ProductServiceInfoSalesWitsApplId = applData.Id;
            serveData.ContractNum = contractNum;
            serveData.State = EnumContractServiceState.REFUSE;
            serveData.IsChanged = false;
            serveData.Deleted = false;
            serveData.CreateDate = DateTime.Now;
            serveData.CreateUser = UserId;
            serveData.RegisteredId = currentUser;
            serveData.RegisteredTime = today;
            serveData.RegisteredRemark = remark;
            serveData.Remark = remark;
            DbOpe_crm_contract_serviceinfo_saleswits.Instance.Insert(serveData);
            //将之前置位失效的申请数据恢复为有效
            DbOpe_crm_contract_productserviceinfo_saleswits_appl.Instance.ReActiveCurrentAppl(applData.ContractProductInfoId);
        }
        #endregion

        #region 各个服务的初审通过方法
        /// <summary>
        /// Gtis服务初审通过
        /// </summary>
        /// <param name="audit_In"></param>
        /// <param name="witsServeData"></param>
        /// <param name="contractInfo"></param>
        /// <param name="userList"></param>
        /// <param name="oriServiceId"></param>
        /// <param name="today"></param>
        public void AuditGtisApply(AuditWitsAppl_In_Gtis audit_In, Db_crm_contract_serviceinfo_wits witsServeData, Db_crm_contract contractInfo, List<Db_crm_contract_serviceinfo_wits_user> userList, string oriServiceId, DateTime today)
        {
            //获取服务申请信息
            var applData = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.GetDataById(audit_In.ApplyId);
            applData.Remark4List = audit_In.Remark;
            DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.Update(applData);
            //获取登记信息  (如果是驳回的这里应该有历史信息，初审的没有)
            var hisService = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetGtisServiceByApplyId(audit_In.ApplyId);
            //如果有上一条登记审核信息 将之前驳回的申请置为历史
            if (hisService != null)
            {
                hisService.IsApplHistory = true;
                DbOpe_crm_contract_serviceinfo_gtis.Instance.UpdateData(hisService);
            }
            //初始化服务信息，先用申请数据映射，再用前端传入的数据映射
            var serveData = applData.MappingTo<Db_crm_contract_serviceinfo_gtis>();
            audit_In.MappingTo(serveData);
            //补充服务信息
            serveData.ProductServiceInfoGtisApplId = applData.Id;
            serveData.ContractNum = witsServeData.ContractNum;
            serveData.State = (int)EnumContractServiceState.TO_BE_REVIEW;
            serveData.ShareUsageNum = witsServeData.ShareUsageNum;
            serveData.IsChanged = (int)EnumContractServiceInfoIsChange.Not;
            serveData.PrimaryAccountsNum = 1;
            serveData.IsProcessed = (int)EnumGtisServiceIsProcess.Not;
            if (serveData.WordRptPermissions != true && serveData.WordRptMaxTimes.GetValueOrDefault(0) <= 0)
            {
                serveData.WordRptMaxTimes = 0;
                serveData.WordRptPermissions = false;
            }
            serveData.RegisteredRemark = audit_In.Remark;
            serveData.RegisteredId = currentUser;
            serveData.RegisteredDate = today;
            serveData.AccountGenerationMethod = witsServeData.AccountGenerationMethod;
            serveData.IsGtisOldCustomer = false;//默认是新客户，后面看续约和变更状态再赋值
            //处理零售国家信息
            if (audit_In.RetailCountry == (int)EnumGtisRetailCountry.Add)
            {
                audit_In.GtisApplCountry.ForEach(c =>
                {
                    DbOpe_crm_contract_serviceinfo_gtis_country.Instance.Insert(new Db_crm_contract_serviceinfo_gtis_country
                    {
                        Id = Guid.NewGuid().ToString(),
                        ContractServiceInfoGtisId = serveData.Id,
                        Sid = c.Sid
                    });
                });
            }
            if (applData.ProcessingType == (int)EnumProcessingType.Change)
            {
                var hisGtisService = DbOpe_crm_contract_serviceinfo_gtis.Instance.QueryByPrimaryKey(oriServiceId);
                serveData.HistoryId = oriServiceId;
                serveData.IsGtisOldCustomer = hisGtisService.IsGtisOldCustomer;//取上一次的新老客户标记
            }
            else if (contractInfo.ContractType == (int)EnumContractType.ReNew)
            {
                var hisGtisService = DbOpe_crm_contract_serviceinfo_gtis.Instance.QueryByPrimaryKey(oriServiceId);
                if (!contractInfo.IsOverseasCustomer.GetValueOrDefault(false) && new TimeSpan(serveData.ServiceCycleStart.Value.Ticks - hisGtisService.ServiceCycleEnd.Value.Ticks).TotalDays > 90)
                    serveData.IsGtisOldCustomer = false;//超过90天续约的境内客户，算新客户
                else//其他情况沿用续约之前的新老客户标记;
                    serveData.IsGtisOldCustomer = hisGtisService.IsGtisOldCustomer;
                serveData.HistoryId = oriServiceId;
            }
            DbOpe_crm_contract_serviceinfo_gtis.Instance.InsertData(serveData);
            //生成gtis_user数据
            userList.ForEach(item =>
            {
                var user = item.MappingTo<Db_crm_contract_serviceinfo_gtis_user>();
                user.ContractServiceInfoGtisId = serveData.Id;
                DbOpe_crm_contract_serviceinfo_gtis_user.Instance.InsertData(user);
            });

        }

        /// <summary>
        /// 环球搜初审通过
        /// </summary>
        /// <param name="audit_In"></param>
        /// <param name="witsServeData"></param>
        /// <param name="contractType"></param>
        /// <param name="oriServiceId"></param>
        /// <param name="today"></param>
        public void AuditGlobalSearchApply(AuditWitsAppl_In_GlobalSearch audit_In, Db_crm_contract_serviceinfo_wits witsServeData, int contractType, string oriServiceId, DateTime today)
        {
            //获取申请的数据
            var applData = DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.QueryByPrimaryKey(audit_In.ApplyId);
            applData.Remark4List = audit_In.Remark;
            DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.Update(applData);
            //获取登记信息  (如果是驳回的这里应该有历史信息，初审的没有)
            var hisService = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetSerivceDataByApplyId(audit_In.ApplyId);
            //如果有上一条登记审核信息 将之前驳回的申请置为历史
            if (hisService != null)
            {
                hisService.IsHistory = true;
                DbOpe_crm_contract_serviceinfo_globalsearch.Instance.UpdateData(hisService);
            }
            //初始化服务信息，先用申请数据映射，再用前端传入的数据映射
            var serveData = applData.MappingTo<Db_crm_contract_serviceinfo_globalsearch>();
            audit_In.MappingTo(serveData);
            //补充服务数据
            serveData.Id = Guid.NewGuid().ToString();
            serveData.ProductServiceInfoGlobalSearchApplId = applData.Id;
            serveData.ContractNum = witsServeData.ContractNum;
            serveData.State = EnumContractServiceState.TO_BE_REVIEW;
            serveData.IsChanged = false;
            serveData.IsHistory = false;//如果是被驳回状态的数据，新建service数据后，将原service数据置位历史状态
            serveData.RegisteredId = UserId;
            serveData.RegisteredTime = today;
            serveData.RegisteredRemark = audit_In.Remark;
            serveData.ExecuteServiceCycleStart = audit_In.ServiceCycleStart;
            serveData.ExecuteServiceCycleEnd = audit_In.ServiceCycleEnd;
            serveData.ExecuteServiceMonth = audit_In.ServiceMonth;
            serveData.IsProcessed = (int)EnumGtisServiceIsProcess.Not;
            //变更的处理
            if (applData.ProcessingType == EnumProcessingType.Change.ToInt())
            {
                serveData.HistoryId = oriServiceId;
            }
            //续约的处理
            else if (contractType == (int)EnumContractType.ReNew)
            {
                var oriService = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.QueryByPrimaryKey(oriServiceId);
                serveData.OldContractNum = oriService.ContractNum;
                serveData.HistoryId = oriServiceId;
            }
            //插入服务数据
            DbOpe_crm_contract_serviceinfo_globalsearch.Instance.InsertData(serveData);
        }

        /// <summary>
        /// 慧思学院初审通过
        /// </summary>
        /// <param name="audit_In"></param>
        /// <param name="witsServeData"></param>
        /// <param name="contractType"></param>
        /// <param name="oriServiceId"></param>
        /// <param name="today"></param>
        public void AuditCollegeApply(AuditWitsAppl_In_College audit_In, Db_crm_contract_serviceinfo_wits witsServeData, int contractType, string oriServiceId, DateTime today)
        {
            //获取申请的数据
            var applData = DbOpe_crm_contract_productserviceinfo_college_appl.Instance.QueryByPrimaryKey(audit_In.ApplyId);
            //获取登记信息  (如果是驳回的这里应该有历史信息，初审的没有)
            var hisService = DbOpe_crm_contract_serviceinfo_college.Instance.GetSerivceDataByApplyId(audit_In.ApplyId);
            //如果有上一条登记审核信息 将之前驳回的申请置为历史
            if (hisService != null)
            {
                hisService.IsHistory = true;
                DbOpe_crm_contract_serviceinfo_college.Instance.UpdateData(hisService);
            }
            //初始化服务信息，先用申请数据映射，再用前端传入的数据映射
            var serveData = applData.MappingTo<Db_crm_contract_serviceinfo_college>();
            audit_In.MappingTo(serveData);
            //补充服务数据
            serveData.Id = Guid.NewGuid().ToString();
            serveData.ProductServiceInfoCollegeApplId = applData.Id;
            serveData.ContractNum = witsServeData.ContractNum;
            serveData.State = EnumContractServiceState.TO_BE_REVIEW;
            serveData.IsChanged = false;
            serveData.IsHistory = false;
            serveData.RegisteredId = UserId;
            serveData.RegisteredTime = today;
            serveData.PrimaryAccountsNum = audit_In.AccountCount;
            serveData.RegisteredRemark = audit_In.Remark;
            serveData.IsProcessed = (int)EnumGtisServiceIsProcess.Not;
            DbOpe_crm_contract_serviceinfo_college.Instance.InsertData(serveData);
            //变更或续约的处理
            if (applData.ProcessingType == EnumProcessingType.Change.ToInt() || contractType == (int)EnumContractType.ReNew)
                serveData.HistoryId = oriServiceId;
        }

        /// <summary>
        /// SalesWits初审通过
        /// </summary>
        /// <param name="audit_In"></param>
        /// <param name="witsServeData"></param>
        /// <param name="contractType"></param>
        /// <param name="oriServiceId"></param>
        /// <param name="today"></param>
        public void AuditSalesWitsApply(AuditWitsAppl_In_SalesWits audit_In, Db_crm_contract_serviceinfo_wits witsServeData, int contractType, string oriServiceId, DateTime today)
        {
            //获取申请的数据
            var applData = DbOpe_crm_contract_productserviceinfo_saleswits_appl.Instance.QueryByPrimaryKey(audit_In.ApplyId);
            //获取登记信息  (如果是驳回的这里应该有历史信息，初审的没有)
            var hisService = DbOpe_crm_contract_serviceinfo_saleswits.Instance.GetSerivceDataByApplyId(audit_In.ApplyId);
            //如果有上一条登记审核信息 将之前驳回的申请置为历史
            if (hisService != null)
            {
                hisService.IsHistory = true;
                DbOpe_crm_contract_serviceinfo_saleswits.Instance.UpdateData(hisService);
            }
            //初始化服务信息，先用申请数据映射，再用前端传入的数据映射
            var serveData = applData.MappingTo<Db_crm_contract_serviceinfo_saleswits>();
            audit_In.MappingTo(serveData);
            //补充服务数据
            serveData.Id = Guid.NewGuid().ToString();
            serveData.ProductServiceInfoSalesWitsApplId = applData.Id;
            serveData.ContractNum = witsServeData.ContractNum;
            serveData.State = EnumContractServiceState.TO_BE_REVIEW;
            serveData.IsChanged = false;
            serveData.IsHistory = false;
            serveData.RegisteredId = UserId;
            serveData.RegisteredTime = today;
            serveData.AccountsNum = audit_In.AccountCount;
            serveData.RegisteredRemark = audit_In.Remark;
            serveData.RechargeAmount = audit_In.AddCredit;
            serveData.IsProcessed = (int)EnumGtisServiceIsProcess.Not;
            DbOpe_crm_contract_serviceinfo_saleswits.Instance.InsertData(serveData);
            //变更或续约的处理
            if (applData.ProcessingType == EnumProcessingType.Change.ToInt() || contractType == (int)EnumContractType.ReNew)
                serveData.HistoryId = oriServiceId;
        }
        #endregion

        #endregion

        /// <summary>
        /// 慧思产品复核
        /// </summary>
        /// <param name="review_In"></param>
        public void ReviewWitsAppl(ReviewWitsApply_In review_In)
        {
            DbOpe_crm_contract_productserviceinfo_wits_appl.Instance.TransDeal(() =>
            {
                var today = DateTime.Now;
                //获取申请表信息
                var applyData = DbOpe_crm_contract_productserviceinfo_wits_appl.Instance.QueryByPrimaryKey(review_In.WitsApplId);
                if (applyData == null || applyData.Deleted == true)
                    throw new ApiException("未找到对应服务产品申请信息");
                if (applyData.State != EnumProcessStatus.Submit)
                    throw new ApiException("不能对服务产品申请信息重复审核");
                if (applyData.IsInvalid != EnumIsInvalid.Effective)
                    throw new ApiException("已失效的审核无法进行操作");
                //获取服务表信息
                var serveData = DbOpe_crm_contract_serviceinfo_wits.Instance.GetRegisterInfoByApplId(review_In.WitsApplId);
                if (serveData == null)
                    throw new ApiException("未找到对应服务产品开通信息");
                else if (serveData.State != EnumContractServiceState.TO_BE_REVIEW)
                    throw new ApiException("未找到待复核的服务产品");
                //获取合同信息
                var contractInfo = DbOpe_crm_contract.Instance.QueryByPrimaryKey(applyData.ContractId);
                if (contractInfo == null)
                    throw new ApiException("未找到合同信息");

                //复核驳回
                if (!review_In.Pass)
                {
                    serveData.State = EnumContractServiceState.RETURNED;
                    serveData.ReviewerId = currentUser;
                    serveData.ReviewerDate = today;
                    serveData.ReviewerRemark = review_In.Remark;
                    serveData.Remark = review_In.Remark;
                    serveData.FeedBack = review_In.FeedBack;
                    DbOpe_crm_contract_serviceinfo_wits.Instance.Update(serveData);
                    applyData.Remark4List = review_In.FeedBack;
                    DbOpe_crm_contract_productserviceinfo_wits_appl.Instance.Update(applyData);
                    //如果初审中提交了Gtis服务，进行退回
                    if (serveData.IsGtisAudit)
                        RejectGtisAudit(applyData.Id, review_In.Remark, review_In.FeedBack, today);
                    //如果初审中提交了环球搜服务，进行退回
                    if (serveData.IsGlobalSearchAudit)
                        RejectGlobalSearchAudit(applyData.Id, review_In.GlobalSearchRemark, review_In.FeedBack, today);
                    //如果初审中提交了慧思学院服务，进行退回
                    if (serveData.IsCollegeAudit)
                        RejectCollegeAudit(applyData.Id, review_In.CollegeRemark, review_In.FeedBack, today);
                    //如果初审中提交了慧思学院服务，进行退回
                    if (serveData.IsSalesWitsAudit)
                        RejectSalesWitsAudit(applyData.Id, review_In.SalesWitsRemark, review_In.FeedBack, today);
                }
                else
                {
                    //1.更新所有业务服务的备注内容
                    DbOpe_crm_contract_serviceinfo_wits.Instance.UpdAllServiceRemark(review_In, serveData);
                    //2.执行组合服务开通（传递审核人ID）
                    var retVal = BLL_ServiceOpening.Instance.ExecuteServiceOpening(serveData.Id, false, UserId).Result;
                    //执行内容失败，抛异常错误信息
                    if (!retVal.Success)
                    {
                        throw new ApiException(retVal.ErrorMessage);
                    }
                    //执行成功
                    else
                    {
                        var oriWitsServe = new Db_crm_contract_serviceinfo_wits();
                        if (applyData.ProcessingType == EnumProcessingType.Change)
                        {
                            //原服务信息
                            oriWitsServe = DbOpe_crm_contract_serviceinfo_wits.Instance.QueryByPrimaryKey(applyData.OriWitsServiceId);
                            //如果被变更服务使用服务天数，需要计算是否存在退还个人服务天数
                            if (oriWitsServe != null && oriWitsServe.PerlongServiceDays.GetValueOrDefault(0) > 0)
                                ReturnWitsServicedaysProcessing(oriWitsServe, serveData);
                            //处理原服务数据
                            oriWitsServe.State = (int)EnumContractServiceState.INVALID;
                            oriWitsServe.IsChanged = true;
                            oriWitsServe.ChangedId = serveData.Id;
                            DbOpe_crm_contract_serviceinfo_wits.Instance.UpdateData(oriWitsServe);
                        }
                        else if (contractInfo.ContractType == (int)EnumContractType.ReNew)
                        {
                            //原服务信息
                            oriWitsServe = DbOpe_crm_contract_serviceinfo_wits.Instance.QueryByPrimaryKey(applyData.OriWitsServiceId);
                            //如果被变更服务使用服务天数，需要计算是否存在退还个人服务天数
                            if (oriWitsServe != null && oriWitsServe.PerlongServiceDays.GetValueOrDefault(0) > 0)
                                ReturnWitsServicedaysProcessing(oriWitsServe, serveData);
                            //处理原服务数据
                            if (oriWitsServe.State != EnumContractServiceState.OUT)
                                oriWitsServe.State = EnumContractServiceState.INVALID;
                            DbOpe_crm_contract_serviceinfo_wits.Instance.UpdateData(oriWitsServe);
                        }
                        //申请状态记为通过
                        applyData.State = EnumProcessStatus.Pass;
                        applyData.ReviewerDate = today;
                        applyData.ReviewerId = currentUser;
                        applyData.Feedback = review_In.FeedBack;
                        applyData.Remark4List = review_In.Remark;
                        DbOpe_crm_contract_productserviceinfo_wits_appl.Instance.Update(applyData);
                        //判断开通的服务时间是否是过期的
                        var isExpired = today > serveData.ServiceCycleEnd.Value;
                        //服务表数据标记通过
                        serveData.State = isExpired ? EnumContractServiceState.OUT : EnumContractServiceState.VALID;
                        serveData.ReviewerId = currentUser;
                        serveData.ReviewerDate = today;
                        serveData.ProcessedTime = today;
                        serveData.IsProcessed = (int)EnumGtisServiceIsProcess.Processed;
                        //优惠券标记为已使用
                        if (!string.IsNullOrEmpty(serveData.CounponDetailIds))
                        {
                            if (DbOpe_crm_customer_coupon.Instance.ChangeCouponType(serveData.CounponDetailIds.Split(',').ToList(), EnumCouponType.Used).state != 1)
                                throw new ApiException("优惠券核销失败");
                        }

                        DbOpe_crm_customer_coupon.Instance.UpdateCoupanDeadline(serveData.ContractId, serveData.ServiceCycleEnd.Value);
                        //个人服务天数标记使用
                        if (serveData.PerlongServiceDays.GetValueOrDefault(0) > 0)
                        {
                            BLL_PrivateService.Instance.UsingPrivateServiceForContractService(serveData.ContractId, EnumPrivateServiceType.Used, serveData.WitsApplId, serveData.PrivateServiceDays.Value, serveData.OverServiceDays.Value);
                        }


                        // 更新相关合同产品申请状态为通过
                        ContractProductApplStateHelper.UpdateWitsContractProductApplState(
                            applyData.Id,
                            EnumProcessStatus.Pass,
                            currentUser,
                            null,
                            applyData.ChangeReasonEnums
                            );


                        //更新wits_user表内容
                        var witsUserList = DbOpe_crm_contract_serviceinfo_wits_user.Instance.GetUsersByServelId(serveData.Id);

                        var addUserList = new List<Db_crm_contract_serviceinfo_wits_user>();
                        retVal.GtisResult.UserResults.ForEach(user =>
                        {
                            var witsUser = witsUserList.Find(s => s.Id == user.CrmId);
                            witsUser.SysUserId = user.GtisUserId;
                            witsUser.AccountNumber = user.AccountNumber;
                            witsUser.PassWord = user.Password;
                            witsUser.OpeningStatus = isExpired ? (int)EnumGtisUserOpeningStatus.Expired : (int)EnumGtisUserOpeningStatus.Ok;
                            DbOpe_crm_contract_serviceinfo_wits_user.Instance.UpdateData(witsUser);
                            addUserList.Add(witsUser);
                        });

                        if (serveData.IsGtisAudit)
                        {
                            var curGtisServeId = ReviewGtisAppl(applyData.Id, addUserList, review_In.Remark, review_In.FeedBack, oriWitsServe.CurrentGtisServiceId, contractInfo.ContractType.GetValueOrDefault(0), today);
                            serveData.CurrentGtisServiceId = curGtisServeId;
                        }

                        if (serveData.IsGlobalSearchAudit)
                        {
                            var curGlobalsearchServeId = ReviewGlobalsearchAppl(applyData.Id, retVal.GlobalSearchResult, review_In.GlobalSearchRemark, review_In.FeedBack, oriWitsServe.CurrentGlobalSearchServiceId, contractInfo.ContractType.GetValueOrDefault(0), today);
                            serveData.CurrentGlobalSearchServiceId = curGlobalsearchServeId;
                        }
                        if (serveData.IsCollegeAudit)
                        {
                            var curCollegeServeId = ReviewCollegeAppl(applyData.Id, review_In.CollegeRemark, review_In.FeedBack, oriWitsServe.CurrentCollegeServiceId, contractInfo.ContractType.GetValueOrDefault(0), today);
                            serveData.CurrentCollegeServiceId = curCollegeServeId;
                        }
                        if (serveData.IsSalesWitsAudit)
                        {
                            var curSalesWitsServeId =  ReviewSalesWitsAppl(applyData.Id, review_In.SalesWitsRemark, review_In.FeedBack, oriWitsServe.CurrentSalesWitsServiceId, contractInfo.ContractType.GetValueOrDefault(0), retVal.SaleWitsResult.ResourceManagementId, today);

                            serveData.CurrentSalesWitsServiceId = curSalesWitsServeId;
                        }
                        DbOpe_crm_contract_serviceinfo_wits.Instance.Update(serveData);
                    }
                    if (review_In.Pass)
                        BLL_WorkFlow.Instance.AddWorkFlow("慧思产品(Gtis系列)服务审批流程", applyData.Id, applyData, contractInfo, contractInfo.Issuer, serveData.ReviewerRemark, EnumContractServiceOpenState.Open.GetEnumDescription(), "复核");
                    else
                        BLL_WorkFlow.Instance.AddWorkFlow("慧思产品(Gtis系列)服务审批流程", applyData.Id, serveData, contractInfo, serveData.Remark, EnumContractServiceOpenState.Returned.GetEnumDescription(), "复核");
                }
            });
        }

        #region 各个服务的复核方法
        #region 各个服务的复核驳回方法

        /// <summary>
        /// Gtis复核驳回方法
        /// </summary>
        /// <param name="witsApplId"></param>
        /// <param name="remark">本次备注</param>
        /// <param name="feedBack">审核反馈（驳回时必填）</param>
        /// <param name="today"></param>
        private void RejectGtisAudit(string witsApplId, string remark, string feedBack, DateTime today)
        {
            //Gtis申请数据
            var applData = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.GetGtisApplByWitsId(witsApplId);
            //Gtis服务数据
            var serveData = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetGtisServiceByApplyId(applData.Id);

            serveData.State = (int)EnumContractServiceState.RETURNED;
            serveData.ReviewerId = currentUser;
            serveData.ReviewerDate = today;
            serveData.ReviewerRemark = remark;
            serveData.Remark = remark;
            DbOpe_crm_contract_serviceinfo_gtis.Instance.Update(serveData);
        }

        /// <summary>
        /// 环球搜复核驳回方法
        /// </summary>
        /// <param name="witsApplId"></param>
        /// <param name="remark">本次备注</param>
        /// <param name="feedBack">审核反馈（驳回时必填）</param>
        /// <param name="today"></param>
        private void RejectGlobalSearchAudit(string witsApplId, string remark, string feedBack, DateTime today)
        {
            //环球搜申请数据
            var applData = DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.GetGlobalsearchApplByWitsId(witsApplId);
            //环球搜服务数据
            var serveData = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetSerivceDataByApplyId(applData.Id);

            serveData.State = EnumContractServiceState.RETURNED;
            serveData.ReviewerId = currentUser;
            serveData.ReviewerTime = today;
            serveData.ReviewerRemark = remark;
            serveData.Remark = remark;
            DbOpe_crm_contract_serviceinfo_globalsearch.Instance.Update(serveData);
        }

        /// <summary>
        /// 慧思学院复核驳回方法
        /// </summary>
        /// <param name="witsApplId"></param>
        /// <param name="remark">本次备注</param>
        /// <param name="feedBack">审核反馈（驳回时必填）</param>
        /// <param name="today"></param>
        private void RejectCollegeAudit(string witsApplId, string remark, string feedBack, DateTime today)
        {
            //慧思学院申请数据
            var applData = DbOpe_crm_contract_productserviceinfo_college_appl.Instance.GetCollegeApplByWitsId(witsApplId);
            //慧思学院服务数据
            var serveData = DbOpe_crm_contract_serviceinfo_college.Instance.GetSerivceDataByApplyId(applData.Id);

            serveData.State = EnumContractServiceState.RETURNED;
            serveData.ReviewerId = currentUser;
            serveData.ReviewerTime = today;
            serveData.ReviewerRemark = remark;
            serveData.Remark = remark;
            DbOpe_crm_contract_serviceinfo_college.Instance.Update(serveData);
        }

        /// <summary>
        /// SalesWits复核驳回方法
        /// </summary>
        /// <param name="witsApplId"></param>
        /// <param name="remark">本次备注</param>
        /// <param name="feedBack">审核反馈（驳回时必填）</param>
        /// <param name="today"></param>
        private void RejectSalesWitsAudit(string witsApplId, string remark, string feedBack, DateTime today)
        {
            //慧思学院申请数据
            var applData = DbOpe_crm_contract_productserviceinfo_saleswits_appl.Instance.GetSalesWitsApplByWitsId(witsApplId);
            //慧思学院服务数据
            var serveData = DbOpe_crm_contract_serviceinfo_saleswits.Instance.GetSerivceDataByApplyId(applData.Id);

            serveData.State = EnumContractServiceState.RETURNED;
            serveData.ReviewerId = currentUser;
            serveData.ReviewerTime = today;
            serveData.ReviewerRemark = remark;
            serveData.Remark = remark;
            DbOpe_crm_contract_serviceinfo_saleswits.Instance.Update(serveData);
        }
        #endregion
        #region 各个服务的复核通过方法
        /// <summary>
        /// Gtis复核通过
        /// </summary>
        /// <param name="witsApplId"></param>
        /// <param name="addUserList"></param>
        /// <param name="remark"></param>
        /// <param name="feedBack"></param>
        /// <param name="hisServiceId"></param>
        /// <param name="contractType"></param>
        /// <param name="today"></param>
        private string ReviewGtisAppl(string witsApplId, List<Db_crm_contract_serviceinfo_wits_user> addUserList, string remark, string feedBack, string hisServiceId, int contractType, DateTime today)
        {
            //Gtis申请数据
            var applyData = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.GetGtisApplByWitsId(witsApplId);
            //Gtis服务数据
            var serveData = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetGtisServiceByApplyId(applyData.Id);
            if (applyData.ProcessingType == (int)EnumProcessingType.Change && !string.IsNullOrEmpty(hisServiceId))
            {
                //处理原服务数据
                var hisServeData = DbOpe_crm_contract_serviceinfo_gtis.Instance.QueryByPrimaryKey(hisServiceId);
                hisServeData.State = (int)EnumContractServiceState.INVALID;
                hisServeData.IsChanged = EnumContractServiceInfoIsChange.Change.ToInt();
                hisServeData.ChangedId = serveData.Id;
                DbOpe_crm_contract_serviceinfo_gtis.Instance.UpdateData(hisServeData);
            }
            else if (contractType == (int)EnumContractType.ReNew && !string.IsNullOrEmpty(hisServiceId))
            {
                //原服务信息
                var hisServeData = DbOpe_crm_contract_serviceinfo_wits.Instance.QueryByPrimaryKey(hisServiceId);
                //处理原服务数据
                if (hisServeData.State != EnumContractServiceState.OUT)
                    hisServeData.State = EnumContractServiceState.INVALID;
                DbOpe_crm_contract_serviceinfo_gtis.Instance.UpdateData(hisServeData);
            }
            //更新申请表数据
            applyData.State = (int)EnumProcessStatus.Pass;
            applyData.ReviewerDate = today;
            applyData.ReviewerId = currentUser;
            applyData.Feedback = feedBack;
            applyData.Remark4List = remark;
            DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.Update(applyData);
            //判断开通的服务时间是否是过期的
            var isExpired = today > serveData.ServiceCycleEnd.Value;
            //更新服务表数据
            serveData.State = isExpired ? (int)EnumContractServiceState.OUT : (int)EnumContractServiceState.VALID;
            serveData.ReviewerId = currentUser;
            serveData.ReviewerDate = today;
            serveData.ProcessedTime = today;
            serveData.IsProcessed = (int)EnumGtisServiceIsProcess.Processed;
            DbOpe_crm_contract_serviceinfo_gtis.Instance.Update(serveData);
            //获取账号信息
            addUserList.ForEach(user =>
            {
                if (user.GtisPermission)
                {
                    var gtisUser = user.MappingTo<Db_crm_contract_serviceinfo_gtis_user>();
                    gtisUser.ContractServiceInfoGtisId = serveData.Id;
                    gtisUser.StartDate = serveData.ServiceCycleStart;
                    gtisUser.EndDate = serveData.ServiceCycleEnd;
                    DbOpe_crm_contract_serviceinfo_gtis_user.Instance.InsertData(gtisUser);
                }
            });
            return serveData.Id;
        }
        /// <summary>
        /// 环球搜复核通过
        /// </summary>
        /// <param name="witsApplId"></param>
        /// <param name="result"></param>
        /// <param name="remark"></param>
        /// <param name="feedBack"></param>
        /// <param name="hisServiceId"></param>
        /// <param name="contractType"></param>
        /// <param name="today"></param>
        /// <returns></returns>
        private string ReviewGlobalsearchAppl(string witsApplId, GlobalSearchResult result, string remark, string feedBack, string hisServiceId, int contractType, DateTime today)
        {
            //????检查同步时候环球搜的服务时间是否都存了Ex开头的字段
            //环球搜申请数据
            var applyData = DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.GetGlobalsearchApplByWitsId(witsApplId);
            //环球搜服务数据
            var serveData = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetSerivceDataByApplyId(applyData.Id);
            //声明原服务数据,保存环球搜modify_type时候要用,可以是空
            Db_crm_contract_serviceinfo_globalsearch? hisServeData = new();
            //变更服务，补充Change
            if (applyData.ProcessingType == (int)EnumProcessingType.Change && !string.IsNullOrEmpty(hisServiceId))
            {
                hisServeData = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.QueryByPrimaryKey(hisServiceId);
                hisServeData.State = EnumContractServiceState.INVALID;
                hisServeData.ChangedId = serveData.Id;
                hisServeData.IsChanged = true;
                DbOpe_crm_contract_serviceinfo_globalsearch.Instance.UpdateData(hisServeData);
            }
            //续约服务，需要将老的服务置为作废
            else if (contractType == (int)EnumContractType.ReNew && !string.IsNullOrEmpty(hisServiceId))
            {
                hisServeData = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.QueryByPrimaryKey(hisServiceId);
                hisServeData.State = EnumContractServiceState.INVALID;
                DbOpe_crm_contract_serviceinfo_globalsearch.Instance.UpdateData(hisServeData);
            }

            //更新申请表数据
            applyData.State = (int)EnumProcessStatus.Pass;
            applyData.ReviewerDate = today;
            applyData.ReviewerId = currentUser;
            applyData.Feedback = feedBack;
            applyData.Remark4List = remark;
            DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.Update(applyData);
            //判断开通的服务时间是否是过期的
            var isExpired = today > serveData.ServiceCycleEnd.Value;
            //更新服务表数据
            serveData.State = isExpired ? EnumContractServiceState.OUT : EnumContractServiceState.VALID;
            serveData.ReviewerId = currentUser;
            serveData.ReviewerTime = today;
            serveData.ProcessedTime = today;
            serveData.IsProcessed = (int)EnumGtisServiceIsProcess.Processed;
            DbOpe_crm_contract_serviceinfo_globalsearch.Instance.Update(serveData);
            //记录环球搜的modify_type,新增也记录
            SaveGlobalSearchModifyType(hisServeData, serveData);
            //处理环球搜user表数据
            var globalsearch_user = new Db_crm_contract_serviceinfo_globalsearch_user();
            globalsearch_user.ContractServiceInfoGlobalSearchId = serveData.Id;
            globalsearch_user.ProductServiceInfoGlobalSearchApplId = applyData.Id;
            globalsearch_user.AccountNumber = result.PrimaryCode;
            globalsearch_user.AccountType = EnumContractServiceGlobalSearchAccountType.PrimaryAccount;
            globalsearch_user.OpeningStatus = EnumContractServiceGlobalSearchUserState.Normal;
            globalsearch_user.StartDate = serveData.ServiceCycleStart;
            globalsearch_user.EndDate = serveData.ServiceCycleEnd;
            DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.InsertData(globalsearch_user);

            result.SubCodes.ForEach(subCode =>
            {
                var globalsearch_user = new Db_crm_contract_serviceinfo_globalsearch_user();
                globalsearch_user.ContractServiceInfoGlobalSearchId = serveData.Id;
                globalsearch_user.ProductServiceInfoGlobalSearchApplId = applyData.Id;
                globalsearch_user.AccountNumber = subCode;
                globalsearch_user.AccountType = EnumContractServiceGlobalSearchAccountType.SubAccount;
                globalsearch_user.OpeningStatus = EnumContractServiceGlobalSearchUserState.Normal;
                globalsearch_user.StartDate = serveData.ServiceCycleStart;
                globalsearch_user.EndDate = serveData.ServiceCycleEnd;
                DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.InsertData(globalsearch_user);
            });
            return serveData.Id;
        }
        private string ReviewCollegeAppl(string witsApplId, string remark, string feedBack, string hisServiceId, int contractType, DateTime today)
        {
            //慧思学院申请数据
            var applyData = DbOpe_crm_contract_productserviceinfo_college_appl.Instance.GetCollegeApplByWitsId(witsApplId);
            //慧思学院服务数据
            var serveData = DbOpe_crm_contract_serviceinfo_college.Instance.GetSerivceDataByApplyId(applyData.Id);
            //变更服务，补充ChangeId等数据
            if (applyData.ProcessingType == (int)EnumProcessingType.Change && !string.IsNullOrEmpty(hisServiceId))
            {
                var hisServeData = DbOpe_crm_contract_serviceinfo_college.Instance.QueryByPrimaryKey(hisServiceId);
                hisServeData.State = EnumContractServiceState.INVALID;
                hisServeData.ChangedId = serveData.Id;
                hisServeData.IsChanged = true;
                DbOpe_crm_contract_serviceinfo_college.Instance.UpdateData(hisServeData);
            }
            //续约服务，需要将老的服务置为作废
            else if (contractType == (int)EnumContractType.ReNew && !string.IsNullOrEmpty(hisServiceId))
            {
                var hisServeData = DbOpe_crm_contract_serviceinfo_college.Instance.QueryByPrimaryKey(hisServiceId);
                hisServeData.State = EnumContractServiceState.INVALID;
                DbOpe_crm_contract_serviceinfo_college.Instance.UpdateData(hisServeData);
            }

            //更新申请表数据
            applyData.State = (int)EnumProcessStatus.Pass;
            applyData.ReviewerDate = today;
            applyData.ReviewerId = currentUser;
            applyData.Feedback = feedBack;
            DbOpe_crm_contract_productserviceinfo_college_appl.Instance.Update(applyData);
           
            //判断开通的服务时间是否是过期的
            var isExpired = today > serveData.ServiceCycleEnd.Value;
            //更新服务表数据
            serveData.State = isExpired ? EnumContractServiceState.OUT : EnumContractServiceState.VALID;
            serveData.ReviewerId = currentUser;
            serveData.ReviewerTime = today;
            serveData.ProcessedTime = today;
            serveData.IsProcessed = (int)EnumGtisServiceIsProcess.Processed;
            DbOpe_crm_contract_serviceinfo_college.Instance.Update(serveData);

            return serveData.Id;
        }
        /// <summary>
        /// SalesWits服务复核通过
        /// </summary>
        /// <param name="witsApplId"></param>
        /// <param name="remark"></param>
        /// <param name="feedBack"></param>
        /// <param name="hisServiceId"></param>
        /// <param name="contractType"></param>
        /// <param name="resourceManagementId"></param>
        /// <param name="today"></param>
        private string ReviewSalesWitsAppl(string witsApplId, string remark, string feedBack, string hisServiceId, int contractType, string resourceManagementId, DateTime today)
        {
            //SalesWits申请数据
            var applyData = DbOpe_crm_contract_productserviceinfo_saleswits_appl.Instance.GetSalesWitsApplByWitsId(witsApplId);
            //SalesWits服务数据
            var serveData = DbOpe_crm_contract_serviceinfo_saleswits.Instance.GetSerivceDataByApplyId(applyData.Id);
            //变更服务，补充ChangeId等数据
            if (applyData.ProcessingType == (int)EnumProcessingType.Change && !string.IsNullOrEmpty(hisServiceId))
            {
                var hisServeData = DbOpe_crm_contract_serviceinfo_saleswits.Instance.QueryByPrimaryKey(hisServiceId);
                hisServeData.State = EnumContractServiceState.INVALID;
                hisServeData.ChangedId = serveData.Id;
                hisServeData.IsChanged = true;
                DbOpe_crm_contract_serviceinfo_saleswits.Instance.UpdateData(hisServeData);
            }
            //续约服务，需要将老的服务置为作废
            else if (contractType == (int)EnumContractType.ReNew && !string.IsNullOrEmpty(hisServiceId))
            {
                var hisServeData = DbOpe_crm_contract_serviceinfo_college.Instance.QueryByPrimaryKey(hisServiceId);
                hisServeData.State = EnumContractServiceState.INVALID;
                DbOpe_crm_contract_serviceinfo_saleswits.Instance.UpdateData(hisServeData);
            }

            //更新申请表数据
            applyData.State = (int)EnumProcessStatus.Pass;
            applyData.ReviewerDate = today;
            applyData.ReviewerId = currentUser;
            applyData.Feedback = feedBack;
            DbOpe_crm_contract_productserviceinfo_saleswits_appl.Instance.Update(applyData);
            
            //判断开通的服务时间是否是过期的
            var isExpired = today > serveData.ServiceCycleEnd.Value;
            //更新服务表数据
            serveData.State = isExpired ? EnumContractServiceState.OUT : EnumContractServiceState.VALID;
            serveData.ReviewerId = currentUser;
            serveData.ReviewerTime = today;
            serveData.ProcessedTime = today;
            serveData.IsProcessed = (int)EnumGtisServiceIsProcess.Processed;
            serveData.ResourceManagementId = resourceManagementId;
            DbOpe_crm_contract_serviceinfo_saleswits.Instance.Update(serveData);

            return serveData.Id;
        }
        #endregion
        #endregion

        /// <summary>
        /// 处理服务天数退还
        /// </summary>
        /// <param name="oriWitsServe"></param>
        /// <param name="curWitsServe"></param>
        private void ReturnWitsServicedaysProcessing(Db_crm_contract_serviceinfo_wits oriWitsServe, Db_crm_contract_serviceinfo_wits curWitsServe)
        {
            //当前的服务的最晚到账日期
            var lastArriveDate = DbOpe_crm_contract_receiptregister_service.Instance.GetLastArriveDateByServiceId(curWitsServe.Id);//查找到账的最晚日期
            var oldWitsReviewDate = oriWitsServe.ReviewerDate;//被变更服务的复核通过时间
            /* 需求727关于到账时间对返还服务天数影响的原描述： 触发返还的服务对应的到账时间，必须在使用个人服务天数生效时间的次月内*/
            //若最晚到账时间符合规则(最晚到账时间早于被变更服务开通时间次月最后一天24点) && 查看当前的服务时间和被变更的服务时间存在重叠  处理服务天数退还，优先退超额服务天数
            if (lastArriveDate != null && oldWitsReviewDate != null
                && lastArriveDate < oldWitsReviewDate.Value.AddMonths(2).AddDays(-oldWitsReviewDate.Value.Day + 1).Date
                && oriWitsServe.ServiceCycleEnd.Value > curWitsServe.ServiceCycleStart.Value)
            {

                //防止int赋值报错，如果存在空值先赋0
                oriWitsServe.OverServiceDays = oriWitsServe.OverServiceDays.GetValueOrDefault(0);
                oriWitsServe.PrivateServiceDays = oriWitsServe.PrivateServiceDays.GetValueOrDefault(0);

                //计算服务重叠天数
                var tobeReturnedDays = (oriWitsServe.ServiceCycleEnd.Value - curWitsServe.ServiceCycleStart.Value).TotalDays.ToInt();
                //定义待退回的超额服务天数和个人服务天数
                int tobeReturned_OverServDays = 0, tobeReturned_PriServDays = 0;

                /*优先计算超额服务天数*/
                if (tobeReturnedDays > 0 && oriWitsServe.OverServiceDays > 0 && oriWitsServe.OverServiceDays >= tobeReturnedDays)
                {//旧服务使用超额服务天数 && 使用数量大于或等于服务重叠天数
                    tobeReturned_OverServDays = tobeReturnedDays;//待退回的超额服务天数为总重叠天数
                    oriWitsServe.OverServiceDays -= tobeReturnedDays;//重新计算旧服务的超额服务天数
                    tobeReturnedDays = 0;
                }
                else if (tobeReturnedDays > 0 && oriWitsServe.OverServiceDays > 0 && oriWitsServe.OverServiceDays < tobeReturnedDays)
                {//旧服务使用超额服务天数 && 且使用数量小于服务重叠天数
                    tobeReturned_OverServDays = oriWitsServe.OverServiceDays.Value;//待退回的超额服务天数为旧服务的超额服务天数
                    oriWitsServe.OverServiceDays = 0;//旧服务的超额服务天数归零
                    tobeReturnedDays -= tobeReturned_OverServDays;
                }

                /*计算个人服务天数*/
                if (tobeReturnedDays > 0 && oriWitsServe.PrivateServiceDays > 0 && oriWitsServe.PrivateServiceDays >= tobeReturnedDays)
                {//此时服务重叠天数大于0 && 旧服务使用个人服务天数 && 使用数量大于或等于服务重叠天数
                    tobeReturned_PriServDays = tobeReturnedDays;//待退回的个人服务天数为总重叠天数
                    oriWitsServe.PrivateServiceDays -= tobeReturnedDays;//重新计算旧服务的个人服务天数
                }
                else if (tobeReturnedDays > 0 && oriWitsServe.PrivateServiceDays > 0 && oriWitsServe.PrivateServiceDays < tobeReturnedDays)
                {//此时服务重叠天数大于0 && 旧服务使用个人服务天数 && 使用数量小于服务重叠天数
                    tobeReturned_PriServDays = oriWitsServe.PrivateServiceDays.Value;//待退回的个人服务天数为旧服务的超额服务天数
                    oriWitsServe.PrivateServiceDays = 0;//旧服务的个人服务天数归零
                }

                //将超额天数和个人天数进行登记退还
                BLL_PrivateService.Instance.UsingPrivateServiceForContractService(oriWitsServe.ContractId, EnumPrivateServiceType.GiveBacking, oriWitsServe.WitsApplId, tobeReturned_PriServDays, tobeReturned_OverServDays);

                //DbOpe_crm_contract_serviceinfo_wits.Instance.UpdateData(oriWitsServe);
            }
        }

        /// <summary>
        /// 保存环球搜的ModifyType
        /// </summary>
        /// <param name="hisServeData"></param>
        /// <param name="curServeData"></param>
        private void SaveGlobalSearchModifyType(Db_crm_contract_serviceinfo_globalsearch? hisServeData, Db_crm_contract_serviceinfo_globalsearch curServeData)
        {
            //如果没有历史的服务信息，就默认为新增类型
            if (hisServeData == null)
            {
                var modifyTypeData = new Db_crm_contract_serviceinfo_globalsearch_modifytype();
                modifyTypeData.ContractServiceInfoGlobalSearchId = curServeData.Id;
                modifyTypeData.ProductServiceInfoGlobalSearchApplId = curServeData.ProductServiceInfoGlobalSearchApplId;
                modifyTypeData.ContractNum = curServeData.ContractNum;
                modifyTypeData.ModifyType = EnumGlobalSearchModifyType.NewService;
                DbOpe_crm_contract_serviceinfo_globalsearch_modifytype.Instance.InsertData(modifyTypeData);
            }
            else
            {
                var changeItem = new List<EnumGlobalSearchModifyType>();

                //从旧Gtis服务中获取旧环球搜的账号数及服务开始结束时间
                var hisAccountNum = hisServeData.PrimaryAccountsNum.GetValueOrDefault(0) + hisServeData.SubAccountsNum.GetValueOrDefault(0);
                var hisServiceStart = hisServeData.ExecuteServiceCycleStart == null ? hisServeData.ServiceCycleStart : hisServeData.ExecuteServiceCycleStart;
                var hisServiceEnd = hisServeData.ExecuteServiceCycleEnd == null ? hisServeData.ServiceCycleEnd : hisServeData.ExecuteServiceCycleEnd;
                //如果不存在有效的服务内容，视为新增服务
                if (hisAccountNum == null || hisServiceStart == null || hisServiceEnd == null)
                {
                    changeItem.Add(EnumGlobalSearchModifyType.NewService);
                }
                else
                {
                    //判断账号数是否一致
                    if ((curServeData.PrimaryAccountsNum.GetValueOrDefault(0) + curServeData.SubAccountsNum.GetValueOrDefault(0)) != hisAccountNum)
                        changeItem.Add(EnumGlobalSearchModifyType.ChangeCode);
                    //判断服务时间是否一致
                    if (curServeData.ServiceCycleStart != hisServiceStart || curServeData.ServiceCycleEnd != hisServiceEnd)
                        changeItem.Add(EnumGlobalSearchModifyType.ChangeTime);
                    //账号数和服务时间均一致，标记为未变更
                    if (changeItem.Count == 0)
                        changeItem.Add(EnumGlobalSearchModifyType.Unchanged);
                }
                //插入服务修改类型数据
                foreach (var item in changeItem)
                {
                    var modifyTypeData = new Db_crm_contract_serviceinfo_globalsearch_modifytype();
                    modifyTypeData.ContractServiceInfoGlobalSearchId = curServeData.Id;
                    modifyTypeData.ProductServiceInfoGlobalSearchApplId = curServeData.ProductServiceInfoGlobalSearchApplId;
                    modifyTypeData.ContractNum = curServeData.ContractNum;
                    modifyTypeData.ModifyType = item;
                    DbOpe_crm_contract_serviceinfo_globalsearch_modifytype.Instance.InsertData(modifyTypeData);
                }

            }

        }

        #region 变更原因解析辅助方法

        /// <summary>
        /// 解析产品特定的变更原因
        /// </summary>
        /// <param name="changeReasonEnums">变更原因字符串</param>
        /// <param name="targetProductId">目标产品ID</param>
        /// <returns>该产品的变更原因列表</returns>
        private List<EnumGtisServiceChangeProject> ParseProductSpecificChangeReasons(
            string changeReasonEnums, 
            string targetProductId)
        {
            if (string.IsNullOrEmpty(changeReasonEnums)) 
                return new List<EnumGtisServiceChangeProject>();
            
            var result = new List<EnumGtisServiceChangeProject>();
            
            try
            {
                // 尝试解析JSON格式
                var changeReasonData = System.Text.Json.JsonSerializer.Deserialize<System.Text.Json.JsonElement>(changeReasonEnums);
                if (changeReasonData.ValueKind != System.Text.Json.JsonValueKind.Null && 
                    changeReasonData.TryGetProperty("ChangeReasons", out var changeReasonsElement) &&
                    changeReasonsElement.ValueKind == System.Text.Json.JsonValueKind.Array &&
                    changeReasonsElement.GetArrayLength() > 0)
                {
                    foreach (var reason in changeReasonsElement.EnumerateArray())
                    {
                        if (reason.TryGetProperty("ChangeReason", out var changeReasonElement) &&
                            reason.TryGetProperty("ContractProductId", out var contractProductIdElement))
                        {
                            var changeReason = changeReasonElement.GetInt32();
                            var contractProductId = contractProductIdElement.GetString();
                            
                            if (contractProductId == targetProductId)
                            {
                                result.Add(changeReason.ToEnum<EnumGtisServiceChangeProject>());
                            }
                        }
                    }
                }
            }
            catch (System.Text.Json.JsonException)
            {
                // JSON解析失败，尝试解析旧格式
                result = changeReasonEnums.Split(',')
                    .Where(r => 
                    {
                        // 兼容旧格式（纯数字）和新格式（数字_产品ID）
                        if (!r.Contains("_"))
                            return true; // 旧格式，全部包含（为了兼容性）
                        else
                            return r.EndsWith($"_{targetProductId}"); // 新格式，只包含匹配的产品
                    })
                    .Select(r => r.Contains("_") ? r.Split('_')[0] : r)
                    .Where(r => int.TryParse(r, out _)) // 确保是有效的数字
                    .Select(r => r.ToEnum<EnumGtisServiceChangeProject>())
                    .ToList();
            }
            
            return result;
        }

        /// <summary>
        /// 检查特定产品是否包含指定的变更原因
        /// </summary>
        /// <param name="changeReasonEnums">变更原因字符串</param>
        /// <param name="targetProductId">目标产品ID</param>
        /// <param name="targetReason">要检查的变更原因</param>
        /// <returns>是否包含该变更原因</returns>
        private bool HasProductSpecificChangeReason(
            string changeReasonEnums, 
            string targetProductId, 
            EnumGtisServiceChangeProject targetReason)
        {
            var reasons = ParseProductSpecificChangeReasons(changeReasonEnums, targetProductId);
            return reasons.Contains(targetReason);
        }

        #endregion
    }
}

