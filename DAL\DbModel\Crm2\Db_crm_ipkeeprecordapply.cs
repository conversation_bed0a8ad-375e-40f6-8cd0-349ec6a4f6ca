﻿using System;
using System.Linq;
using System.Text;
using CRM2_API.Model.BLLModel.Enum;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///IP备案申请表
    ///</summary>
    [SugarTable("crm_ipkeeprecordapply")]
    public class Db_crm_ipkeeprecordapply
    {
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string Id {get;set;}

           /// <summary>
           /// Desc:备案表Id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string IPKeepRecordId {get;set;}

           /// <summary>
           /// Desc:是否新建，否则为更新
           /// Default:
           /// Nullable:True
           /// </summary>           
           public bool? IsNewBuild {get;set;}

           /// <summary>
           /// Desc:申请人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ApplicantId {get;set;}

           /// <summary>
           /// Desc:申请时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? ApplicantDate {get;set;}

           /// <summary>
           /// Desc:申领备注
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string Remark {get;set;}

           /// <summary>
           /// Desc:审核人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ReviewerId {get;set;}

           /// <summary>
           /// Desc:审核时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? ReviewerDate {get;set;}

           /// <summary>
           /// Desc:审核状态
           /// Default:
           /// Nullable:True
           /// </summary>           
           public EnumRecordState? State { get; set; }

           /// <summary>
           /// Desc:审核反馈
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string Feedback {get;set;}

           /// <summary>
           /// Desc:有效截止日
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? ExpirationDate {get;set;}

           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:True
           /// </summary>           
           public bool? Deleted {get;set;}

           /// <summary>
           /// Desc:创建人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CreateUser {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? CreateDate {get;set;}

           /// <summary>
           /// Desc:修改人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UpdateUser {get;set;}

           /// <summary>
           /// Desc:更新时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? UpdateDate {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           [SugarColumn(IsEnableUpdateVersionValidation = true)]
           public string? Version {get;set;}

    }
}
