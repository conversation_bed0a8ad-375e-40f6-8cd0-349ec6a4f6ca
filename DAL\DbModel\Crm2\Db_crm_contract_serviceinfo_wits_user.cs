using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    /// <summary>
    /// 合同服务信息_Gtis系列产品_用户表
    /// </summary>
    [SugarTable("crm_contract_serviceinfo_wits_user")]
    public partial class Db_crm_contract_serviceinfo_wits_user
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// Wits服务表id
        /// </summary>
        public string WitsServeId { get; set; }

        /// <summary>
        /// 账号Id
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        /// 账号
        /// </summary>
        public string AccountNumber { get; set; }

        /// <summary>
        /// 密码
        /// </summary>
        public string PassWord { get; set; }

        /// <summary>
        /// 账号类型
        /// </summary>
        public int? AccountType { get; set; }

        /// <summary>
        /// 开通状态
        /// </summary>
        public int? OpeningStatus { get; set; }

        /// <summary>
        /// 启用日期
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 停用日期
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// 登录IP
        /// </summary>
        public string LoginIP { get; set; }

        /// <summary>
        /// 最近登录时间
        /// </summary>
        public DateTime? LastLoginTime { get; set; }

        /// <summary>
        /// Desc:使用者
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string PhoneUser { get; set; }

        /// <summary>
        /// Desc:使用者数量
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? PhoneUserNum { get; set; }

        /// <summary>
        /// 每个账户共享人数
        /// </summary>
        public int? SharePeopleNum { get; set; }

        /// <summary>
        /// 子账号授权国家次数
        /// </summary>
        public int? AuthorizationNum { get; set; }

        /// <summary>
        /// Gtis权限
        /// </summary>
        public bool GtisPermission { get; set; }

        /// <summary>
        /// Vip零售国家权限
        /// </summary>
        public bool VipPermission { get; set; }

        /// <summary>
        /// 环球搜权限
        /// </summary>
        public bool GlobalSearchPermission { get; set; }
        /// <summary>
        /// Desc:环球搜码
        /// Default:
        /// Nullable:True
        /// </summary>        
        public string GlobalSearchCode { get; set; }
        /// <summary>
        /// 学院权限
        /// </summary>
        public bool CollegePermission { get; set; }

        /// <summary>
        /// SalesWits权限
        /// </summary>
        public bool SalesWitsPermission { get; set; }

        /// <summary>
        /// Desc:SalesWits使用者Id
        /// Default:
        /// Nullable:True
        /// </summary>
        public string SalesWitsBindPhoneId { get; set; }

        /// <summary>
        /// 自动分配全部国家的子账号
        /// </summary>
        public bool? AllCountrySubUser { get; set; }

        /// <summary>
        /// 删除标识
        /// </summary>
        public bool? Deleted { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string CreateUser { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateDate { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string UpdateUser { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateDate { get; set; }
    }
} 