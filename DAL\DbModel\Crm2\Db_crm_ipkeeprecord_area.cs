﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///IP备案_地区表
    ///</summary>
    [SugarTable("crm_ipkeeprecord_area")]
    public class Db_crm_ipkeeprecord_area
    {
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string Id {get;set;}

           /// <summary>
           /// Desc:备案_账号地区表Id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string IPKeepRecordUserAndAreaId {get;set;}

           /// <summary>
           /// Desc:国家Id，冗余减少连表数
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? CountryId {get;set;}

           /// <summary>
           /// Desc:地区
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? AreaId {get;set;}

           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:True
           /// </summary>           
           public bool? Deleted {get;set;}

           /// <summary>
           /// Desc:排序码
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? SortNum {get;set;}

           /// <summary>
           /// Desc:创建人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CreateUser {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? CreateDate {get;set;}

           /// <summary>
           /// Desc:修改人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UpdateUser {get;set;}

           /// <summary>
           /// Desc:更新时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? UpdateDate {get;set;}

    }
}
