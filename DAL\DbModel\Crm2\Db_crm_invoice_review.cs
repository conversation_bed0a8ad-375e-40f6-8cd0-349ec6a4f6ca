using System;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    /// <summary>
    /// 发票审核表 - 存储经办人处理和审核过程中的信息
    /// </summary>
    [SugarTable("crm_invoice_review")]
    public class Db_crm_invoice_review
    {
        /// <summary>
        /// 审核ID
        /// </summary>
        [SugarColumn(IsPrimaryKey=true)]
        public string Id { get; set; }
        
        /// <summary>
        /// 关联的申请ID
        /// </summary>
        public string InvoiceApplicationId { get; set; }
        
        /// <summary>
        /// 合同ID
        /// </summary>
        public string ContractId { get; set; }
        
        /// <summary>
        /// 发票类型 (1=普票, 2=专票, 3=形式发票)
        /// </summary>
        public int InvoiceType { get; set; }
        
        /// <summary>
        /// 发票号码 
        /// </summary>
        public string InvoiceNumber { get; set; }
        
        /// <summary>
        /// 开票日期 
        /// </summary>
        public DateTime? InvoicingDate { get; set; }
        
        /// <summary>
        /// 开票金额 
        /// </summary>
        public decimal InvoicedAmount { get; set; }
        
        /// <summary>
        /// 开票公司 
        /// </summary>
        public string BillingCompany { get; set; }
        
        /// <summary>
        /// 开票抬头 
        /// </summary>
        public string BillingHeader { get; set; }
        
        /// <summary>
        /// 信用代码 
        /// </summary>
        public string CreditCode { get; set; }
        
        /// <summary>
        /// 是否个人开票
        /// </summary>
        public bool IsPersonalInvoice { get; set; }
        
        /// <summary>
        /// 开票明细 
        /// </summary>
        public string InvoicingDetails { get; set; }
        
        /// <summary>
        /// 审核状态
        /// </summary>
        public int AuditStatus { get; set; }
        
        /// <summary>
        /// 处理人ID
        /// </summary>
        public string ProcessorId { get; set; }
        
        /// <summary>
        /// 处理时间
        /// </summary>
        public DateTime? ProcessTime { get; set; }
        
        /// <summary>
        /// 审核人ID
        /// </summary>
        public string AuditorId { get; set; }
        
        /// <summary>
        /// 审核时间
        /// </summary>
        public DateTime? AuditTime { get; set; }
        
        /// <summary>
        /// 审核反馈
        /// </summary>
        public string AuditFeedback { get; set; }
        
        /// <summary>
        /// 经办人备注
        /// </summary>
        public string ProcessorRemark { get; set; }
        
        /// <summary>
        /// 修改的字段(JSON格式)
        /// </summary>
        public string ModifiedFields { get; set; }
        
        /// <summary>
        /// 删除标记
        /// </summary>
        public bool? Deleted { get; set; }
        
        /// <summary>
        /// 标准审计字段
        /// </summary>
        public string CreateUser { get; set; }
        public DateTime? CreateDate { get; set; }
        public string UpdateUser { get; set; }
        public DateTime? UpdateDate { get; set; }
    }
} 