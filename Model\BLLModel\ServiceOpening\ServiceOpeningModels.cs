using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using System;
using System.Collections.Generic;

namespace CRM2_API.Model.BLLModel.ServiceOpening
{
    #region 服务开通参数模型
    
    /// <summary>
    /// 服务开通参数
    /// </summary>
    public class ServiceOpeningParams
    {
        /// <summary>
        /// 主服务信息
        /// </summary>
        public Db_crm_contract_serviceinfo_wits MainService { get; set; }
        
        /// <summary>
        /// 合同信息
        /// </summary>
        public Db_crm_contract Contract { get; set; }
        
        /// <summary>
        /// 用户权限配置列表
        /// </summary>
        public List<Db_crm_contract_serviceinfo_wits_user> Users { get; set; }

        /// <summary>
        /// 开通类型
        /// </summary>
        public ServiceOpeningType OpeningType { get; set; }

        /// <summary>
        /// 拥有的所有服务权限
        /// </summary>
        public List<string> Apps
        {
            get
            {
                var _apps = new List<string>();
                if(MainService != null)
                {
                    if(MainService.HasGtisApp == true)
                    {
                        _apps.Add("Gtis6");
                    }
                    if(MainService.HasGlobalSearchApp == true)
                    {
                        _apps.Add("HQS");
                    }
                    if(MainService.HasSalesWitsApp == true)
                    {
                        _apps.Add("CRM");
                    }
                    if(MainService.HasCollegeApp == true)
                    {
                        _apps.Add("College");
                    }
                }
                return _apps;
            }
        }
        
        #region 各服务基础配置（从Wits主服务表读取）

        /// <summary>
        /// GTIS服务基础配置
        /// </summary>
        public GtisBasicConfig GtisBasicConfig { get; set; }

        /// <summary>
        /// 环球搜服务基础配置
        /// </summary>
        public GlobalSearchBasicConfig GlobalSearchBasicConfig { get; set; }

        /// <summary>
        /// SalesWits服务基础配置
        /// </summary>
        public SalesWitsBasicConfig SalesWitsBasicConfig { get; set; }

        /// <summary>
        /// 慧思学院服务基础配置
        /// </summary>
        public CollegeBasicConfig CollegeBasicConfig { get; set; }

        #endregion

        #region 各服务特殊配置（从各服务专用表读取）

        /// <summary>
        /// GTIS服务特殊配置
        /// </summary>
        public GtisSpecialConfig GtisSpecialConfig { get; set; }

        /// <summary>
        /// 环球搜服务特殊配置
        /// </summary>
        public GlobalSearchSpecialConfig GlobalSearchSpecialConfig { get; set; }

        /// <summary>
        /// SalesWits服务特殊配置
        /// </summary>
        public SalesWitsSpecialConfig SalesWitsSpecialConfig { get; set; }

        /// <summary>
        /// 慧思学院服务特殊配置
        /// </summary>
        public CollegeSpecialConfig CollegeSpecialConfig { get; set; }

        /// <summary>
        /// 环球搜预处理结果
        /// </summary>
        public GlobalSearchPreprocessResult GlobalSearchPreprocessResult { get; set; }

        /// <summary>
        /// GTIS预处理结果
        /// </summary>
        public GtisPreprocessResult GtisPreprocessResult { get; set; }

        #endregion
        
        #region GTIS相关权限配置
        
        /// <summary>
        /// GTIS常驻国家列表
        /// </summary>
        public List<int> GtisCountries { get; set; }
        
        /// <summary>
        /// GTIS常驻城市列表
        /// </summary>
        public List<int> GtisCities { get; set; }
        
        /// <summary>
        /// GTIS相关国家SID配置
        /// </summary>
        public int[] GtisSids { get; set; }
        
        /// <summary>
        /// GTIS需要移除的国家SID列表
        /// </summary>
        public int[] GtisSidsRemove { get; set; }
        
        #endregion
        
        #region 验证相关
        
        /// <summary>
        /// 是否有效
        /// </summary>
        public bool IsValid { get; private set; }
        
        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; private set; }
        
        /// <summary>
        /// 设置为有效
        /// </summary>
        public void SetValid()
        {
            IsValid = true;
            ErrorMessage = "";
        }
        
        /// <summary>
        /// 设置错误
        /// </summary>
        /// <param name="errorMessage">错误信息</param>
        public void SetError(string errorMessage)
        {
            IsValid = false;
            ErrorMessage = errorMessage;
        }
        
        #endregion
        
        #region 测试模式
/// <summary>
/// 是否为测试模式
/// </summary>
public bool IsTestMode { get; set; }

        #endregion

        #region 操作人信息

        /// <summary>
        /// 操作人ID
        /// </summary>
        public string OperatorId { get; set; }

        /// <summary>
        /// 操作人名称
        /// </summary>
        public string OperatorName { get; set; }

        #endregion
    }
    
    #endregion
    
    #region 服务开通结果模型
    
    /// <summary>
    /// 服务开通结果
    /// </summary>
    public class ServiceOpeningResult
    {
        /// <summary>
        /// 服务ID（crm_contract_serviceinfo_wits表ID）
        /// </summary>
        public string ServiceId { get; set; }
        
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }
        
        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; }
        
        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }
        
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }
        
        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime EndTime { get; set; }
        
        /// <summary>
        /// 耗时
        /// </summary>
        public TimeSpan Duration { get; set; }
        
        #region 各服务开通结果
        
        /// <summary>
        /// 环球搜开通结果
        /// </summary>
        public GlobalSearchResult GlobalSearchResult { get; set; }
        
        /// <summary>
        /// GTIS开通结果
        /// </summary>
        public GtisResult GtisResult { get; set; }
        
        /// <summary>
        /// SaleWits开通结果
        /// </summary>
        public SaleWitsResult SaleWitsResult { get; set; }
        
        /// <summary>
        /// 慧思学院开通结果
        /// </summary>
        public CollegeResult CollegeResult { get; set; }
        
        #endregion
        
        /// <summary>
        /// 设置成功
        /// </summary>
        /// <param name="message">成功消息</param>
        public void SetSuccess(string message)
        {
            Success = true;
            Message = message;
            ErrorMessage = "";
        }
        
        /// <summary>
        /// 设置失败
        /// </summary>
        /// <param name="errorMessage">错误消息</param>
        public void SetError(string errorMessage)
        {
            Success = false;
            ErrorMessage = errorMessage;
            Message = "";
        }
    }
    
    /// <summary>
    /// 环球搜开通结果
    /// </summary>
    public class GlobalSearchResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }
        
        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; }
        
        /// <summary>
        /// 主环球搜码
        /// </summary>
        public string PrimaryCode { get; set; }
        
        /// <summary>
        /// 子环球搜码列表
        /// </summary>
        public List<string> SubCodes { get; set; }
        
        /// <summary>
        /// 所有环球搜码（主码+子码）
        /// </summary>
        public List<string> AllCodes
        {
            get
            {
                var allCodes = new List<string>();
                if (!string.IsNullOrEmpty(PrimaryCode))
                    allCodes.Add(PrimaryCode);
                if (SubCodes != null)
                    allCodes.AddRange(SubCodes);
                return allCodes;
            }
        }


        
        /// <summary>
        /// 设置成功
        /// </summary>
        /// <param name="message">成功消息</param>
        public void SetSuccess(string message)
        {
            Success = true;
            Message = message;
        }
        
        /// <summary>
        /// 设置失败
        /// </summary>
        /// <param name="errorMessage">错误消息</param>
        public void SetError(string errorMessage)
        {
            Success = false;
            Message = errorMessage;
        }
    }
    
    /// <summary>
    /// GTIS开通结果
    /// </summary>
    public class GtisResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }
        
        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; }
        
        /// <summary>
        /// GTIS账号
        /// </summary>
        public string GtisAccount { get; set; }
        
        /// <summary>
        /// 租户ID（用于SaleWits）
        /// </summary>
        public string TenantId { get; set; }
        
        /// <summary>
        /// 所有新建用户结果列表（需要后续写入密码到gtisuser表）
        /// 包括：新建场景的所有用户 + 续约/变更场景中的新增用户
        /// </summary>
        public List<GtisUserResult> UserResults { get; set; }


        
        /// <summary>
        /// 设置成功
        /// </summary>
        /// <param name="message">成功消息</param>
        public void SetSuccess(string message)
        {
            Success = true;
            Message = message;
        }
        
        /// <summary>
        /// 设置失败
        /// </summary>
        /// <param name="errorMessage">错误消息</param>
        public void SetError(string errorMessage)
        {
            Success = false;
            Message = errorMessage;
        }
    }
    
    /// <summary>
    /// GTIS用户开通结果
    /// </summary>
    public class GtisUserResult
    {
        /// <summary>
        /// CRM用户ID
        /// </summary>
        public string CrmId { get; set; }
        
        /// <summary>
        /// 原始用户ID
        /// </summary>
        public string OriginalUserId { get; set; }
        
        /// <summary>
        /// GTIS账号
        /// </summary>
        public string AccountNumber { get; set; }
        
        /// <summary>
        /// GTIS系统用户ID
        /// </summary>
        public string GtisUserId { get; set; }
        
        /// <summary>
        /// 密码
        /// </summary>
        public string Password { get; set; }
        
        /// <summary>
        /// 账号类型
        /// </summary>
        public int? AccountType { get; set; }
        
        /// <summary>
        /// 共享人数
        /// </summary>
        public int? SharePeopleNum { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }
        
        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; }
    }
    

    
    /// <summary>
    /// SaleWits开通结果
    /// </summary>
    public class SaleWitsResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }
        
        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; }
        
        /// <summary>
        /// 返回代码
        /// </summary>
        public int Code { get; set; }

        /// <summary>
        /// 下发记录ID
        /// </summary>
        public string DistributionId { get; set; }

        /// <summary>
        /// 下发时间
        /// </summary>
        public DateTime? DistributionTime { get; set; }

        /// <summary>
        /// 资源管理ID
        /// </summary>
        public string ResourceManagementId { get; set; }


        
        /// <summary>
        /// 设置成功
        /// </summary>
        /// <param name="message">成功消息</param>
        public void SetSuccess(string message)
        {
            Success = true;
            Message = message;
        }
        
        /// <summary>
        /// 设置失败
        /// </summary>
        /// <param name="errorMessage">错误消息</param>
        public void SetError(string errorMessage)
        {
            Success = false;
            Message = errorMessage;
        }
    }
    
    /// <summary>
    /// 慧思学院开通结果
    /// </summary>
    public class CollegeResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }
        
        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 更新的用户数量
        /// </summary>
        public int UpdatedUsersCount { get; set; }

        /// <summary>
        /// 配置信息
        /// </summary>
        public string ConfigurationInfo { get; set; }


        
        /// <summary>
        /// 设置成功
        /// </summary>
        /// <param name="message">成功消息</param>
        public void SetSuccess(string message)
        {
            Success = true;
            Message = message;
        }
        
        /// <summary>
        /// 设置失败
        /// </summary>
        /// <param name="errorMessage">错误消息</param>
        public void SetError(string errorMessage)
        {
            Success = false;
            Message = errorMessage;
        }
    }

    /// <summary>
    /// 慧思学院权限更新结果
    /// </summary>
    public class CollegePermissionUpdateResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 成功消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 更新的用户数量
        /// </summary>
        public int UpdatedUsersCount { get; set; }
    }

    #endregion
    
    #region 枚举
    
    /// <summary>
    /// 服务开通类型
    /// </summary>
    public enum ServiceOpeningType
    {
        /// <summary>
        /// 新开账号
        /// </summary>
        NewAccount = 1,
        
        /// <summary>
        /// 续约
        /// </summary>
        Renewal = 2,
        
        /// <summary>
        /// 变更
        /// </summary>
        Change = 3
    }

    #endregion

    #region 环球搜预处理结果

    /// <summary>
    /// 环球搜预处理结果
    /// </summary>
    public class GlobalSearchPreprocessResult
    {
        /// <summary>
        /// 是否存在现有环球搜服务
        /// </summary>
        public bool HasExistingService { get; set; }

        /// <summary>
        /// 现有环球搜主账号码
        /// </summary>
        public string ExistingPrimaryCode { get; set; }

        /// <summary>
        /// 现有环球搜子账号码列表
        /// </summary>
        public List<string> ExistingSubCodes { get; set; }

        /// <summary>
        /// 现有账号总数量
        /// </summary>
        public int ExistingAccountCount { get; set; }

        /// <summary>
        /// 后续操作类型
        /// </summary>
        public GlobalSearchOperationType OperationType { get; set; }

        /// <summary>
        /// 现有环球搜账号详细信息
        /// </summary>
        public List<Db_crm_contract_serviceinfo_globalsearch_user> ExistingAccounts { get; set; }

        public GlobalSearchPreprocessResult()
        {
            ExistingSubCodes = new List<string>();
            ExistingAccounts = new List<Db_crm_contract_serviceinfo_globalsearch_user>();
        }
    }

    /// <summary>
    /// 环球搜操作类型
    /// </summary>
    public enum GlobalSearchOperationType
    {
        /// <summary>
        /// 新增服务（包括新开通和变更申请中的新增）
        /// </summary>
        NewService,

        /// <summary>
        /// 更新服务（包括续约和变更现有账号）
        /// </summary>
        UpdateService
    }

    #endregion

    #region GTIS预处理结果

    /// <summary>
    /// GTIS预处理结果
    /// </summary>
    public class GtisPreprocessResult
    {
        /// <summary>
        /// 操作类型
        /// </summary>
        public GtisOperationType OperationType { get; set; }

        /// <summary>
        /// 当前Wits用户列表（保留完整权限信息，统一使用wits_user）
        /// </summary>
        public List<Db_crm_contract_serviceinfo_wits_user> CurrentWitsUsers { get; set; }

        /// <summary>
        /// 用户分类结果（仅在UpdateService场景需要）
        /// </summary>
        public UserClassificationResult UserClassification { get; set; }

        /// <summary>
        /// 是否为首次开通GTIS服务
        /// </summary>
        public bool IsFirstTimeOpening { get; set; }

        public GtisPreprocessResult()
        {
            CurrentWitsUsers = new List<Db_crm_contract_serviceinfo_wits_user>();
        }
    }

    /// <summary>
    /// GTIS操作类型
    /// </summary>
    public enum GtisOperationType
    {
        /// <summary>
        /// 新增服务（首次开通和续约重新开通）
        /// </summary>
        NewService,

        /// <summary>
        /// 更新服务（变更和续约续期服务）
        /// </summary>
        UpdateService
    }

    /// <summary>
    /// 用户分类结果
    /// </summary>
    public class UserClassificationResult
    {
        public List<Db_crm_contract_serviceinfo_wits_user> AddUsers { get; set; }
        public List<Db_crm_contract_serviceinfo_gtis_user> UpdateUsers { get; set; }
        public List<Db_crm_contract_serviceinfo_gtis_user> DelUsers { get; set; }

        /// <summary>
        /// 用户权限变更列表（用于UserPhoneCrmChange）
        /// </summary>
        public List<CRM2_API.Model.BusinessModel.BM_GtisOpe.BM_GtisOpe_RenewalContact.BM_GtisOpe_ChangeUserPermission> UserPermissionChanges { get; set; }

        public UserClassificationResult()
        {
            AddUsers = new List<Db_crm_contract_serviceinfo_wits_user>();
            UpdateUsers = new List<Db_crm_contract_serviceinfo_gtis_user>();
            DelUsers = new List<Db_crm_contract_serviceinfo_gtis_user>();
            UserPermissionChanges = new List<CRM2_API.Model.BusinessModel.BM_GtisOpe.BM_GtisOpe_RenewalContact.BM_GtisOpe_ChangeUserPermission>();
        }
    }

    #endregion

    #region SaleWits资源下发相关模型

    /// <summary>
    /// SaleWits资源数据准备结果
    /// </summary>
    public class SaleWitsResourceDataResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 下发类型
        /// </summary>
        public CRM2_API.Model.BLLModel.Enum.EnumSaleWitsDistributionType DistributionType { get; set; }

        /// <summary>
        /// 预设资源
        /// </summary>
        public SaleWitsPresetResources PresetResources { get; set; }

        /// <summary>
        /// 设置成功
        /// </summary>
        /// <param name="message">消息</param>
        public void SetSuccess(string message)
        {
            Success = true;
            Message = message;
        }

        /// <summary>
        /// 设置错误
        /// </summary>
        /// <param name="message">错误消息</param>
        public void SetError(string message)
        {
            Success = false;
            Message = message;
        }
    }

    /// <summary>
    /// SaleWits预设资源
    /// </summary>
    public class SaleWitsPresetResources
    {
        /// <summary>
        /// 月数
        /// </summary>
        public int MonthsCount { get; set; }

        /// <summary>
        /// Token数量（万个）
        /// </summary>
        public int TokenCount { get; set; }

        /// <summary>
        /// 邮件数量
        /// </summary>
        public int EmailCount { get; set; }

        /// <summary>
        /// 充值金额（元）
        /// </summary>
        public decimal RechargeAmount { get; set; }
    }

    /// <summary>
    /// SaleWits资源验证结果
    /// </summary>
    public class SaleWitsResourceValidationResult
    {
        /// <summary>
        /// 是否有效
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 预设资源
        /// </summary>
        public SaleWitsPresetResources Resources { get; set; }

        /// <summary>
        /// 设置有效
        /// </summary>
        /// <param name="resources">预设资源</param>
        public void SetValid(SaleWitsPresetResources resources)
        {
            IsValid = true;
            Resources = resources;
            ErrorMessage = null;
        }

        /// <summary>
        /// 设置无效
        /// </summary>
        /// <param name="errorMessage">错误消息</param>
        public void SetInvalid(string errorMessage)
        {
            IsValid = false;
            ErrorMessage = errorMessage;
            Resources = null;
        }
    }

    /// <summary>
    /// SaleWits API调用结果
    /// </summary>
    public class SaleWitsApiResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 成功消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 下发记录ID
        /// </summary>
        public string DistributionId { get; set; }

        /// <summary>
        /// 下发时间
        /// </summary>
        public DateTime DistributionTime { get; set; }

        /// <summary>
        /// 租户ID
        /// </summary>
        public string TenantId { get; set; }

        /// <summary>
        /// 响应数据
        /// </summary>
        public string ResponseData { get; set; }
    }

    #endregion

    #region SaleWits定时下发统计模型

    /// <summary>
    /// 定时下发总结信息
    /// </summary>
    public class DistributionSummary
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; } = true;

        /// <summary>
        /// 总结消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 总服务数量
        /// </summary>
        public int TotalServices { get; set; }

        /// <summary>
        /// 成功数量
        /// </summary>
        public int SuccessCount { get; set; }

        /// <summary>
        /// 失败数量
        /// </summary>
        public int FailureCount { get; set; }

        /// <summary>
        /// 总耗时
        /// </summary>
        public TimeSpan Duration { get; set; }

        /// <summary>
        /// 各服务的处理结果
        /// </summary>
        public List<ServiceDistributionResult> ServiceResults { get; set; } = new List<ServiceDistributionResult>();
    }

    /// <summary>
    /// 单个服务的下发结果
    /// </summary>
    public class ServiceDistributionResult
    {
        /// <summary>
        /// 服务ID
        /// </summary>
        public string ServiceId { get; set; }

        /// <summary>
        /// 资源管理ID
        /// </summary>
        public string ResourceManagementId { get; set; }

        /// <summary>
        /// 合同ID
        /// </summary>
        public string ContractId { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 结果消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 异常信息（如果有）
        /// </summary>
        public Exception Exception { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// 耗时
        /// </summary>
        public TimeSpan Duration { get; set; }
    }

    #endregion
}