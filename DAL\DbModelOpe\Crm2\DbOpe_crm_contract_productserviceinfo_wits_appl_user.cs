using CRM2_API.DAL.DbCommon;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.Model.ControllersViewModel;

namespace CRM2_API.DAL.DbModelOpe.Crm2
{
    /// <summary>
    /// crm_contract_productserviceinfo_appl_user表操作
    /// </summary>
    public class DbOpe_crm_contract_productserviceinfo_wits_appl_user : DbOperateCrm2<Db_crm_contract_productserviceinfo_wits_appl_user, DbOpe_crm_contract_productserviceinfo_wits_appl_user>
    {
        public List<GetWitsApplyInfo4Audit_Out_ApplyInfo_UserList> GetUserListByWitsId(string witsApplId)
        {
            return Queryable
                .Where(e => e.WitsApplId == witsApplId)
                .Where(e => e.Deleted == false)
                .Select(e => new GetWitsApplyInfo4Audit_Out_ApplyInfo_UserList
                {
                    Id = e.Id,
                    AccountNumber = e.AccountNumber,
                    AccountType = e.AccountType.Value,
                    OpeningStatus = e.OpeningStatus.Value,
                    SharePeopleNum = e.SharePeopleNum.Value,
                    GtisPermission = e.GtisPermission.Value,
                    VipPermission = e.VipPermission.Value,
                    GlobalSearchPermission = e.GlobalSearchPermission.Value,
                    CollegePermission = e.CollegePermission.Value,
                    SalesWitsPermission = e.SalesWitsPermission.Value,
                    SalesWitsBindPhoneId = e.SalesWitsPhoneId,
                })
                .ToList();
        }
    }
}
