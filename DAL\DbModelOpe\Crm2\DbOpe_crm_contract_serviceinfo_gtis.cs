using CRM2_API.BLL.Common;
using CRM2_API.BLL.GtisOpe;
using CRM2_API.DAL.DbCommon;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.BusinessModel.BM_GtisOpe;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.ControllersViewModel.ContractOverseasIPRecord;
using CRM2_API.Model.System;
using DocumentFormat.OpenXml.EMMA;
using DocumentFormat.OpenXml.ExtendedProperties;
using JiebaNet.Segmenter.Common;
using SqlSugar;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel.Design;
using System.Linq;
using System.Xml.Schema;
using CRM2_API.Model.BLLModel.Enum;
using static CRM2_API.Model.ControllersViewModel.VM_Collectioninfo;
using static CRM2_API.Model.ControllersViewModel.VM_Contract;
using static CRM2_API.Model.ControllersViewModel.VM_ContractInvoice;

namespace CRM2_API.DAL.DbModelOpe.Crm2
{
    /// <summary>
    /// crm_contract_serviceinfo_gtis表操作
    /// </summary>
    public class DbOpe_crm_contract_serviceinfo_gtis : DbOperateCrm2Ex<Db_crm_contract_serviceinfo_gtis, DbOpe_crm_contract_serviceinfo_gtis>
    {
        /// <summary>
        /// 检测过期服务
        /// </summary>
        public void CheckGtisServiceOutOfTime(string applId = "")
        {
            var dt = DateTime.Now;
            var dtDay = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd"));
            TransDeal(() =>
            {
                var endList = Queryable
                  .LeftJoin<Db_crm_contract_productserviceinfo_gtis_appl>((gtis, appl) => gtis.ProductServiceInfoGtisApplId == appl.Id && appl.Deleted == false)
                  .Where((gtis, appl) => gtis.Deleted == false)
                  .WhereIF(!SqlFunc.IsNullOrEmpty(applId), (gtis, appl) => appl.Id == applId)
                  .Where((gtis, appl) => gtis.State == (int)EnumContractServiceState.VALID)
                  .Where((gtis, appl) => gtis.IsApplHistory == false)
                  //.Where((gtis, appl) => gtis.IsChanged == (int)EnumContractServiceInfoIsChange.Not)
                  .Where((gtis, appl) => appl.State == (int)EnumProcessStatus.Pass)
                  .Where((gtis, appl) => gtis.IsProcessed == (int)EnumGtisServiceIsProcess.Processed)
                  .Where((gtis, appl) => gtis.ServiceCycleEnd != null && SqlFunc.DateDiff(DateType.Day, dtDay, gtis.ServiceCycleEnd.Value) <= 0)
                  .ToList();
                foreach (var service in endList)
                {
                    //后补：同时在gtis系统停用对应账号   10.18 gtis自己会处理停用账号
                    var users = DbOpe_crm_contract_serviceinfo_gtis_user.Instance.GetDataList(service.Id);
                    foreach (var user in users)
                    {
                        user.OpeningStatus = (int)EnumGtisUserOpeningStatus.Expired;
                        user.EndDate = dt;
                        DbOpe_crm_contract_serviceinfo_gtis_user.Instance.Update(user);
                    }
                    service.State = (int)EnumContractServiceState.OUT;
                    Update(service);
                }
            });
        }
        /// <summary>
        /// 获取未执行的gtis服务申请
        /// </summary>
        /// <param name="waitServiceCycleStart">是否等到服务开始时间再执行？</param>
        /// <returns></returns>
        public List<Db_crm_contract_serviceinfo_gtis> GetNeedProcessData(bool waitServiceCycleStart = false)
        {
            var dtDay = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd"));
            var r = Queryable
              .LeftJoin<Db_crm_contract_productserviceinfo_gtis_appl>((gtis, appl) => gtis.ProductServiceInfoGtisApplId == appl.Id && appl.Deleted == false)
              .Where((gtis, appl) => gtis.Deleted == false)
              .Where((gtis, appl) => gtis.State == (int)EnumContractServiceState.VALID || gtis.State == (int)EnumContractServiceState.TO_BE_EFFECTIVE)
              .Where((gtis, appl) => appl.State == (int)EnumProcessStatus.Pass)
              .Where((gtis, appl) => gtis.IsApplHistory == false)
              .Where((gtis, appl) => gtis.IsProcessed == (int)EnumGtisServiceIsProcess.Not)
              .WhereIF(waitServiceCycleStart, (gtis, appl) => gtis.ServiceCycleStart != null && SqlFunc.DateDiff(DateType.Day, gtis.ServiceCycleStart.Value, dtDay) >= 0)
              .ToList();
            return r;

        }
        /// <summary>
        /// 获取服务产品当前生效的服务信息
        /// </summary>
        /// <param name="contractProductInfoId"></param>
        /// <param name="containsOutStates">是否包括过期的   服务变更只能是当前生效的，续约包括过期的</param>
        /// <returns></returns>
        public GtisInfo_OUT GetInfoByApplContractProductId(string contractProductInfoId, bool containsOutStates = false)
        {
            GtisInfo_OUT r = null;
            List<int> states = new List<int>() { (int)EnumContractServiceState.VALID };
            if (containsOutStates)
            {
                states.Add((int)EnumContractServiceState.OUT);
                //states.Add((int)EnumContractServiceState.VOID);
            }
            string applId = "";
            try
            {
                applId = Queryable
                .Where(g => g.ContractProductInfoId == contractProductInfoId)
                .Where(g => g.Deleted == false)
                .Where(g => g.IsChanged == (int)EnumContractServiceInfoIsChange.Not)
                .Where(g => g.IsApplHistory == false)
                .Where(g => SqlFunc.ContainsArray(states, g.State))
                .OrderByDescending(g => new { g.UpdateDate, g.CreateDate })
                .Select<GtisInfo_OUT>().First()?.ProductServiceInfoGtisApplId;
            }
            catch (Exception e)
            {
                LogUtil.AddErrorLog("GetInfoByApplContractProductId 1 " + contractProductInfoId + " " + e.Message);
            }
            if (string.IsNullOrEmpty(applId))
            {
                return null;
            }
            List<ServiceInfoGtisUser_OUT> uList = new List<ServiceInfoGtisUser_OUT>();
            try
            {
                //这里会调用g5接口刷新服务和账号最新状态
                uList = DbOpe_crm_contract_productserviceinfo_gtis_appl_user.Instance.GetContractServiceInfoGtisUserByApplId(applId);
            }
            catch (Exception e)
            {
                LogUtil.AddErrorLog("GetContractServiceInfoGtisUserByApplId 2 " + applId + " " + e.Message);
            }
            ApplGtisInfo_OUT applData = null;
            try
            {
                applData = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.GetApplGtisInfo(applId);
            }
            catch (Exception e)
            {
                LogUtil.AddErrorLog("GetApplGtisInfo 3 " + applId + " " + e.Message);
            }
            if (applData == null)
            {
                return null;
            }
            try
            {
                r = Queryable
                                .Where(g => g.ContractProductInfoId == contractProductInfoId)
                                .Where(g => g.Deleted == false)
                                .Where(g => g.IsChanged == (int)EnumContractServiceInfoIsChange.Not)
                                .Where(g => g.IsApplHistory == false)
                                .Where(g => g.ProductServiceInfoGtisApplId == applId)
                                .Where(g => SqlFunc.ContainsArray(states, g.State))
                                .OrderByDescending(g => new { g.UpdateDate, g.CreateDate })
                                .Select<GtisInfo_OUT>().First();
                r.SendAccount = applData.SendAccount;
                r.SendAccountTime = applData.SendAccountTime;
                r.GtisUserInfo = new List<GtisUserInfo_OUT>();
                r.GtisUserInfo = uList.MappingTo<List<GtisUserInfo_OUT>>();
                r.GtisUserInfo.Sort((p1, p2) =>
                {
                    if (p2.AccountType == null || p1.AccountType == null)
                    {
                        return 0;
                    }
                    else
                    {
                        return p2.AccountType.Value.CompareTo(p1.AccountType.Value);
                    }
                });
                r.ResidentCountries = DbOpe_crm_contract_serviceinfo_gtis_residentcountry.Instance.GetDataList(u => u.ContractServiceInfoGtisId == r.Id).MappingTo<List<GtisResidentCountry>>();
                r.ResidentCitys = DbOpe_crm_contract_serviceinfo_gtis_residentcity.Instance.GetDataList(u => u.ContractServiceInfoGtisId == r.Id).MappingTo<List<GtisResidentCity>>();
                r.GtisRetailCountry = new List<GtisRetailCountry_OUT>();
                if (r.RetailCountry == (int)EnumGtisRetailCountry.Add)
                {
                    var countrys = DbOpe_sys_g4_dbnames.Instance.GetDataAllList().Select<G4DbNames>().ToList();
                    var applCountrys = DbOpe_crm_contract_serviceinfo_gtis_country.Instance.GetDataList(u => u.ContractServiceInfoGtisId == r.Id);
                    foreach (var country in applCountrys)
                    {
                        GtisRetailCountry_OUT gtisRetailCountry = country.MappingTo<GtisRetailCountry_OUT>();
                        var c = countrys.Find(c => c.SID == gtisRetailCountry.Sid);
                        if (c != null)
                        {
                            gtisRetailCountry.SidName = c.CountryName;
                            r.GtisRetailCountry.Add(gtisRetailCountry);
                        }
                    }
                }
                r.SharePeopleNum = r.GtisUserInfo.Max(u => u.SharePeopleNum);
                r.SubAccountsNum = r.GtisUserInfo.Where(u => u.AccountType == (int)EnumGtisAccountType.Sub).Count();
                r.AuthorizationNum = r.GtisUserInfo.Find(u => u.AccountType == (int)EnumGtisAccountType.Main).AuthorizationNum;
                r.ServiceCycle = (r.ServiceCycleStart == null ? "" : r.ServiceCycleStart.Value.ToString("yyyy-MM-dd")) + "至" + (r.ServiceCycleEnd == null ? "" : r.ServiceCycleEnd.Value.ToString("yyyy-MM-dd"));
            }
            catch (Exception e)
            {
                LogUtil.AddErrorLog("GetInfoByApplContractProductId 4 " + contractProductInfoId + " " + e.Message);
            }
            return r;

        }
        /// <summary>
        /// 获取公司上一份GTIS合同的产品信息
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="currentContractId"></param>
        /// <returns></returns>
        public GtisInfo_OUT GetOldContractGtisInfoByCompanyId(string companyId, string currentContractId)
        {
            var contractProductInfoId = GetOldContractProductIdByCompanyId(companyId, currentContractId);
            if (string.IsNullOrEmpty(contractProductInfoId))
            {
                return null;
            }
            else
            {
                return GetInfoByApplContractProductId(contractProductInfoId, true);
            }
        }
        /// <summary>
        /// 获取公司上一份GTIS合同的产品信息表ID（ContractProductInfoId）
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="currentContractId"></param>
        /// <returns></returns>
        public string GetOldContractProductIdByCompanyId(string companyId, string currentContractId)
        {
            try
            {
                var old = Queryable
                  .LeftJoin<Db_crm_contract>((gtis, contract) => gtis.ContractId == contract.Id)
                  .Where((gtis, contract) => contract.FirstParty == companyId)
                  .Where((gtis, contract) => contract.Id != currentContractId)
                  .Where((gtis, contract) => !SqlFunc.IsNullOrEmpty(contract.ContractNum))
                  .Where((gtis, contract) => contract.Deleted == false && gtis.Deleted == false)
                  .Where((gtis, contract) => gtis.IsProcessed == (int)EnumGtisServiceIsProcess.Processed)
                  .Where((gtis, contract) => gtis.IsApplHistory == false)
                  .Where((gtis, contract) => gtis.State == (int)EnumContractServiceState.VALID || gtis.State == (int)EnumContractServiceState.OUT)
                  .OrderByDescending((gtis, contract) => new { gtis.ProcessedTime, gtis.ServiceCycleEnd })
                  .First();
                return old?.ContractProductInfoId;
            }
            catch (Exception e)
            {
                LogUtil.AddErrorLog("GetOldContractProductIdByCompanyId " + companyId + " " + currentContractId + " " + e.Message);
            }
            return null;
        }

        /// <summary>
        /// 获取公司上一份GTIS合同的产品信息表ID（ContractProductInfoId）
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="CurContractId"></param>
        /// <returns></returns>
        public string GetOldContractContractNumByCompanyId(string companyId, string CurContractId)
        {
            try
            {
                var old = Db.Queryable<Db_crm_contract>()
                 .LeftJoin<Db_v_productserviceinfo>((contract, view) => contract.Id == view.ContractId)
                 .Where(contract => contract.FirstParty == companyId)
                 .Where(contract => contract.ContractStatus == (int)EnumContractStatus.Pass)
                 .Where(contract => contract.Id != CurContractId)
                 .Where(contract => contract.Deleted == false)
                 .Where((contract, view) => view.ProductType == (int)EnumProductType.Gtis || view.ProductType == (int)EnumProductType.Combination || view.ProductType == (int)EnumProductType.Vip)
                 .Where((contract, view) => view.ApplState == (int)EnumProcessStatus.Pass)
                 .OrderByDescending(contract => contract.CreateDate)
                 .Select((contract, view) => contract)
                 .First();

                /*var old = Queryable
                  .LeftJoin<Db_crm_contract>((gtis, contract) => gtis.ContractId == contract.Id)
                  .Where((gtis, contract) => contract.FirstParty == companyId)
                  .Where((gtis, contract) => contract.Id != currentContractId)
                  .Where((gtis, contract) => !SqlFunc.IsNullOrEmpty(contract.ContractNum))
                  .Where((gtis, contract) => contract.Deleted == false && gtis.Deleted == false)
                  .Where((gtis, contract) => gtis.IsProcessed == (int)EnumGtisServiceIsProcess.Processed)
                  .Where((gtis, contract) => gtis.IsApplHistory == false)
                  .Where((gtis, contract) => gtis.State == (int)EnumContractServiceState.VALID || gtis.State == (int)EnumContractServiceState.OUT)
                  .OrderByDescending((gtis, contract) => new { gtis.ProcessedTime, gtis.ServiceCycleEnd })
                  .First();*/
                return old?.ContractNum;
            }
            catch (Exception e)
            {
                LogUtil.AddErrorLog("GetOldContractContractNumByCompanyId " + companyId + " " + CurContractId + " " + e.Message);
            }
            return null;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="contractNum"></param>
        /// <returns></returns>
        public string GetContractProductIdByContractNum(string contractNum)
        {
            try
            {
                var old = Queryable
                  .LeftJoin<Db_crm_contract>((gtis, contract) => gtis.ContractId == contract.Id)
                  .Where((gtis, contract) => contract.ContractNum == contractNum)
                  .Where((gtis, contract) => !SqlFunc.IsNullOrEmpty(contract.ContractNum))
                  .Where((gtis, contract) => contract.Deleted == false && gtis.Deleted == false)
                  .Where((gtis, contract) => gtis.IsProcessed == (int)EnumGtisServiceIsProcess.Processed)
                  .Where((gtis, contract) => gtis.IsApplHistory == false)
                  .Where((gtis, contract) => gtis.State == (int)EnumContractServiceState.VALID || gtis.State == (int)EnumContractServiceState.OUT)
                  .OrderByDescending((gtis, contract) => new { gtis.ProcessedTime, gtis.ServiceCycleEnd })
                  .First();
                return old?.ContractProductInfoId;
            }
            catch (Exception e)
            {
                LogUtil.AddErrorLog("GetOldContractProductIdByCompanyId " + contractNum + " " + e.Message);
            }
            return null;
        }
        /// <summary>
        /// 获取登记/复核信息
        /// </summary>
        /// <param name="applId"></param>
        /// <returns></returns>
        public RegisterInfo_OUT GetRegisterInfoByApplId(string applId)
        {
            //获取服务申请信息
            var applData = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.GetDataById(applId);
            if (applData == null || applData.Deleted == true)
            {
                throw new ApiException("未找到对应服务产品申请信息");
            }
            var gtisInfos = Queryable
                .LeftJoin<Db_sys_user>((g, register) => g.RegisteredId == register.Id && register.Deleted == false)
                .LeftJoin<Db_sys_user>((g, register, reviewer) => g.ReviewerId == reviewer.Id && reviewer.Deleted == false)
                .Where((g, register, reviewer) => g.ProductServiceInfoGtisApplId == applId)
                .Where((g, register, reviewer) => g.Deleted == false)
                .Select((g, register, reviewer) => new RegisterRemarks_OUT
                {
                    RegisteredName = register.Name,
                    ReviewerName = reviewer.Name,
                    AuditDate = g.UpdateDate == null ? g.CreateDate : g.UpdateDate
                }, true)
                .MergeTable()
                .OrderBy(it => it.IsApplHistory)
                .OrderByDescending(it => it.AuditDate)
                .ToList();
            if (gtisInfos.Count == 0)
            {
                //还没登记过呢
                return null;
            }
            else
            {
                RegisterInfo_OUT r = new RegisterInfo_OUT();
                //取ishistory=false的最新一条的登记详情 
                var latestRemarkData = gtisInfos.First();
                var newInfo = Queryable.Where(d => d.Id == latestRemarkData.Id)
                    .Where(d => d.Deleted == false)
                    .Select(d => new RegisterInfo_OUT()
                    {

                    }, true)
                    .First();
                latestRemarkData.MappingTo(newInfo);
                newInfo.MappingTo(r);
                r.RegisterRemarks = gtisInfos;
                //查其他子表登记信息
                r.GtisUserInfo = new List<GtisUserInfo_OUT>();
                var applUsers = DbOpe_crm_contract_serviceinfo_gtis_user.Instance.GetDataList(r.Id)
                    .OrderBy(e => e.AccountNumber == null).ThenBy(e => e.AccountNumber)
                    .OrderByDescending(e => e.AccountType);
                r.GtisUserInfo = applUsers.MappingTo<List<GtisUserInfo_OUT>>();
                r.ResidentCountries = DbOpe_crm_contract_serviceinfo_gtis_residentcountry.Instance.GetDataList(u => u.ContractServiceInfoGtisId == r.Id).MappingTo<List<GtisResidentCountry>>();
                r.ResidentCitys = DbOpe_crm_contract_serviceinfo_gtis_residentcity.Instance.GetDataList(u => u.ContractServiceInfoGtisId == r.Id).MappingTo<List<GtisResidentCity>>();
                r.GtisRetailCountry = new List<GtisRetailCountry_OUT>();
                if (r.RetailCountry == (int)EnumGtisRetailCountry.Add)
                {
                    var countrys = DbOpe_sys_g4_dbnames.Instance.GetDataAllList().Select<G4DbNames>().ToList();
                    var applCountrys = DbOpe_crm_contract_serviceinfo_gtis_country.Instance.GetDataList(u => u.ContractServiceInfoGtisId == r.Id);
                    foreach (var country in applCountrys)
                    {
                        GtisRetailCountry_OUT gtisRetailCountry = country.MappingTo<GtisRetailCountry_OUT>();
                        var c = countrys.Find(c => c.SID == gtisRetailCountry.Sid);
                        if (c != null)
                        {
                            gtisRetailCountry.SidName = c.CountryName;
                            r.GtisRetailCountry.Add(gtisRetailCountry);
                        }
                    }
                }
                if (r.GtisUserInfo != null && r.GtisUserInfo.Count != 0)
                {
                    r.SharePeopleNum = r.GtisUserInfo.Count == 0 ? r.SharePeopleNum : r.GtisUserInfo.Max(u => u.SharePeopleNum);
                    r.SubAccountsNum = r.GtisUserInfo.Where(u => u.AccountType == (int)EnumGtisAccountType.Sub).Count();
                    r.AuthorizationNum = r.GtisUserInfo.Find(u => u.AccountType == (int)EnumGtisAccountType.Main).AuthorizationNum;
                    r.GtisUserInfo.Sort((p1, p2) =>
                    {
                        if (p2.AccountType == null || p1.AccountType == null)
                        {
                            return 0;
                        }
                        else
                        {
                            return p2.AccountType.Value.CompareTo(p1.AccountType.Value);
                        }
                    });
                }
                r.ServiceCycle = (r.ServiceCycleStart == null ? "" : r.ServiceCycleStart.Value.ToString("yyyy-MM-dd")) + "至" + (r.ServiceCycleEnd == null ? "" : r.ServiceCycleEnd.Value.ToString("yyyy-MM-dd"));

                r.ServiceMonthAfterDiscount = r.ServiceMonth;
                r.ServiceCycleStartAfterDiscount = r.ServiceCycleStart;
                r.ServiceCycleEndAfterDiscount = r.ServiceCycleEnd;
                r.MainSysUserPhones = DbOpe_crm_contract_serviceinfo_gtis_user_phone.Instance.GetAuditUserPhoneList(r.Id);
                return r;
            }
        }
        /// <summary>
        /// 根据合同Id，判断该合同是否有已开通的GTIS服务
        /// </summary>
        /// <param name="ContractId"></param>
        /// <returns></returns>
        public EnumContractGtisProductServiceState CheckHasUnValidGTISService(string ContractId)
        {
            var productList = Db.Queryable<Db_v_productserviceinfo>()
                .Where(e => e.ContractId.Equals(ContractId))
                .Where(e => e.ProductType == (int)EnumProductType.Gtis || e.ProductType == (int)EnumProductType.Vip)
                .ToList();
            if (productList.Count == 0)
                return EnumContractGtisProductServiceState.Inexistence;
            else if (productList.Any(e => e.ServiceState == (long)EnumContractServiceState.VALID || e.ServiceState == (long)EnumContractServiceState.OUT))
                return EnumContractGtisProductServiceState.Opened;
            else
                return EnumContractGtisProductServiceState.ExistAndNotopen;
        }

        public EnumContractGtisProductServiceState CheckHasUnValidGTISServiceWithOutView(string ContractId)
        {
            var productList = Db.Queryable<Db_v_productserviceinfo>()
                .Where(e => e.ContractId.Equals(ContractId))
                .Where(e => e.ProductType == (int)EnumProductType.Gtis || e.ProductType == (int)EnumProductType.Vip)
                .ToList();
            if (productList.Count == 0)
                return EnumContractGtisProductServiceState.Inexistence;
            else if (productList.Any(e => e.ServiceState == (long)EnumContractServiceState.VALID || e.ServiceState == (long)EnumContractServiceState.OUT))
                return EnumContractGtisProductServiceState.Opened;
            else
                return EnumContractGtisProductServiceState.ExistAndNotopen;
        }

        /// <summary>
        /// 查看Gtis账号生成方式是否是使用原有
        /// </summary>
        /// <param name="ContractId"></param>
        /// <returns></returns>
        public bool CheckGtisUseOrgAccount(string ContractId)
        {
            var gtisService = Queryable
                .Where(e => e.ContractId == ContractId)
                .Where(e => e.Deleted == false && e.IsApplHistory == false)
                .Where(e => e.State == 1)
                .First();
            return gtisService?.AccountGenerationMethod == (int)EnumGtisAccountGenerationMethod.Generate;
        }

        /// <summary>
        /// 环球搜开通时，将旧合同中的(最新一条开通或过期的)Gtis服务作废
        /// </summary>
        /// <param name="ContractNum"></param>
        /// <param name="UserId"></param>
        public void UpdateOldGtisServiceVoid(string ContractNum, string UserId)
        {
            Updateable
                .SetColumns(e => e.State == (int)EnumContractServiceState.VOID)
                .SetColumns(e => e.UpdateUser == UserId)
                .SetColumns(e => e.UpdateDate == DateTime.Now)
                .Where(e => e.ContractNum == ContractNum)
                .Where(e => e.IsApplHistory == false)
                .Where(e => e.IsChanged == (int)EnumContractServiceInfoIsChange.Not)
                .Where(e => e.State == (int)EnumContractServiceState.VALID || e.State == (int)EnumContractServiceState.OUT)
                .AddQueue();
        }

        /// <summary>
        /// 根据合同ID获取Gtis服务信息
        /// </summary>
        /// <param name="ContractId"></param>
        /// <returns></returns>
        public Db_crm_contract_serviceinfo_gtis GetGtisServiceInfoByContractId(string ContractId)
        {
            return Queryable
                .Where(e => e.ContractId == ContractId)
                .Where(e => e.Deleted == false && e.IsApplHistory == false && e.IsChanged == 0)
                .Where(e => e.State == (int)EnumContractServiceState.VALID || e.State == (int)EnumContractServiceState.OUT)
                .OrderByDescending(e => e.CreateDate)
                .First();
        }

        /// <summary>
        /// 根据gtis账号获取当前客户经理私有池下所有包含Gtis服务客户
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="contractServiceState"></param>
        /// <returns></returns>
        public string[] GetGtisSvcodes(string userId, int contractServiceState)
        {
            if (string.IsNullOrEmpty(userId))
            {
                throw new ApiException("查询GTIS使用情况权限错误");
            }
            else
            {
                var authGtisUserIds = Queryable
                    .LeftJoin<Db_crm_contract>((g, c) => g.ContractId == c.Id && c.Deleted == false && c.Issuer == userId)
                    .LeftJoin<Db_crm_customer_subcompany>((g, c, s) => s.Id == c.FirstParty && s.Deleted == (int)EnumCustomerDel.NotDel)
                    .LeftJoin<Db_crm_customer_privatepool>((g, c, s, p) => p.CustomerId == s.CustomerId && p.Deleted == (int)EnumCustomerDel.NotDel)
                    .LeftJoin<Db_crm_customer>((g, c, s, p, cc) => cc.TrackingStage == 50)//签约库
                    .LeftJoin<Db_crm_contract_productinfo>((g, c, s, p, cc, cp) => c.Id == cp.ContractId)
                    .LeftJoin<Db_crm_product>((g, c, s, p, cc, cp, pp) => cp.ProductId == pp.Id && pp.ProductType == 1)
                    .Where((g, c, s, p) => g.Deleted == false)
                    //.Where(( g,  s, p) => SqlFunc.ContainsArray(gtisUserIds,u.UserId))
                    .Where((g, c, s, p) => g.IsApplHistory == false)
                    .WhereIF(contractServiceState.ToInt() == 99, (g, c, s, p) => SqlFunc.ContainsArray(new int[] { (int)EnumContractServiceState.VALID, (int)EnumContractServiceState.OUT }, g.State))
                    .WhereIF(contractServiceState.ToInt() != 99, (g, c, s, p) => g.State == (int)contractServiceState)
                    //.Where((g, c, s, p) => SqlFunc.ContainsArray(new int[] { (int)EnumContractServiceState.VALID, (int)EnumContractServiceState.OUT }, g.State))
                    .Where((g, c, s, p) => g.IsChanged == (int)EnumContractServiceInfoIsChange.Not)
                    .Where((g, c, s, p) => s.IsValid == (int)EnumCompanyValid.VALID)
                    .Where((g, c, s, p) => p.State == (int)EnumCustomerPrivateRelease.Not)
                    .Where((g, c, s, p) => p.UserId == userId).Distinct()
                    .Select((g, c, s, p) => SqlFunc.ToString(g.ContractNum))
                    .ToList();
                //修改 2024年8月7日 区分签约和保留 保留客户若上一份Gtis合同不是自己的则不提供svcode
                /*var notNewSignCustomerID = Db.Queryable<Db_crm_customer_privatepool>()
                    .LeftJoin<Db_crm_customer_subcompany>((p, s) => p.CustomerId == s.CustomerId && p.Deleted == (int)EnumCustomerDel.NotDel && s.Deleted == (int)EnumCustomerDel.NotDel)
                    .LeftJoin<Db_crm_customer>((p, s, cc) => cc.Id == s.CustomerId && cc.TrackingStage != 50)//签约库
                    .Where((p, s, cc) => s.IsValid == (int)EnumCompanyValid.VALID)
                    .Where((p, s, cc) => p.State == (int)EnumCustomerPrivateRelease.Not)
                    //.Where((s,p,cc)=>)
                    .Where((p, s, cc) => p.UserId == userId)
                    .Select((p, s, cc) => s.CustomerId).ToList();
                notNewSignCustomerID.ForEach(i =>
                {
                    var newContract = Db.Queryable<Db_crm_contract>()
                                        .LeftJoin<Db_crm_contract_productinfo>((c, cp) => c.Id == cp.ContractId)
                                        .LeftJoin<Db_crm_product>((c, cp, p) => cp.ProductId == p.Id && p.ProductType == 1)
                                        .Where((c, cp, p) => c.FirstParty == i)
                                        .OrderByDescending((c, cp, p) => c.SigningDate)
                                        .Select((c, cp, p) => new
                                        {
                                            Issuer = c.Issuer,
                                            ContractNum = SqlFunc.ToString(c.ContractNum)
                                        }).First();
                    if (newContract.IsNotNull())
                    {
                        if (newContract.Issuer == userId)
                        {
                            authGtisUserIds.Add(newContract.ContractNum);
                        }
                    }
                });*/
                var notNewSignCustomerID = Db.Queryable<Db_crm_contract>()
                                                .LeftJoin<Db_crm_contract_productinfo>((c, cp) => c.Id == cp.ContractId)
                                                .InnerJoin<Db_crm_product>((c, cp, p) => cp.ProductId == p.Id && p.ProductType == 1)
                                                .Where((c, cp, p) => c.Issuer == userId)
                                                //.OrderByDescending((c, cp, p) => c.SigningDate)
                                                .Select((c, cp, p) => new
                                                {
                                                    index = SqlFunc.RowNumber(SqlFunc.Desc(c.SigningDate), c.FirstParty),
                                                    FirstParty = c.FirstParty,
                                                    ContractNum = SqlFunc.ToString(c.ContractNum),
                                                    Issuer = c.Issuer,
                                                    CustomerId = c.CustomerId,
                                                })
                                                .MergeTable()//将结果合并成一个表
                                                .Where(it => it.index == 1)
                                                .LeftJoin<Db_crm_customer_privatepool>((it, p) => it.CustomerId == p.CustomerId)
                                                .LeftJoin<Db_crm_customer_subcompany>((it, p, s) => p.CustomerId == s.CustomerId && p.Deleted == (int)EnumCustomerDel.NotDel && s.Deleted == (int)EnumCustomerDel.NotDel)
                                                .LeftJoin<Db_crm_customer>((it, p, s, cc) => cc.Id == s.CustomerId && cc.TrackingStage != 50)//签约库
                                                .Where((it, p, s, cc) => s.IsValid == (int)EnumCompanyValid.VALID)
                                                .Where((it, p, s, cc) => p.State == (int)EnumCustomerPrivateRelease.Not)
                                                .Where((it, p, s, cc) => p.UserId == userId)
                                                .Where((it, p, s, cc) => it.ContractNum != null && it.ContractNum != "")
                                                .Select((it, p, s, cc) => it.ContractNum).ToList();
                authGtisUserIds.AddRange(notNewSignCustomerID);

                return authGtisUserIds.ToArray();


            }
        }

        /// <summary>
        /// 根据gtis账号获取当前客户经理私有池下所有包含Gtis服务客户
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public List<CustomersvCodesInfo> GetGtisSvcodes(string userId)
        {
            if (string.IsNullOrEmpty(userId))
            {
                throw new ApiException("查询GTIS使用情况权限错误");
            }
            else
            {
                var authGtisUserIds = Queryable
                    .LeftJoin<Db_crm_contract>((g, c) => g.ContractId == c.Id && c.Deleted == false && c.Issuer == userId)
                    .LeftJoin<Db_crm_customer_subcompany>((g, c, s) => s.Id == c.FirstParty && s.Deleted == (int)EnumCustomerDel.NotDel)
                    .LeftJoin<Db_crm_customer_privatepool>((g, c, s, p) => p.CustomerId == s.CustomerId && p.Deleted == (int)EnumCustomerDel.NotDel)
                    .LeftJoin<Db_crm_customer>((g, c, s, p, cc) => cc.TrackingStage == 50)//签约库
                    .Where((g, c, s, p) => g.Deleted == false)
                    //.Where(( g,  s, p) => SqlFunc.ContainsArray(gtisUserIds,u.UserId))
                    .Where((g, c, s, p) => g.IsApplHistory == false)
                    .Where((g, c, s, p) => SqlFunc.ContainsArray(new int[] { (int)EnumContractServiceState.VALID, (int)EnumContractServiceState.OUT }, g.State))
                    //.Where((g, c, s, p) => SqlFunc.ContainsArray(new int[] { (int)EnumContractServiceState.VALID, (int)EnumContractServiceState.OUT }, g.State))
                    .Where((g, c, s, p) => g.IsChanged == (int)EnumContractServiceInfoIsChange.Not)
                    .Where((g, c, s, p) => s.IsValid == (int)EnumCompanyValid.VALID)
                    .Where((g, c, s, p) => p.State == (int)EnumCustomerPrivateRelease.Not)
                    .Where((g, c, s, p) => p.UserId == userId).Distinct()
                    .Select((g, c, s, p) => new CustomersvCodesInfo
                    {
                        svcodes = SqlFunc.ToString(g.ContractNum),
                        CompanyName = s.CompanyName

                    })
                    .ToList();
                //修改 2024年8月7日 区分签约和保留 保留客户若上一份Gtis合同不是自己的则不提供svcode
                /* var notNewSignCustomerID = Db.Queryable<Db_crm_customer_privatepool>()
                     .LeftJoin<Db_crm_customer_subcompany>((p, s) => p.CustomerId == s.CustomerId && p.Deleted == (int)EnumCustomerDel.NotDel && s.Deleted == (int)EnumCustomerDel.NotDel)
                     .LeftJoin<Db_crm_customer>((p, s, cc) => cc.Id == s.CustomerId && cc.TrackingStage != 50)//签约库
                     .Where((p, s, cc) => s.IsValid == (int)EnumCompanyValid.VALID)
                     .Where((p, s, cc) => p.State == (int)EnumCustomerPrivateRelease.Not)
                 //.Where((s,p,cc)=>)
                     .Where((p, s, cc) => p.UserId == userId)
                     .Where((p, s, cc) =>
                                     SqlFunc.Subqueryable<Db_crm_contract>()
                                     .LeftJoin<Db_crm_contract_productinfo>((c, cp) => c.Id == cp.ContractId)
                                     .LeftJoin<Db_crm_product>((c, cp, p) => cp.ProductId == p.Id && p.ProductType == 1)
                                     .Where((c, cp, p) => c.FirstParty == s.Id)
                                     .Where((c, cp, p) => c.Issuer == userId)
                                     .Any())
                 //.Select((p, s, cc) => s.CustomerId).ToList();
                     .Select((p, s, cc) => new CustomersvCodesInfo { svcodes = c.ContractNum, CompanyName = s.CompanyName }).ToList();
                 notNewSignCustomerID.ForEach(i =>
                 {
                     var newContract = Db.Queryable<Db_crm_contract>()
                     .LeftJoin<Db_crm_contract_productinfo>((c, cp) => c.Id == cp.ContractId)
                                         .LeftJoin<Db_crm_product>((c, cp, p) => cp.ProductId == p.Id && p.ProductType == 1)
                                         .Where((c, cp, p) => c.FirstParty == i.Id)
                                         .OrderByDescending((c, cp, p) => c.SigningDate)
                                         .Select((c, cp, p) => new
                                         {

                                             Issuer = c.Issuer,
                                             ContractNum = SqlFunc.ToString(c.ContractNum)
                                         }).First();
                     if (newContract.IsNotNull())
                     {
                         if (newContract.Issuer == userId)
                         {
                             if (newContract.ContractNum != null)
                             {
                                 authGtisUserIds.Add(new CustomersvCodesInfo { svcodes = newContract.ContractNum, CompanyName = i.CompanyName });
                             }
                         }
                     }
                 });*/
                var notNewSignCustomerID = Db.Queryable<Db_crm_contract>()
                                                .InnerJoin<Db_crm_contract_productinfo>((c, cp) => c.Id == cp.ContractId)
                                                .InnerJoin<Db_crm_product>((c, cp, p) => cp.ProductId == p.Id && p.ProductType == 1)
                                                .Where((c, cp, p) => c.Issuer == userId)
                                                //.OrderByDescending((c, cp, p) => c.SigningDate)
                                                .Select((c, cp, p) => new
                                                {
                                                    index = SqlFunc.RowNumber(SqlFunc.Desc(c.SigningDate), c.FirstParty),
                                                    FirstParty = c.FirstParty,
                                                    ContractNum = SqlFunc.ToString(c.ContractNum),
                                                    Issuer = c.Issuer,
                                                    CustomerId = c.CustomerId,
                                                })
                                                .MergeTable()//将结果合并成一个表
                                                .Where(it => it.index == 1)
                                                .LeftJoin<Db_crm_customer_privatepool>((it, p) => it.CustomerId == p.CustomerId)
                                                .LeftJoin<Db_crm_customer_subcompany>((it, p, s) => s.Id == it.FirstParty && p.Deleted == (int)EnumCustomerDel.NotDel && s.Deleted == (int)EnumCustomerDel.NotDel)
                                                .LeftJoin<Db_crm_customer>((it, p, s, cc) => cc.Id == s.CustomerId && cc.TrackingStage != 50)//签约库
                                                .Where((it, p, s, cc) => s.IsValid == (int)EnumCompanyValid.VALID)
                                                .Where((it, p, s, cc) => p.State == (int)EnumCustomerPrivateRelease.Not)
                                                .Where((it, p, s, cc) => p.UserId == userId)
                                                .Where((it, p, s, cc) => it.ContractNum != null)
                                                .Select((it, p, s, cc) => new CustomersvCodesInfo { svcodes = it.ContractNum, CompanyName = s.CompanyName }).ToList();
                ;
                //authGtisUserIds = authGtisUserIds.GroupBy(i =>new { i.CompanyName }).Select(i => new CustomersvCodesInfo
                //{
                //    svcodes = SqlFunc.MergeString(),
                //    CompanyName = i.Key.CompanyName
                //}).ToList();
                authGtisUserIds.AddRange(notNewSignCustomerID);
                var a = authGtisUserIds.GroupBy(i => i.CompanyName)
                    .ToDictionary(k => k.Key, v => v.ToList());
                List<CustomersvCodesInfo> resultList = new List<CustomersvCodesInfo>();
                foreach ((string key, List<CustomersvCodesInfo> value) in a)
                {
                    CustomersvCodesInfo item = new CustomersvCodesInfo();
                    item.CompanyName = key;
                    foreach (CustomersvCodesInfo areaItem in value)
                    {
                        item.svcodes += areaItem.svcodes + ",";
                    }
                    resultList.Add(item);
                }


                return resultList.OrderBy(i => i.CompanyName).ToList();


            }
        }


        public ApiTableOut<GtisAllUserUseLog_OUT> GetAllCustomerGtisUserLog(AllGtisUserUseLog_IN allGtisUserUseLog_IN)
        {
            ApiTableOut<GtisAllUserUseLog_OUT> r = new ApiTableOut<GtisAllUserUseLog_OUT>();
            string[] svcodes = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetGtisSvcodes(UserId, allGtisUserUseLog_IN.ContractServiceState);
            if (svcodes.Length == 0)
            {
                //throw new ApiException("无服务");
                return r;
            }
            var log = BLL_GtisOpe.Instance.GetAllUserOperateLogSta(string.Empty, svcodes).Result;
            List<GtisAllUserUseLog_OUT> list = new List<GtisAllUserUseLog_OUT>();
            List<GtisAllUserUseLog_OUT> lastList = new List<GtisAllUserUseLog_OUT>();
            //r.Data = log;
            var gtisInfo = Queryable
                               .LeftJoin<Db_crm_contract_serviceinfo_gtis_user>((csg, csgu) => csg.Id == csgu.ContractServiceInfoGtisId && csgu.Deleted == false)
                               .LeftJoin<Db_crm_contract>((csg, csgu, c) => csg.ContractId == c.Id && c.Deleted == false)
                               .LeftJoin<Db_crm_customer_subcompany>((csg, csgu, c, s) => s.Id == c.FirstParty && s.Deleted == (int)EnumCustomerDel.NotDel)
                               .LeftJoin<Db_crm_contract_productserviceinfo_gtis_appl>((csg, csgu, c, s, a) => a.Id == csg.ProductServiceInfoGtisApplId && a.Deleted == false)
                               .Where((csg, csgu) => SqlFunc.ContainsArray(new int[] { (int)EnumContractServiceState.VALID, (int)EnumContractServiceState.OUT }, csg.State))
                               .Where((csg, csgu) => csg.IsChanged == 0 && csg.IsApplHistory == false)
                               //.Where((csg, csgu) => csgu.AccountNumber == l.UID)
                               .Select((csg, csgu, c, s, a) => new
                               {
                                   AccountNumber = csgu.AccountNumber,
                                   ContractNum = csg.ContractNum,
                                   CustomerId = s.CustomerId,
                                   CustomerName = s.CompanyName,
                                   GtisServiceEndDate = csg.ServiceCycleEnd,
                                   ApplId = a.Id,
                               }).ToList();
            log.ForEach(l =>
            {
                GtisAllUserUseLog_OUT e = l.MappingTo<GtisAllUserUseLog_OUT>();
                var info = gtisInfo.Where(g => g.AccountNumber == l.UID).FirstOrDefault();
                if (info.IsNotNull())
                {
                    e.CustomerId = info.CustomerId;
                    e.ContractNum = info.ContractNum;
                    e.CustomerName = info.CustomerName;
                    e.GtisServiceEndDate = info.GtisServiceEndDate.Value.ToString("yyyy-MM-dd");
                    e.NotLoingDays = e.NotLoingDays.IsNullOrEmpty() ? "65535" : e.NotLoingDays;
                    string StateDesc = "";
                    if (l.Deleted == "0" && l.ManageDelete == "0")
                    {
                        StateDesc = "正常";
                    }
                    else
                    {
                        StateDesc = l.Deleted == "0" ? "未删除" : "已删除" + " " + l.ManageDelete == "0" ? "未禁用" : "已禁用";
                    }
                    e.StateDesc = StateDesc;
                }
                else
                {
                    e.CustomerId = "";
                    e.ContractNum = "";
                    e.CustomerName = "";
                    e.GtisServiceEndDate = "";
                    e.NotLoingDays = e.NotLoingDays.IsNullOrEmpty() ? "65535" : e.NotLoingDays;

                }
                lastList.Add(e);
            });

            var tmp = lastList.GroupBy(l => l.ContractNum).Select(it => new
            {
                Key = it.Key,
                NotLoingDays = it.Min(i => i.NotLoingDays),
                LastLoginDate = it.Min(i => i.LastLoginDate),
            }).ToList();

            tmp.ForEach(t =>
            {
                GtisAllUserUseLog_OUT resultItem = t.MappingTo<GtisAllUserUseLog_OUT>();
                var item = gtisInfo.Where(g => g.ContractNum == t.Key).FirstOrDefault();
                if (item.IsNotNull())
                {
                    resultItem.CustomerId = item.CustomerId;
                    resultItem.ContractNum = item.ContractNum;
                    resultItem.CustomerName = item.CustomerName;
                    resultItem.GtisServiceEndDate = item.GtisServiceEndDate.Value.ToString("yyyy-MM-dd");
                    resultItem.LastLoginPlace = lastList.Where(l => l.ContractNum == item.ContractNum).Select(l => l.LastLoginPlace).FirstOrDefault();
                    resultItem.ApplId = item.ApplId;
                }
                list.Add(resultItem);
            });

            List<GtisAllUserUseLog_OUT> s = new List<GtisAllUserUseLog_OUT>();

            switch (allGtisUserUseLog_IN.NotLoingDaysScope)
            {
                case EnumLoingDaysScope.ForAll:
                    s = list; break;
                case EnumLoingDaysScope.NeverLogin:
                    s = list.Where(l => l.NotLoingDays.ToInt() >= 65535).ToList();
                    break;
                case EnumLoingDaysScope.Morethan30:
                    s = list.Where(l => l.NotLoingDays.ToInt() >= 30 && l.NotLoingDays.ToInt() != 65535).ToList();
                    break;
                case EnumLoingDaysScope.Morethan45:
                    s = list.Where(l => l.NotLoingDays.ToInt() >= 45 && l.NotLoingDays.ToInt() != 65535).ToList();
                    break;
                case EnumLoingDaysScope.Morethan60:
                    s = list.Where(l => l.NotLoingDays.ToInt() >= 60 && l.NotLoingDays.ToInt() != 65535).ToList();
                    break;
                case EnumLoingDaysScope.Morethan90:
                    s = list.Where(l => l.NotLoingDays.ToInt() >= 90 && l.NotLoingDays.ToInt() != 65535).ToList();
                    break;
                default:
                    s = list;
                    break;
            }
            //r.Data = 
            //list.or
            //list.OrderByPropertyName(allGtisUserUseLog_IN.SortField, allGtisUserUseLog_IN.IsDESC ? OrderByType.Desc : OrderByType.Asc)
            //    .OrderByIF(StringUtil.IsNullOrEmpty(allGtisUserUseLog_IN.SortField), (r, u, ca, c) => r.CreateDate, OrderByType.Desc);

            s = Db.Reportable(s).ToQueryable().Select<GtisAllUserUseLog_OUT>().OrderByPropertyName(allGtisUserUseLog_IN.SortField, allGtisUserUseLog_IN.IsDESC ? OrderByType.Desc : OrderByType.Asc)
                                        .OrderByIF(StringUtil.IsNullOrEmpty(allGtisUserUseLog_IN.SortField), (i) => SqlFunc.ToInt32(i.NotLoingDays), OrderByType.Desc).ToList();
            r.Total = s.Count();
            s = s.Skip(allGtisUserUseLog_IN.PageSize * (allGtisUserUseLog_IN.PageNumber - 1)).Take(allGtisUserUseLog_IN.PageSize).ToList();
            r.Data = s;

            //r.Total = s.Count();
            return r;
        }

        public ApiTableOut<GtisAllUserUseLog_OUT> GetAllCustomerGtisUserLogNew(AllGtisUserUseLog_IN allGtisUserUseLog_IN)
        {
            ApiTableOut<GtisAllUserUseLog_OUT> r = new ApiTableOut<GtisAllUserUseLog_OUT>();

            string[] svcodes_in = allGtisUserUseLog_IN.svCodes == null ? GetGtisSvcodes(UserId, allGtisUserUseLog_IN.ContractServiceState) : string.Join(string.Empty, allGtisUserUseLog_IN.svCodes).Split(",").Where(s => !string.IsNullOrEmpty(s)).ToArray();

            // string[] svcodes = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetGtisSvcodes(UserId, allGtisUserUseLog_IN.ContractServiceState);
            if (svcodes_in.Length == 0)
            {
                //throw new ApiException("无服务");
                return r;
            }
            int sortField = 0;
            if (allGtisUserUseLog_IN.SortField == "NotLoingDays" || allGtisUserUseLog_IN.SortField.IsNull())
            {
                sortField = 0;
            }
            else
            {
                sortField = 1;
            }
            int ordertype = allGtisUserUseLog_IN.IsDESC.ToInt();
            var log = BLL_GtisOpe.Instance.GetAllUserOperateLogStaNew(svcodes_in, allGtisUserUseLog_IN.NotLoingDaysScope.ToInt(), allGtisUserUseLog_IN.ContractServiceState, allGtisUserUseLog_IN.PageNumber, allGtisUserUseLog_IN.PageSize, sortField, ordertype).Result;
            var newData = log.Data.ToList();
            List<GtisAllUserUseLog_OUT> list = new List<GtisAllUserUseLog_OUT>();
            List<GtisAllUserUseLog_OUT> lastList = new List<GtisAllUserUseLog_OUT>();

            var uid = newData.Select(u => u.UID).ToList();
            //var sysuserid = newData.Select(u => u.SysUserID).ToList();
            var gtisInfo = Queryable
                               .LeftJoin<Db_crm_contract_serviceinfo_gtis_user>((csg, csgu) => csg.Id == csgu.ContractServiceInfoGtisId && csgu.Deleted == false)
                               .LeftJoin<Db_crm_contract>((csg, csgu, c) => csg.ContractId == c.Id && c.Deleted == false)
                               .LeftJoin<Db_crm_customer_subcompany>((csg, csgu, c, s) => s.Id == c.FirstParty && s.Deleted == (int)EnumCustomerDel.NotDel)
                               .LeftJoin<Db_crm_contract_productserviceinfo_gtis_appl>((csg, csgu, c, s, a) => a.Id == csg.ProductServiceInfoGtisApplId && a.Deleted == false)
                               .WhereIF(allGtisUserUseLog_IN.ContractServiceState.ToInt() != 99, (csg, csgu) => csg.State == (int)allGtisUserUseLog_IN.ContractServiceState)
                               .WhereIF(allGtisUserUseLog_IN.ContractServiceState.ToInt() == 99, (csg, csgu) => SqlFunc.ContainsArray(new int[] { (int)EnumContractServiceState.VALID, (int)EnumContractServiceState.OUT }, csg.State))
                               //.Where((csg, csgu) => SqlFunc.ContainsArray(new int[] { (int)EnumContractServiceState.VALID, (int)EnumContractServiceState.OUT }, csg.State))
                               .Where((csg, csgu, c) => csg.IsChanged == 0 && csg.IsApplHistory == false)
                               .Where((csg, csgu, c) => uid.Contains(csgu.AccountNumber))
                               //.Where((csg, csgu, c) => sysuserid.Contains(csgu.UserId))
                               .Select((csg, csgu, c, s, a) => new
                               {
                                   AccountNumber = csgu.AccountNumber,
                                   UserId = csgu.UserId,
                                   ContractNum = csg.ContractNum,
                                   CustomerId = s.CustomerId,
                                   CustomerName = s.CompanyName,
                                   GtisServiceEndDate = csg.ServiceCycleEnd,
                                   ApplId = a.Id,
                               }).ToList();
            newData.ForEach(i =>
            {
                GtisAllUserUseLog_OUT item = i.MappingTo<GtisAllUserUseLog_OUT>();
                var eachInfo = gtisInfo.Where(g => g.AccountNumber == i.UID).FirstOrDefault();
                if (eachInfo != null)
                {
                    item.ApplId = eachInfo.ApplId;
                    item.UID = eachInfo.AccountNumber;
                    item.CustomerId = eachInfo.CustomerId;
                    item.CustomerName = eachInfo.CustomerName;
                    item.GtisServiceEndDate = i.EndServerDate.Value.ToString("yyyy-MM-dd");//eachInfo.GtisServiceEndDate.Value.ToString("yyyy-MM-dd");
                    item.ContractNum = eachInfo.ContractNum;
                    item.NotLoingDays = item.OtherInfo.IsEmpty() ? "0" : item.OtherInfo;
                    list.Add(item);
                }
            });


            r.Data = list;
            r.Total = log.Total;
            return r;
        }
        public GtisAllUserUseLog_OUT GetGtisAccountCountryInfoLog(GtisUseLog_IN gtisUseLog_IN)
        {
            ApiTableOut<GtisAllUserUseLog_OUT> r = new ApiTableOut<GtisAllUserUseLog_OUT>();
            string[] svcodes = new string[1];
            svcodes[0] = gtisUseLog_IN.SvCode;

            var log = BLL_GtisOpe.Instance.GetAllUserOperateLogSta(string.Empty, svcodes).Result;

            List<GtisAllUserUseLog_OUT> list = new List<GtisAllUserUseLog_OUT>();
            List<GtisAllUserUseLog_OUT> lastList = new List<GtisAllUserUseLog_OUT>();
            //r.Data = log;
            var gtisInfo = Queryable
                               .LeftJoin<Db_crm_contract_serviceinfo_gtis_user>((csg, csgu) => csg.Id == csgu.ContractServiceInfoGtisId && csgu.Deleted == false)
                               .LeftJoin<Db_crm_contract>((csg, csgu, c) => csg.ContractId == c.Id && c.Deleted == false)
                               .LeftJoin<Db_crm_customer_subcompany>((csg, csgu, c, s) => s.Id == c.FirstParty && s.Deleted == (int)EnumCustomerDel.NotDel)
                               .Where((csg, csgu) => SqlFunc.ContainsArray(new int[] { (int)EnumContractServiceState.VALID, (int)EnumContractServiceState.OUT }, csg.State))
                               .Where((csg, csgu) => csg.IsChanged == 0 && csg.IsApplHistory == false)
                               .Where((csg, csgu) => csgu.AccountNumber == gtisUseLog_IN.AccountNumber)
                               .Select((csg, csgu, c, s) => new
                               {
                                   AccountNumber = csgu.AccountNumber,
                                   ContractNum = csg.ContractNum,
                                   CustomerId = s.CustomerId,
                                   CustomerName = s.CompanyName,
                                   GtisServiceEndDate = csg.ServiceCycleEnd,
                               }).First();
            var info = log.Where(g => g.UID == gtisUseLog_IN.AccountNumber).FirstOrDefault();
            GtisAllUserUseLog_OUT e = info.MappingTo<GtisAllUserUseLog_OUT>();
            e.CustomerId = gtisInfo.CustomerId;
            e.ContractNum = gtisInfo.ContractNum;
            e.CustomerName = gtisInfo.CustomerName;
            e.GtisServiceEndDate = gtisInfo.GtisServiceEndDate.Value.ToString("yyyy-MM-dd");
            e.NotLoingDays = e.NotLoingDays;
            return e;
        }

        public ApiTableOut<BM_OneUserOperateLogByMonth> GetGtisUserOperateLogByMonthLog(GtisUseLog_IN gtisUseLog_IN)
        {
            ApiTableOut<BM_OneUserOperateLogByMonth> r = new ApiTableOut<BM_OneUserOperateLogByMonth>();
            //r.Data = BLL_GtisOpe.Instance.GetOneUserOpeLogStaByMonth(string.Empty, gtisUseLog_IN.PhoneId).Result;

            var log = BLL_GtisOpe.Instance.GetOneUserOpeLogStaByMonth(string.Empty, gtisUseLog_IN.PhoneId).Result;//.OrderByDescending(i => new { i.AppYear, i.AppMonths }).ToList();
            r.Data = Db.Reportable(log).ToQueryable().OrderByDescending(i => new { i.AppYear, i.AppMonths }).ToList();

            ////测试数据
            //List<BM_OneUserOperateLogByMonth> tlist = new List<BM_OneUserOperateLogByMonth>();
            //BM_OneUserOperateLogByMonth t = new BM_OneUserOperateLogByMonth();
            //t.AppMonths = 6;
            //t.AppYear = "2024";
            //t.SysUserID = string.Empty ;
            //t.SupportlineNum = 8;
            //t.HQSNum = 7;
            //t.VisualizationNum = 6;
            //t.ExportNum = 22;
            //t.TradesearchNum = 23;
            //t.ExportNum = 255;
            //t.SupportlineNum = 11;
            //t.DirectTrackNum = 13;
            //t.BrowseNum = 14;
            //tlist.Add(t);
            //r.Data = tlist;
            return r;
        }


        /// <summary>
        /// 获取客户所有公司的上一份GTIS合同的产品信息
        /// </summary>
        /// <param name="companyIds"></param>
        /// <param name="currentContractId"></param>
        /// <returns></returns>
        public List<GetOldContractGtisInfoByCompanyIdList_Out> GetOldContractGtisInfoByCompanyIdList(List<string> companyIds, string currentContractId)
        {

            var oldGtisInfoList = Queryable
                  .LeftJoin<Db_crm_contract>((gtis, contract) => gtis.ContractId == contract.Id && contract.Deleted == false)
                  .LeftJoin<Db_crm_product>((gtis, contract, product) => gtis.ProductId == product.Id && product.Deleted == false)
                  .LeftJoin<Db_crm_customer_subcompany>((gtis, contract, product, subcompay) => contract.FirstParty == subcompay.Id)
                  .LeftJoin<Db_crm_contract_serviceinfo_gtis_user>((gtis, contract, product, subcompay, user) => gtis.Id == user.ContractServiceInfoGlobalSearchId && user.AccountType == (int)EnumGtisAccountType.Main && user.Deleted == false)
                  .Where((gtis, contract) => companyIds.Contains(contract.FirstParty))
                  .Where((gtis, contract) => contract.Id != currentContractId)
                  .Where((gtis, contract) => !SqlFunc.IsNullOrEmpty(contract.ContractNum))
                  .Where((gtis, contract) => gtis.Deleted == false)
                  .Where((gtis, contract) => gtis.IsProcessed == (int)EnumGtisServiceIsProcess.Processed)
                  .Where((gtis, contract) => gtis.IsApplHistory == false)
                  .Where((gtis, contract) => gtis.State == (int)EnumContractServiceState.VALID || gtis.State == (int)EnumContractServiceState.OUT)
                  .Select((gtis, contract, product, subcompay, user) => new GetOldContractGtisInfoByCompanyIdList_Out
                  {
                      ProductName = product.ProductName,
                      CompanyName = subcompay.CompanyName,
                      ContractNum = contract.ContractNum,
                      CreateDate = gtis.CreateDate.Value,
                      CompanyId = contract.FirstParty,
                      UserId = user.UserId,
                  })
                  .ToList();
            var retList = new List<GetOldContractGtisInfoByCompanyIdList_Out>();
            companyIds.ForEach(companyId =>
            {
                var retObj = oldGtisInfoList.Where(e => e.CompanyId == companyId).OrderByDescending(e => e.CreateDate).FirstOrDefault();
                if (retObj != null)
                    retList.Add(retObj);
            });
            return retList;

        }

        /// <summary>
        /// 根据客户编码获取被续约服务信息
        /// </summary>
        /// <param name="ContractNum"></param>
        /// <returns></returns>
        public HistoryGtisServiceInfo_Mid GetHisGtisServiceInfoByContractNum(string ContractNum)
        {
            //获取真实客户编码
            var realContractNum = GetGtisServiceInfoContractNumByContractNum(ContractNum);
            return Queryable
                .LeftJoin<Db_crm_contract_productserviceinfo_gtis_appl>((e, f) => e.ProductServiceInfoGtisApplId == f.Id)
                .Where(e => e.ContractNum == realContractNum)
                .Where(e => e.Deleted == false && e.IsApplHistory == false && e.IsChanged == 0)
                .Where(e => e.State != (int)EnumContractServiceState.REFUSE)
                .Select((e, f) => new HistoryGtisServiceInfo_Mid
                {
                    Id = e.Id,
                    ContractNum = e.ContractNum,
                    PrimaryAccountsNum = e.PrimaryAccountsNum,
                    SubAccountsNum = e.SubAccountsNum,
                    SharePeopleNum = e.SharePeopleNum,
                    ShareUsageNum = e.ShareUsageNum,
                    AuthorizationNum = e.AuthorizationNum,
                    ServiceCycle = e.ServiceCycleStart.Value.ToString("yyyy-MM-dd") + "至" + e.ServiceCycleEnd.Value.ToString("yyyy-MM-dd"),
                    Remark = f.Remark4List,
                    OldContractNum = e.OldContractNum,
                    ProcessingType = e.ProcessingType,
                    ContractId = e.ContractId,
                    ApplyId = e.ProductServiceInfoGtisApplId,
                    CouponIds = f.CouponIds,
                    GlobalSearchAccountCount = e.GlobalSearchAccountCount,
                    GlobalSearchSettlementCount = e.GlobalSearchSettlementCount,
                    GlobalSearchRemark = e.GlobalSearchRemark,
                    ProductId = e.ProductId,
                    IsOpenCrm = e.IsOpenCrm,
                })
                .Mapper(it =>
                {
                    it.CouponCount = string.IsNullOrEmpty(it.CouponIds) ? 0 : it.CouponIds.Split(',').ToList().Count;
                    it.ProductName = DbOpe_crm_product.Instance.QueryByPrimaryKey(it.ProductId)?.ProductName;
                })
                .First();
        }

        /// <summary>
        /// 获取被变更的服务数据信息
        /// </summary>
        /// <param name="applyId"></param>
        /// <param name="contractNum"></param>
        /// <returns></returns>
        public HistoryGtisServiceInfo_Mid GetChangedGtisServiceInfoByApplyInfo(string applyId, string contractNum)
        {
            var apply = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.QueryByPrimaryKey(applyId);
            var hisGtis = new HistoryGtisServiceInfo_Mid();
            //获取真实客户编码
            var realContractNum = GetGtisServiceInfoContractNumByContractNum(contractNum); //2025.1.21注释 只在申请中的情况需要用到客户编码，这时候不存在客户编码被手动修改的情况
            if (apply.State == (int)EnumProcessStatus.Submit)
            {
                //如果当前申请是提交状态，可推导出当前的apply是最新的服务流程数据，需要寻找当前合同的最近的在服或过期服务数据
                hisGtis = Queryable
                    .LeftJoin<Db_crm_contract_productserviceinfo_gtis_appl>((e, f) => e.ProductServiceInfoGtisApplId == f.Id)
                    .Where(e => e.ContractNum == realContractNum)
                    .Where(e => e.Deleted == false && e.IsApplHistory == false && e.IsChanged == 0)
                    .Where(e => e.State == (int)EnumContractServiceState.VALID || e.State == (int)EnumContractServiceState.OUT)
                    .Select((e, f) => new HistoryGtisServiceInfo_Mid
                    {
                        Id = e.Id,
                        ContractNum = e.ContractNum,
                        PrimaryAccountsNum = e.PrimaryAccountsNum,
                        SubAccountsNum = e.SubAccountsNum,
                        SharePeopleNum = e.SharePeopleNum,
                        ShareUsageNum = e.ShareUsageNum,
                        AuthorizationNum = e.AuthorizationNum,
                        ServiceCycle = e.ServiceCycleStart.Value.ToString("yyyy-MM-dd") + "至" + e.ServiceCycleEnd.Value.ToString("yyyy-MM-dd"),
                        Remark = f.Remark4List,
                        OldContractNum = e.OldContractNum,
                        ProcessingType = e.ProcessingType,
                        ContractId = e.ContractId,
                        ApplyId = f.Id,
                        CouponIds = f.CouponIds,
                        AddFreeGitsServiceMonth = f.ServiceAddMonth.Value,
                        GlobalSearchAccountCount = e.GlobalSearchAccountCount,
                        GlobalSearchSettlementCount = e.GlobalSearchSettlementCount,
                        GlobalSearchRemark = e.GlobalSearchRemark,
                        ChangeReasonEnums = f.ChangeReasonEnums,
                        ProductId = e.ProductId,
                        IsOpenCrm = e.IsOpenCrm,
                    })
                    .Mapper(it =>
                    {
                        if (!string.IsNullOrEmpty(it.ChangeReasonEnums))
                        {
                            it.ChangeReasons = new List<ApplGtisInfo_OUT_ChangeReason>();
                            try
                            {
                                // 尝试解析JSON格式
                                var changeReasonData = System.Text.Json.JsonSerializer.Deserialize<System.Text.Json.JsonElement>(it.ChangeReasonEnums);
                                if (changeReasonData.ValueKind != System.Text.Json.JsonValueKind.Null && 
                                    changeReasonData.TryGetProperty("ChangeReasons", out var changeReasonsElement) &&
                                    changeReasonsElement.ValueKind == System.Text.Json.JsonValueKind.Array &&
                                    changeReasonsElement.GetArrayLength() > 0)
                                {
                                    foreach (var reason in changeReasonsElement.EnumerateArray())
                                    {
                                        if (reason.TryGetProperty("ChangeReason", out var changeReasonElement))
                                        {
                                            var changeReason = changeReasonElement.GetInt32();
                                            var reasonObj = new ApplGtisInfo_OUT_ChangeReason();
                                            reasonObj.ChangeReasonEnum = changeReason.ToEnum<EnumGtisServiceChangeProject>();
                                            reasonObj.ChangeReasonEnumName = reasonObj.ChangeReasonEnum.GetEnumDescription();
                                            it.ChangeReasons.Add(reasonObj);
                                        }
                                    }
                                }
                            }
                            catch (System.Text.Json.JsonException)
                            {
                                // JSON解析失败，尝试解析旧格式（兼容性处理）
                                var changeReasonEnumsList = it.ChangeReasonEnums.Split(",").ToList();
                                changeReasonEnumsList.ForEach(item =>
                                {
                                    var reason = new ApplGtisInfo_OUT_ChangeReason();
                                    reason.ChangeReasonEnum = item.ToEnum<EnumGtisServiceChangeProject>();
                                    reason.ChangeReasonEnumName = reason.ChangeReasonEnum.GetEnumDescription();
                                    it.ChangeReasons.Add(reason);
                                });
                            }
                        }
                        it.ProductName = DbOpe_crm_product.Instance.QueryByPrimaryKey(it.ProductId)?.ProductName;
                    })
                    .First();

            }
            else if (apply.State == (int)EnumProcessStatus.Pass)
            {
                //如果当前申请是通过状态，根据当前的applyId查找对应的在服数据,并根据此在服数据的historyId找到历史服务信息
                hisGtis = Queryable
                    .LeftJoin<Db_crm_contract_serviceinfo_gtis>((e, f) => e.Id == f.HistoryId && f.Deleted == false && f.IsApplHistory == false)
                    .LeftJoin<Db_crm_contract_productserviceinfo_gtis_appl>((e, f, g) => e.ProductServiceInfoGtisApplId == g.Id)
                    .Where((e, f, g) => f.ProductServiceInfoGtisApplId == apply.Id)
                    .Where(e => e.Deleted == false && e.IsApplHistory == false)
                    .Where((e, f, g) => g.IsInvalid == (int)EnumIsInvalid.Invalid)
                    .Select((e, f, g) => new HistoryGtisServiceInfo_Mid
                    {
                        Id = e.Id,
                        ContractNum = e.ContractNum,
                        PrimaryAccountsNum = e.PrimaryAccountsNum,
                        SubAccountsNum = e.SubAccountsNum,
                        SharePeopleNum = e.SharePeopleNum,
                        ShareUsageNum = e.ShareUsageNum,
                        AuthorizationNum = e.AuthorizationNum,
                        ServiceCycle = e.ServiceCycleStart.Value.ToString("yyyy-MM-dd") + "至" + e.ServiceCycleEnd.Value.ToString("yyyy-MM-dd"),
                        Remark = g.Remark4List,
                        OldContractNum = e.OldContractNum,
                        ProcessingType = e.ProcessingType,
                        ContractId = e.ContractId,
                        ApplyId = g.Id,
                        CouponIds = g.CouponIds,
                        AddFreeGitsServiceMonth = g.ServiceAddMonth.Value,
                        GlobalSearchAccountCount = e.GlobalSearchAccountCount,
                        GlobalSearchSettlementCount = e.GlobalSearchSettlementCount,
                        GlobalSearchRemark = e.GlobalSearchRemark,
                        ChangeReasonEnums = g.ChangeReasonEnums,
                        ProductId = e.ProductId,
                        IsOpenCrm = e.IsOpenCrm,
                    })
                    .Mapper(it =>
                    {
                        if (!string.IsNullOrEmpty(it.ChangeReasonEnums))
                        {
                            it.ChangeReasons = new List<ApplGtisInfo_OUT_ChangeReason>();
                            try
                            {
                                // 尝试解析JSON格式
                                var changeReasonData = System.Text.Json.JsonSerializer.Deserialize<System.Text.Json.JsonElement>(it.ChangeReasonEnums);
                                if (changeReasonData.ValueKind != System.Text.Json.JsonValueKind.Null &&
                                    changeReasonData.TryGetProperty("ChangeReasons", out var changeReasonsElement) &&
                                    changeReasonsElement.ValueKind == System.Text.Json.JsonValueKind.Array &&
                                    changeReasonsElement.GetArrayLength() > 0)
                                {
                                    foreach (var reason in changeReasonsElement.EnumerateArray())
                                    {
                                        if (reason.TryGetProperty("ChangeReason", out var changeReasonElement))
                                        {
                                            var changeReason = changeReasonElement.GetInt32();
                                            var reasonObj = new ApplGtisInfo_OUT_ChangeReason();
                                            reasonObj.ChangeReasonEnum = changeReason.ToEnum<EnumGtisServiceChangeProject>();
                                            reasonObj.ChangeReasonEnumName = reasonObj.ChangeReasonEnum.GetEnumDescription();
                                            it.ChangeReasons.Add(reasonObj);
                                        }
                                    }
                                }
                            }
                            catch (System.Text.Json.JsonException)
                            {
                                // JSON解析失败，尝试解析旧格式（兼容性处理）
                                var changeReasonEnumsList = it.ChangeReasonEnums.Split(",").ToList();
                                changeReasonEnumsList.ForEach(item =>
                                {
                                    var reason = new ApplGtisInfo_OUT_ChangeReason();
                                    reason.ChangeReasonEnum = item.ToEnum<EnumGtisServiceChangeProject>();
                                    reason.ChangeReasonEnumName = reason.ChangeReasonEnum.GetEnumDescription();
                                    it.ChangeReasons.Add(reason);
                                });
                            }
                        }
                        it.ProductName = DbOpe_crm_product.Instance.QueryByPrimaryKey(it.ProductId)?.ProductName;
                    })
                    .First();
            }
            else if (apply.State == (int)EnumProcessStatus.Refuse)
            {
                //如果当前申请是拒绝状态，查找CreateDate早于当前申请CreateDate的通过的申请数据，再用那条申请查询
                var oldApply = Db.Queryable<Db_crm_contract_productserviceinfo_gtis_appl>()
                    .Where(e => e.ContractProductInfoId == apply.ContractProductInfoId)
                    .Where(e => e.Deleted == false && e.State == (int)EnumProcessStatus.Pass)
                    .Where(e => e.CreateDate < apply.CreateDate)
                    .OrderByDescending(e => e.CreateDate)
                    .First();

                hisGtis = Queryable
                    .LeftJoin<Db_crm_contract_productserviceinfo_gtis_appl>((e, f) => e.ProductServiceInfoGtisApplId == f.Id)
                    .Where(e => e.ProductServiceInfoGtisApplId == oldApply.Id)
                    .Where(e => e.Deleted == false && e.IsApplHistory == false)
                    .Where(e => e.State == (int)EnumContractServiceState.VALID || e.State == (int)EnumContractServiceState.OUT)
                    .Select((e, f) => new HistoryGtisServiceInfo_Mid
                    {
                        Id = e.Id,
                        ContractNum = e.ContractNum,
                        PrimaryAccountsNum = e.PrimaryAccountsNum,
                        SubAccountsNum = e.SubAccountsNum,
                        SharePeopleNum = e.SharePeopleNum,
                        ShareUsageNum = e.ShareUsageNum,
                        AuthorizationNum = e.AuthorizationNum,
                        ServiceCycle = e.ServiceCycleStart.Value.ToString("yyyy-MM-dd") + "至" + e.ServiceCycleEnd.Value.ToString("yyyy-MM-dd"),
                        Remark = f.Remark4List,
                        OldContractNum = e.OldContractNum,
                        ProcessingType = e.ProcessingType,
                        ContractId = e.ContractId,
                        ApplyId = f.Id,
                        CouponIds = f.CouponIds,
                        AddFreeGitsServiceMonth = f.ServiceAddMonth.Value,
                        GlobalSearchAccountCount = e.GlobalSearchAccountCount,
                        GlobalSearchSettlementCount = e.GlobalSearchSettlementCount,
                        GlobalSearchRemark = e.GlobalSearchRemark,
                        ChangeReasonEnums = f.ChangeReasonEnums,
                        ProductId = e.ProductId,
                        IsOpenCrm = e.IsOpenCrm,
                    })
                    .Mapper(it =>
                    {
                        if (!string.IsNullOrEmpty(it.ChangeReasonEnums))
                        {
                            it.ChangeReasons = new List<ApplGtisInfo_OUT_ChangeReason>();
                            try
                            {
                                // 尝试解析JSON格式
                                var changeReasonData = System.Text.Json.JsonSerializer.Deserialize<System.Text.Json.JsonElement>(it.ChangeReasonEnums);
                                if (changeReasonData.ValueKind != System.Text.Json.JsonValueKind.Null && 
                                    changeReasonData.TryGetProperty("ChangeReasons", out var changeReasonsElement) &&
                                    changeReasonsElement.ValueKind == System.Text.Json.JsonValueKind.Array &&
                                    changeReasonsElement.GetArrayLength() > 0)
                                {
                                    foreach (var reason in changeReasonsElement.EnumerateArray())
                                    {
                                        if (reason.TryGetProperty("ChangeReason", out var changeReasonElement))
                                        {
                                            var changeReason = changeReasonElement.GetInt32();
                                            var reasonObj = new ApplGtisInfo_OUT_ChangeReason();
                                            reasonObj.ChangeReasonEnum = changeReason.ToEnum<EnumGtisServiceChangeProject>();
                                            reasonObj.ChangeReasonEnumName = reasonObj.ChangeReasonEnum.GetEnumDescription();
                                            it.ChangeReasons.Add(reasonObj);
                                        }
                                    }
                                }
                            }
                            catch (System.Text.Json.JsonException)
                            {
                                // JSON解析失败，尝试解析旧格式（兼容性处理）
                                var changeReasonEnumsList = it.ChangeReasonEnums.Split(",").ToList();
                                changeReasonEnumsList.ForEach(item =>
                                {
                                    var reason = new ApplGtisInfo_OUT_ChangeReason();
                                    reason.ChangeReasonEnum = item.ToEnum<EnumGtisServiceChangeProject>();
                                    reason.ChangeReasonEnumName = reason.ChangeReasonEnum.GetEnumDescription();
                                    it.ChangeReasons.Add(reason);
                                });
                            }
                        }
                        it.ProductName = DbOpe_crm_product.Instance.QueryByPrimaryKey(it.ProductId)?.ProductName;
                    })
                    .First();
            }
            if (hisGtis != null && !string.IsNullOrEmpty(hisGtis.CouponIds))
                hisGtis.CouponCount = hisGtis.CouponIds.Split(',').ToList().Count;
            return hisGtis;
        }

        public Db_crm_contract_serviceinfo_gtis GetGtisServiceInfoByContractNum(string contractNum)
        {
            return Queryable
                .Where(e => e.ContractNum == contractNum)
                .Where(e => e.Deleted == false && e.IsApplHistory == false && e.IsChanged == 0)
                .Where(e => e.State == (int)EnumContractServiceState.VALID || e.State == (int)EnumContractServiceState.OUT)
                .First();
        }


        /// <summary>
        /// 根据Id作废gtis服务
        /// </summary>
        /// <param name="servId"></param>
        public void VoidGtisServiceByServiceId(string servId)
        {
            Updateable
                .SetColumns(e => e.State == (int)EnumContractServiceState.VOID)
                .SetColumns(e => e.UpdateDate == DateTime.Now)
                .SetColumns(e => e.UpdateUser == UserId)
                .Where(e => e.Id == servId)
                .ExecuteCommand();
        }

        public GtisInfo_OUT GetGtisInfoByContractNum(string contractNum)
        {
            GtisInfo_OUT retObj = null;
            List<int> states = new List<int>() { (int)EnumContractServiceState.VALID, (int)EnumContractServiceState.OUT };
            retObj = Queryable
                .LeftJoin<Db_crm_contract_productserviceinfo_gtis_appl>((serv, appl) => serv.ProductServiceInfoGtisApplId == appl.Id)
                .Where(serv => serv.Deleted == false)
                .Where(serv => serv.IsChanged == (int)EnumContractServiceInfoIsChange.Not)
                .Where(serv => serv.IsApplHistory == false)
                .Where(serv => serv.ContractNum == contractNum)
                .Where(serv => SqlFunc.ContainsArray(states, serv.State))
                .OrderByDescending(serv => new { serv.UpdateDate, serv.CreateDate })
                .Select((serv, appl) => new GtisInfo_OUT
                {
                    Id = serv.Id.SelectAll(),
                    /* ProductServiceInfoGtisApplId = serv.ProductServiceInfoGtisApplId,
                     AccountGenerationMethod = serv.AccountGenerationMethod,
                     ContractNum = serv.ContractNum,
                     ContractProductInfoId = serv.ContractProductInfoId,
                     ContractId = serv.ContractId,
                     ProductId = serv.ProductId,
                     State = serv.State,
                     PrimaryAccountsNum = serv.PrimaryAccountsNum,
                     SubAccountsNum = serv.SubAccountsNum,
                     SharePeopleNum = serv.SharePeopleNum,
                     ShareUsageNum = serv.ShareUsageNum,
                     AuthorizationNum = serv.AuthorizationNum,
                     ServiceCycleStart = serv.ServiceCycleStart,
                     ServiceCycleEnd = serv.ServiceCycleEnd,
                     ServiceMonth = serv.ServiceMonth,
                     ForbidSearchExport = serv.ForbidSearchExport,
                     WordRptPermissions = serv.WordRptPermissions,
                     WordRptMaxTimes = serv.WordRptMaxTimes,
                     IsGtisOldCustomer = serv.IsGtisOldCustomer,
                     AllCountrySubUser = serv.AllCountrySubUser,
                     Remark = serv.Remark,*/
                    SendAccount = appl.SendAccount,
                    SendAccountTime = appl.SendAccountTime,
                    ChangeReasonEnums = appl.ChangeReasonEnums,
                })
                .Mapper(it =>
                {
                    //常驻国家
                    it.ResidentCountries = DbOpe_crm_contract_serviceinfo_gtis_residentcountry.Instance.GetDataList(u => u.ContractServiceInfoGtisId == it.Id).MappingTo<List<GtisResidentCountry>>();
                    //常驻城市
                    it.ResidentCitys = DbOpe_crm_contract_serviceinfo_gtis_residentcity.Instance.GetDataList(u => u.ContractServiceInfoGtisId == it.Id).MappingTo<List<GtisResidentCity>>();
                    //选择国家
                    it.GtisRetailCountry = new List<GtisRetailCountry_OUT>();
                    if (it.RetailCountry == (int)EnumGtisRetailCountry.Add)
                    {
                        var countrys = DbOpe_sys_g4_dbnames.Instance.GetDataAllList().Select<G4DbNames>().ToList();
                        var applCountrys = DbOpe_crm_contract_serviceinfo_gtis_country.Instance.GetDataList(u => u.ContractServiceInfoGtisId == it.Id);
                        foreach (var country in applCountrys)
                        {
                            GtisRetailCountry_OUT gtisRetailCountry = country.MappingTo<GtisRetailCountry_OUT>();
                            var c = countrys.Find(c => c.SID == gtisRetailCountry.Sid);
                            if (c != null)
                            {
                                gtisRetailCountry.SidName = c.CountryName;
                                it.GtisRetailCountry.Add(gtisRetailCountry);
                            }
                        }
                    }
                    //服务周期
                    it.ServiceCycle = (it.ServiceCycleStart == null ? "" : it.ServiceCycleStart.Value.ToString("yyyy-MM-dd")) + "至" + (it.ServiceCycleEnd == null ? "" : it.ServiceCycleEnd.Value.ToString("yyyy-MM-dd"));
                    //
                    it.GtisUserInfo = Db.Queryable<Db_crm_contract_serviceinfo_gtis_user>()
                        .Where(e => e.ContractServiceInfoGtisId == it.Id)
                        .Where(e => e.Deleted == false)
                        .Where(e => e.OpeningStatus != (int)EnumGtisUserOpeningStatus.NotFound && e.OpeningStatus != (int)EnumGtisUserOpeningStatus.INPROCESS && e.OpeningStatus != (int)EnumGtisUserOpeningStatus.BackManageStop)
                        .Select<GtisUserInfo_OUT>()
                        .OrderBy(e => e.AccountNumber)
                        .OrderByDescending(e => e.AccountType)
                        .ToList();
                    //if (it.State == 1)
                    //    it.GtisUserInfo4ChangeApply = it.GtisUserInfo.Where(e => e.OpeningStatus == (int)EnumGtisUserOpeningStatus.Ok).ToList();
                    //else if (it.State == 2)
                    //    it.GtisUserInfo4ChangeApply = it.GtisUserInfo.Where(e => e.OpeningStatus == (int)EnumGtisUserOpeningStatus.Ok || e.OpeningStatus == (int)EnumGtisUserOpeningStatus.Expired).ToList();
                    it.GtisUserInfo4ChangeApply = it.GtisUserInfo;//2025.1.17 需求707 客户主动停用账号应不被过滤
                    it.CouponCount = string.IsNullOrEmpty(it.CouponIds) ? 0 : it.CouponIds.Split(',').ToList().Count;
                    if (!string.IsNullOrEmpty(it.ChangeReasonEnums))
                    {
                        it.ChangeReasons = new List<ApplGtisInfo_OUT_ChangeReason>();
                        try
                        {
                            // 尝试解析JSON格式
                            var changeReasonData = System.Text.Json.JsonSerializer.Deserialize<System.Text.Json.JsonElement>(it.ChangeReasonEnums);
                            if (changeReasonData.ValueKind != System.Text.Json.JsonValueKind.Null &&
                                changeReasonData.TryGetProperty("ChangeReasons", out var changeReasonsElement) &&
                                changeReasonsElement.ValueKind == System.Text.Json.JsonValueKind.Array &&
                                changeReasonsElement.GetArrayLength() > 0)
                            {
                                foreach (var reason in changeReasonsElement.EnumerateArray())
                                {
                                    if (reason.TryGetProperty("ChangeReason", out var changeReasonElement))
                                    {
                                        var changeReason = changeReasonElement.GetInt32();
                                        var reasonObj = new ApplGtisInfo_OUT_ChangeReason();
                                        reasonObj.ChangeReasonEnum = changeReason.ToEnum<EnumGtisServiceChangeProject>();
                                        reasonObj.ChangeReasonEnumName = reasonObj.ChangeReasonEnum.GetEnumDescription();
                                        it.ChangeReasons.Add(reasonObj);
                                    }
                                }
                            }
                        }
                        catch (System.Text.Json.JsonException)
                        {
                            // JSON解析失败，尝试解析旧格式（兼容性处理）
                            var changeReasonEnumsList = it.ChangeReasonEnums.Split(",").ToList();
                            changeReasonEnumsList.ForEach(item =>
                            {
                                var reason = new ApplGtisInfo_OUT_ChangeReason();
                                reason.ChangeReasonEnum = item.ToEnum<EnumGtisServiceChangeProject>();
                                reason.ChangeReasonEnumName = reason.ChangeReasonEnum.GetEnumDescription();
                                it.ChangeReasons.Add(reason);
                            });
                        }
                    }
                    /*it.GtisUserInfo.Sort((p1, p2) =>
                    {
                        if (p2.AccountType == null || p1.AccountType == null)
                        {
                            return 0;
                        }
                        else
                        {
                            return p2.AccountType.Value.CompareTo(p1.AccountType.Value);
                        }
                    });*/
                })
                .First();
            return retObj;
        }

        /// <summary>
        /// 根据当前服务的申请Id，获取被续约服务的信息。通过renewContractNum相连
        /// </summary>
        /// <param name="ApplId"></param>
        /// <returns></returns>
        public Db_crm_contract_serviceinfo_gtis GetOldGtisServiceByCurApplyId(string ApplId)
        {
            return Queryable
                    .LeftJoin<Db_crm_contract_productserviceinfo_gtis_appl>((oldServ, curAppl) => oldServ.ContractNum == curAppl.RenewContractNum)
                    .Where((oldServ, curAppl) => oldServ.Deleted == false && oldServ.IsApplHistory == false && oldServ.IsChanged == 0)
                    .Where((oldServ, curAppl) => curAppl.Id == ApplId)
                    .Select<Db_crm_contract_serviceinfo_gtis>((oldServ, curAppl) => oldServ)
                    .First();
        }

        /// <summary>
        /// 根据合同产品表Id获取当前服务信息
        /// </summary>
        /// <param name="conProInfoId"></param>
        /// <returns></returns>
        public Db_crm_contract_serviceinfo_gtis GetGtisServiceByContractProductInfoId(string conProInfoId)
        {
            return Queryable
                    .Where(e => e.Deleted == false && e.IsApplHistory == false && e.IsChanged == 0)
                    .Where(e => e.State == (int)EnumContractServiceState.VALID || e.State == (int)EnumContractServiceState.OUT)
                    .Where(e => e.ContractProductInfoId == conProInfoId)
                    .First();
        }

        /// <summary>
        /// 根据合同客户编码获取服务客户编码
        /// </summary>
        /// <param name="contractNum"></param>
        /// <returns></returns>
        public string GetGtisServiceInfoContractNumByContractNum(string contractNum)
        {
            var realContractNum = Queryable
                .LeftJoin<Db_crm_contract>((serv, con) => serv.ContractId == con.Id && con.Deleted == false)
                .Where((serv, con) => con.ContractNum == contractNum)
                .Where((serv, con) => serv.Deleted == false && serv.IsApplHistory == false && serv.IsChanged == 0)
                .Where((serv, con) => serv.State == (int)EnumContractServiceState.VALID || serv.State == (int)EnumContractServiceState.OUT)//审核通过的 过期的也算
                .Select((serv, con) => serv.ContractNum)
                .First();
            //如果不存在正常或过期的服务，继续查找失效的服务
            if (string.IsNullOrEmpty(realContractNum))
            {
                realContractNum = Queryable
                .LeftJoin<Db_crm_contract>((serv, con) => serv.ContractId == con.Id && con.Deleted == false)
                .Where((serv, con) => con.ContractNum == contractNum)
                .Where((serv, con) => serv.Deleted == false && serv.IsApplHistory == false && serv.IsChanged == 0)
                .Where((serv, con) => serv.State == (int)EnumContractServiceState.INVALID)
                .OrderByDescending((serv, con) => serv.CreateDate)
                .Select((serv, con) => serv.ContractNum)
                .First();
            }


            return realContractNum;

        }


        public List<SearchServicesList4ServeManage_OUT> SearchSericesList(SearchServicesList4ServeManage_OUT_IN condition, ref int total)
        {
            int pD = DbOpe_crm_customer_privatepool.Instance.GetProtectWarningDays();
            int sD = DbOpe_crm_customer_privatepool.Instance.GetServiceWarningDays();
            var dt = DateTime.Now;
            var dtDay = DateTime.Parse(dt.ToString("yyyy-MM-dd"));
            var weekEnd = dt.AddDays(1 - Convert.ToInt32(dt.DayOfWeek.ToString("d"))).AddDays(6).ToString("yyyy-MM-dd");
            var weekStart = dt.AddDays(1 - Convert.ToInt32(dt.DayOfWeek.ToString("d"))).ToString("yyyy-MM-dd");
            List<SearchServicesList4ServeManage_OUT> r = new List<SearchServicesList4ServeManage_OUT>();
            r = Db.Queryable<Db_v_product_contract_service_info_receipt_singlevip>()
                //.LeftJoin<Db_crm_contract>((service, contract) => service.ContractId == contract.Id && contract.Deleted == false)
                .LeftJoin<Db_crm_customer_subcompany>((service, /*contract,*/ company) => service.FirstParty == company.Id && company.Deleted == (int)EnumCustomerDel.NotDel)
                .LeftJoin<Db_crm_customer_subcompany>((service, /*contract,*/ company, customer) => company.CustomerId == customer.CustomerId && customer.IsMain == (int)EnumCustomerCompanyMain.Main)
                .LeftJoin<Db_v_customer>((service, /*contract,*/ company, customer, customerAuth) => customerAuth.CustomerId == company.CustomerId)
                .LeftJoin<Db_crm_contract_serviceinfo_gtis>((service, /*contract,*/ company, customer, customerAuth, gtis) => service.applId == gtis.ProductServiceInfoGtisApplId && gtis.IsApplHistory == false && gtis.Deleted == false && gtis.IsChanged == (int)EnumContractServiceInfoIsChange.Not)
                .LeftJoin<Db_v_userwithorg>((service, /*contract,*/ company, customer, customerAuth, gtis, user) => customerAuth.UserId == user.Id)
                .WhereIF(!string.IsNullOrEmpty(condition.ContractNum), (service, /*contract,*/ company, customer, customerAuth, gtis) => service.ContractNum == condition.ContractNum)
                .WhereIF(!string.IsNullOrEmpty(condition.FirstPartyName), (service, /*contract,*/ company, customer, customerAuth, gtis) => company.CompanyName.Contains(condition.FirstPartyName))
                .WhereIF(!string.IsNullOrEmpty(condition.ContractName), (service, /*contract,*/ company, customer, customerAuth, gtis) => service.ContractName.Contains(condition.ContractName))
                .WhereIF(!string.IsNullOrEmpty(condition.KeyWords), (service, /*contract,*/ company, customer, customerAuth, gtis) => service.ContractNum == condition.KeyWords || service.ContractName.Contains(condition.KeyWords) || company.CompanyName.Contains(condition.KeyWords))
                .WhereIF(!ArrayUtil.IsNullOrEmpty(condition.Products), (service, /*contract,*/ company, customer, customerAuth, gtis) => SqlFunc.ContainsArray(condition.Products, service.ProductId))
                .WhereIF(!ArrayUtil.IsNullOrEmpty(condition.ProductTypes), (service, /*contract,*/ company, customer, customerAuth, gtis) => SqlFunc.ContainsArray(condition.ProductTypes, service.ProductType))
                .WhereIF(condition.ProtectDateStart != null, (service, /*contract,*/ company, customer, customerAuth, gtis) => customerAuth.ProtectionDeadline != null && condition.ProtectDateStart != null && SqlFunc.ToDateShort(condition.ProtectDateStart.Value) <= SqlFunc.ToDateShort(customerAuth.ProtectionDeadline.Value))
                .WhereIF(condition.ProtectDateEnd != null, (service, /*contract,*/ company, customer, customerAuth, gtis) => customerAuth.ProtectionDeadline != null && condition.ProtectDateEnd != null && SqlFunc.ToDateShort(condition.ProtectDateEnd.Value) >= SqlFunc.ToDateShort(customerAuth.ProtectionDeadline.Value))
                .WhereIF(condition.ServiceEndDateStart != null, (service, /*contract,*/ company, customer, customerAuth, gtis) => service.ServiceCycleEnd != null && condition.ServiceEndDateStart != null && SqlFunc.ToDateShort(condition.ServiceEndDateStart.Value) <= SqlFunc.ToDateShort(service.ServiceCycleEnd.Value))
                .WhereIF(condition.ServiceEndDateEnd != null, (service, /*contract,*/ company, customer, customerAuth, gtis) => service.ServiceCycleEnd != null && condition.ServiceEndDateEnd != null && SqlFunc.ToDateShort(condition.ServiceEndDateEnd.Value) >= SqlFunc.ToDateShort(service.ServiceCycleEnd.Value))
                .WhereIF(condition.ServiceStartDateStart != null, (service, /*contract,*/ company, customer, customerAuth, gtis) => service.ServiceCycleStart != null && condition.ServiceStartDateStart != null && SqlFunc.ToDateShort(condition.ServiceStartDateStart.Value) <= SqlFunc.ToDateShort(service.ServiceCycleStart.Value))
                .WhereIF(condition.ServiceStartDateEnd != null, (service, /*contract,*/ company, customer, customerAuth, gtis) => service.ServiceCycleStart != null && condition.ServiceStartDateEnd != null && SqlFunc.ToDateShort(condition.ServiceStartDateEnd.Value) >= SqlFunc.ToDateShort(service.ServiceCycleStart.Value))
                .WhereIF(!string.IsNullOrEmpty(condition.UserId), (service, /*contract,*/ company, customer, customerAuth, gtis) => customerAuth.UserId == condition.UserId)
                .WhereIF(condition.IsReceipt != null && condition.IsReceipt.Count > 0, (service, /*contract,*/ company, customer, customerAuth, gtis) => condition.IsReceipt.Contains(SqlFunc.IIF(service.IsReceipt == null, 3, service.IsReceipt.Value)))
                .WhereIF(condition.BelongingMonthStart != null, (service, /*contract,*/ company, customer, customerAuth, gtis) => SqlFunc.ToDateShort(service.BelongingMonth.Value) >= SqlFunc.ToDateShort(condition.BelongingMonthStart))
                .WhereIF(condition.BelongingMonthEnd != null, (service, /*contract,*/ company, customer, customerAuth, gtis) => SqlFunc.ToDateShort(service.BelongingMonth.Value) <= SqlFunc.ToDateShort(condition.BelongingMonthEnd))
                //.Where((service, /*contract,*/ company, customer, customerAuth, gtis) => contract.ContractStatus == (int)EnumContractStatus.Pass)
                .Where((service, /*contract,*/ company, customer, customerAuth, gtis) => customerAuth.privatePool == 1)
                .Select((service, /*contract,*/ company, customer, customerAuth, gtis, user) => new SearchServicesList4ServeManage_OUT()
                {
                    Id = service.Id,
                    ApplId = service.applId,
                    ContractNum = service.ContractNum,
                    FirstPartyName = company.CompanyName,
                    ContractName = service.ContractName,
                    ProductName = service.ProductName + ((!SqlFunc.IsNullOrEmpty(gtis.Id) && gtis.RetailCountry == (int)EnumGtisRetailCountry.Add && service.ProductType != (int)EnumProductType.Vip) ? "（定制零售国家）" : ""),
                    ServiceCycle = (service.ServiceCycleStart == null || service.ServiceCycleEnd == null) ? "--" : service.ServiceCycleStart.Value.ToString("yyyy-MM-dd") + "至" + service.ServiceCycleEnd.Value.ToString("yyyy-MM-dd"),
                    CustomerName = customer.CompanyName,
                    ProtectionDeadline = customerAuth.ProtectionDeadline == null ? "" : customerAuth.ProtectionDeadline.Value.ToString("yyyy-MM-dd"),
                    State = SqlFunc.ToInt32(service.ServiceState),
                    ProtectionDeadlineDt = customerAuth.ProtectionDeadline == null ? DateTime.MaxValue : customerAuth.ProtectionDeadline.Value,
                    ServiceCycleEndDt = service.ServiceCycleEnd == null ? DateTime.MaxValue : service.ServiceCycleEnd.Value,
                    ServiceEnd = service.ServiceCycleEnd == null ? "" : service.ServiceCycleEnd.Value.ToString("yyyy-MM-dd 00:00"),
                    ServiceStart = service.ServiceCycleStart == null ? "" : service.ServiceCycleStart.Value.ToString("yyyy-MM-dd 00:00"),
                    CustomerId = company.CustomerId,
                    ProductType = service.ProductType,
                    ApplState = SqlFunc.ToInt32(service.ApplState),
                    SendAccountTimeDt = service.SendAccountTime,
                    SalesServiceState = EnumSalesServiceState.PENDING,
                    SalesServiceStateName = "",
                    ProductTypeName = "",
                    SigningDate = service.SigningDate == null ? "" : service.SigningDate.Value.ToString("yyyy-MM-dd"),
                    ServiceType = service.ProcessingType,
                    IsReceipt = service.IsReceipt,
                    BelongingMonth = service.BelongingMonth,
                    UserName = user.UserWithOrgFullName,
                    IsReceiptName = "",
                    CouldApplyService = false,
                    CouldUpdateService = false,
                }, true)
                .MergeTable()
                .Select(it => new SearchServicesList4ServeManage_OUT()
                {
                    Id = it.Id,
                    //it.StateName = ((EnumContractServiceState)it.State).GetEnumDescription();
                    NearlyRealese = SqlFunc.DateDiff(DateType.Day, dtDay, it.ProtectionDeadlineDt) <= pD,
                    NearlyServiceEnd = false,
                    SendAccountTime = SqlFunc.IF(it.ProductType == (int)EnumProductType.Vip ||
                    it.ProductType == (int)EnumProductType.Global ||
                    it.ProductType == (int)EnumProductType.Gtis ||
                    it.ProductType == (int)EnumProductType.Combination
                    ).Return(it.SendAccountTimeDt).End("--"),
                    SalesServiceState = EnumSalesServiceState.PENDING,
                    ProductTypeName = ""
                    //NearlyRealese = dtDay.Subtract(it.ProtectionDeadlineDt).TotalDays <= 15;,
                    //NearlyServiceEnd = dtDay.Subtract(it.ServiceCycleEndDt).TotalDays <= 15 && it.State == (int)EnumContractServiceState.VALID;
                }, true)
                .MergeTable()
                .Select(it => new SearchServicesList4ServeManage_OUT()
                {
                    Id = it.Id,
                    SalesServiceState = SqlFunc.IF(it.ApplState == null)
                    .Return(EnumSalesServiceState.NOTAPPL)
                    .ElseIF(it.ApplState == 1 && it.State == null)
                    .Return(EnumSalesServiceState.PENDING)
                    .ElseIF(it.ApplState == 1)
                    .Return(EnumSalesServiceState.PROCESS)
                    .ElseIF(it.ApplState == 3)
                    .Return(EnumSalesServiceState.REFUSE)
                    .ElseIF(it.ApplState == 4)
                    .Return(EnumSalesServiceState.VOID)
                    .ElseIF(it.State == (int)EnumContractServiceState.VALID && string.IsNullOrEmpty(it.SendAccountTime) && it.ProductType.Value != (int)EnumProductType.Global)
                    .Return(EnumSalesServiceState.PROCESS)
                    .ElseIF(it.State == (int)EnumContractServiceState.VALID)
                    .Return(EnumSalesServiceState.VALID)
                    .ElseIF(it.State == (int)EnumContractServiceState.OUT)
                    .Return(EnumSalesServiceState.OVERDUE)
                    .ElseIF(it.State == (int)EnumContractServiceState.VOID || it.State == (int)EnumContractServiceState.INVALID)
                    .Return(EnumSalesServiceState.VOID)
                    .ElseIF(it.State == (int)EnumContractServiceState.REFUSE)
                    .Return(EnumSalesServiceState.REFUSE)
                    .End(EnumSalesServiceState.PROCESS),
                    ProductTypeName = "",
                }, true)
                .MergeTable()
                .WhereIF(!ArrayUtil.IsNullOrEmpty(condition.State), it => SqlFunc.ContainsArray(condition.State, it.SalesServiceState))
                .Select(it => new SearchServicesList4ServeManage_OUT()
                {
                    Id = it.Id,
                    NearlyServiceEnd = SqlFunc.DateDiff(DateType.Day, dtDay, it.ServiceCycleEndDt) <= sD && it.SalesServiceState == EnumSalesServiceState.VALID,
                }, true)
                .MergeTable()
                .OrderByIF(StringUtil.IsNullOrEmpty(condition.SortField), it => new { it.NearlyServiceEnd }, OrderByType.Desc)
                .OrderByIF((condition.SortField == "ServiceEnd" || StringUtil.IsNullOrEmpty(condition.SortField)), it => SqlFunc.IIF(SqlFunc.IsNullOrEmpty(it.ServiceEnd), 1, 0))  //ServiceEnd排序时 将null值排序到最后（没服务）
                .OrderByIF((condition.SortField == "ServiceStart" || StringUtil.IsNullOrEmpty(condition.SortField)), it => SqlFunc.IIF(SqlFunc.IsNullOrEmpty(it.ServiceStart), 1, 0))  //ServiceStart排序时 将null值排序到最后（没服务）
                .OrderByPropertyName(condition.SortField, condition.IsDESC ? OrderByType.Desc : OrderByType.Asc)
                .OrderByIF(StringUtil.IsNullOrEmpty(condition.SortField), "ServiceStart desc")
                .OrderByPropertyName("Id")
                .Mapper(it =>
                {
                    it.SalesServiceStateName = it.SalesServiceState.GetEnumDescription();
                    it.ProductTypeName = it.ProductType == null ? "" : ((EnumProductType)it.ProductType.Value).GetEnumDescription();
                    if (DbOpe_crm_contract_productinfo.Instance.CheckContractHasGlobalSearchProduct(it.ContractId))
                    {
                        if (it.ProductName.Contains("（定制零售国家）"))
                            it.ProductName.Replace("（定制零售国家）", "（环球搜+定制零售国家）");
                        else
                            it.ProductName += "（环球搜）";
                    }
                    //判断是否是单环球搜
                    if (it.ProductType.Value == (int)EnumProductType.Global)
                    {
                        var gtisCase = DbOpe_crm_contract_productinfo.Instance.CheckHasUnValidGTISService(it.ContractId);
                        if (gtisCase == EnumContractGtisProductServiceState.Inexistence && it.State == (int)EnumContractServiceState.VALID && string.IsNullOrEmpty(it.SendAccountTime))
                        {
                            it.SalesServiceState = EnumSalesServiceState.PENDING;
                        }
                    }
                    it.IsReceiptName = it.IsReceipt == null ? "未到账" : Dictionary.IsReceipt.First(e => e.Value == it.IsReceipt.ToString()).Name;
                    //it.NearlyServiceEnd = SqlFunc.DateDiff(DateType.Day, dtDay, it.ServiceCycleEndDt) <= sD && it.SalesServiceState == EnumSalesServiceState.VALID;
                    if (it.ProductType == (int)EnumProductType.Gtis)
                    {
                        //判断能否申请服务  当前合同服务产品是否存在有效的服务申请？不可申请：可以申请
                        it.CouldApplyService = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.CheckCouldApplyServiceByContractId(it.ContractId);
                        //判断能否服务变更
                        if (it.CouldApplyService)//如果当前数据可以申请服务，则不能进行服务变更
                            it.CouldUpdateService = false;
                        else if (it.SalesServiceState != EnumSalesServiceState.VALID && it.SalesServiceState != EnumSalesServiceState.OVERDUE && it.SalesServiceState != EnumSalesServiceState.REFUSE)
                            it.CouldUpdateService = false;
                        else
                            it.CouldUpdateService = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.CheckCouldUpdateServiceByContractId(it.ContractId, it.ApplId);
                    }
                })
                .ToPageList(condition.PageNumber, condition.PageSize, ref total);
            return r;
        }

        /// <summary>
        /// 根据申请Id获取服务信息
        /// </summary>
        /// <param name="ApplId"></param>
        /// <returns></returns>
        public Db_crm_contract_serviceinfo_gtis GetGtisServiceByApplyId(string ApplId)
        {
            return Queryable
                    .Where(serv => serv.Deleted == false && serv.IsApplHistory == false && serv.IsChanged == 0)
                    .Where(serv => serv.ProductServiceInfoGtisApplId == ApplId)
                    .First();
        }

        /// <summary>
        /// 修改gtis绑定的环球搜服务Id
        /// </summary>
        /// <param name="GtisId"></param>
        /// <param name="GlobalSearchId"></param>
        public void UpdateGlobalSeachId(string GtisId, string GlobalSearchId)
        {
            Updateable
                .SetColumns(e => e.GlobalSearchSerivceId == GlobalSearchId)
                .SetColumns(e => e.UpdateDate == DateTime.Now)
                .SetColumns(e => e.UpdateUser == UserId)
                .Where(e => e.Id == GtisId)
                .ExecuteCommand();
        }

        /// <summary>
        /// 根据公司名称获取其对应的在服的Gtis服务的客户编码
        /// </summary>
        /// <param name="companyName"></param>
        /// <returns></returns>
        public List<string> GetValidGtisServiceByCompanyName(string companyName)
        {
            return Queryable
                .LeftJoin<Db_crm_contract>((e, f) => e.ContractId == f.Id && f.Deleted == false)
                .LeftJoin<Db_crm_customer_subcompany>((e, f, g) => f.FirstParty == g.Id && g.Deleted == 0)
                .Where(e => e.Deleted == false && e.IsApplHistory == false && e.IsChanged == 0 && e.State == (int)EnumContractServiceState.VALID)
                .Where((e, f) => f.ContractStatus == (int)EnumContractStatus.Pass || f.ContractStatus == (int)EnumContractStatus.AutoPass)
                .Where((e, f, g) => g.CompanyName.Contains(companyName))
                .Select(e => e.ContractNum)
                .ToList();
        }

        /// <summary>
        /// 根据Gtis账号获取其对应的在服的Gtis服务的客户编码
        /// </summary>
        /// <param name="accountNumber"></param>
        /// <returns></returns>
        public List<string> GetValidGtisServiceByAccountNumber(string accountNumber)
        {
            return Queryable
                .LeftJoin<Db_crm_contract_serviceinfo_gtis_user>((e, f) => e.Id == f.ContractServiceInfoGtisId)
                .Where(e => e.Deleted == false && e.IsApplHistory == false && e.IsChanged == 0 && e.State == (int)EnumContractServiceState.VALID)
                .Where((e, f) => f.AccountNumber == accountNumber)
                .Select(e => e.ContractNum)
                .ToList();
        }

        public Db_crm_contract_serviceinfo_gtis GetValidGtisServiceInfoByContractNum(string contractNum)
        {
            return Queryable
                .Where(e => e.ContractNum == contractNum)
                .Where(e => e.Deleted == false && e.IsApplHistory == false && e.IsChanged == 0)
                .Where(e => e.State == (int)EnumContractServiceState.VALID)
                .First();
        }

        /// <summary>
        /// 获取所有曾经开通过的Gtis服务数据
        /// </summary>
        /// <param name="contractNum"></param>
        /// <returns></returns>
        public Db_crm_contract_serviceinfo_gtis GetOnceOpendGtisServiceByContractNum(string contractNum)
        {
            return Queryable
                .Where(e => e.ContractNum == contractNum)
                .Where(e => e.Deleted == false && e.IsApplHistory == false && e.IsChanged == 0)
                .Where(e => e.State == (int)EnumContractServiceState.VALID || e.State == (int)EnumContractServiceState.OUT || e.State == (int)EnumContractServiceState.INVALID)
                .OrderByDescending(e => e.ReviewerDate)
                .First();
        }

        /// <summary>
        /// 根据Wits申请ID获取Gtis服务数据
        /// </summary>
        /// <param name="witsApplId"></param>
        public GetWitsApplyInfo4Audit_Out_RegisterInfo_Gtis GetServiceInfoByWitsApplyId(string witsApplId)
        {
            return Queryable
                .LeftJoin<Db_crm_product>((e,f) => e.ProductId == f.Id && f.Deleted == false)
                .Where(e => e.WitsApplId == witsApplId)
                .Where(e => e.IsApplHistory == false && e.IsChanged == 0 && e.Deleted == false)
                .Select((e,f) => new GetWitsApplyInfo4Audit_Out_RegisterInfo_Gtis
                {
                    Id = e.ProductServiceInfoGtisApplId,
                    ProducttId = e.ProductId,
                    SubAccountNum = e.SubAccountsNum.Value,
                    AuthorizationNum = e.AuthorizationNum.Value,
                    ForbidSearchExport = e.ForbidSearchExport == true,
                    WordRptPermissions = e.WordRptPermissions == true,
                    WordRptMaxTimes = e.WordRptMaxTimes.Value,
                    ServiceCycleStart = e.ServiceCycleStart.Value,
                    ServiceCycleEnd = e.ServiceCycleEnd.Value,
                    ServiceMonth = e.ServiceMonth.Value,
                    RetailCountry = e.RetailCountry.Value,
                    ProductName = f.ProductName,
                    ProductType = (EnumProductType)f.ProductType.Value

                })
                .First();
        }
    }
}